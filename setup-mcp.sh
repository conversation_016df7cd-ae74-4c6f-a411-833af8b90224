#!/bin/bash

# MCP Server Ecosystem Setup Script
# This script sets up the MCP server ecosystem with all required dependencies

set -e

echo "🚀 Setting up MCP Server Ecosystem..."
echo "=================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

echo "✅ Node.js $(node --version) detected"

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not available. Please install npm."
    exit 1
fi

echo "✅ npm $(npm --version) detected"

# Install MCP-specific dependencies
echo ""
echo "📦 Installing MCP dependencies..."
echo "================================"

# Core MCP dependencies
echo "Installing @modelcontextprotocol/sdk..."
npm install @modelcontextprotocol/sdk

echo "Installing validation and utility libraries..."
npm install zod ws uuid dotenv yaml fast-glob chokidar

# Development dependencies for TypeScript
echo "Installing TypeScript development dependencies..."
npm install --save-dev @types/ws @types/uuid

echo ""
echo "🔧 Building the project..."
echo "========================="

# Build TypeScript
npm run build

echo ""
echo "✅ MCP Server Ecosystem setup complete!"
echo "======================================="
echo ""
echo "🎯 Next steps:"
echo "1. Configure environment variables (see README-MCP.md)"
echo "2. List available servers: npm run mcp:start list"
echo "3. Start all servers: npm run mcp:start start"
echo "4. Or start a specific server: npm run mcp:start run \"Code Analysis\""
echo ""
echo "📚 Documentation:"
echo "- Main README: README-MCP.md"
echo "- Server documentation: src/mcp/servers/"
echo ""
echo "🔒 Security Note:"
echo "Set MCP_ALLOWED_PATHS environment variable to restrict file access"
echo "Example: export MCP_ALLOWED_PATHS=\"/safe/path1,/safe/path2\""
echo ""
echo "Happy coding! 🎉"
