# CyberSec Toolkit Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Legal Disclaimer](#legal-disclaimer)
3. [Installation](#installation)
4. [Tool Categories](#tool-categories)
   - [Network Scanning](#network-scanning)
   - [Password Analysis](#password-analysis)
   - [System Information](#system-information)
   - [Wireless Analysis](#wireless-analysis)
   - [Encryption/Decryption](#encryptiondecryption)
   - [Packet Analysis](#packet-analysis)
   - [Social Engineering Education](#social-engineering-education)
   - [AI-Powered Tools](#ai-powered-tools)
   - [Mobile Device Analyzer](#mobile-device-analyzer)
   - [Web Application Scanner](#web-application-scanner)
   - [Steganography Tools](#steganography-tools)
   - [Bluetooth Security Toolkit](#bluetooth-security-toolkit)
   - [IoT Security Scanner](#iot-security-scanner)
5. [Usage Examples](#usage-examples)
6. [Troubleshooting](#troubleshooting)
7. [Contributing](#contributing)

## Introduction

CyberSec Toolkit is a comprehensive collection of cybersecurity tools designed for educational purposes, security research, and authorized penetration testing. This toolkit provides various utilities for network analysis, system security assessment, wireless security testing, and security awareness training.

## Legal Disclaimer

**IMPORTANT: This toolkit is provided for EDUCATIONAL PURPOSES ONLY.**

The tools contained in this repository are designed to be used by:

- Cybersecurity professionals
- Network administrators
- Security researchers
- Students learning about cybersecurity

**You MUST:**

- Only use these tools on systems you own
- Only test systems for which you have explicit written permission
- Comply with all local, state, and federal laws
- Use these tools responsibly and ethically

Unauthorized use of these tools to access, probe, or attack systems you do not own or have permission to test is ILLEGAL and may result in criminal charges, civil liability, and severe legal penalties.

The author(s) of this toolkit assume no liability and are not responsible for any misuse or damage caused by this toolkit.

## Installation

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Steps

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/cybersec-toolkit.git
   ```

2. Navigate to the project directory:

   ```bash
   cd cybersec-toolkit
   ```

3. Install dependencies:

   ```bash
   npm install
   ```

4. Run the toolkit:
   ```bash
   node index.js
   ```

## Tool Categories

### Network Scanning

The Network Scanning module provides tools for:

- Pinging hosts to check connectivity
- Scanning networks for active hosts
- Port scanning to identify open services
- Retrieving local network information

**Usage:**

```bash
# From the main menu, select "Network Scanning"
# Then choose the specific tool you want to use
```

**Permissions Required:**

- Network access
- Administrator/root privileges for some scanning functions

### Password Analysis

The Password Analysis module provides tools for:

- Checking password strength
- Generating secure passwords
- Hashing passwords using various algorithms

**Usage:**

```bash
# From the main menu, select "Password Analysis"
# Then choose the specific tool you want to use
```

### System Information

The System Information module provides tools for:

- Gathering basic system information (CPU, memory, OS)
- Listing network connections
- Viewing running processes
- Listing installed software

**Usage:**

```bash
# From the main menu, select "System Information"
# Then choose the specific tool you want to use
```

**Permissions Required:**

- Administrator/root privileges for some system information functions

### Wireless Analysis

The Wireless Analysis module provides tools for:

- Scanning for wireless networks
- Getting details about the current wireless connection
- Analyzing wireless network security

**Usage:**

```bash
# From the main menu, select "Wireless Analysis"
# Then choose the specific tool you want to use
```

**Permissions Required:**

- Wireless adapter in monitor mode for some functions
- Administrator/root privileges

### Encryption/Decryption

The Encryption/Decryption module provides tools for:

- Encrypting and decrypting text using various algorithms
- Generating cryptographic keys
- Encrypting and decrypting files

**Usage:**

```bash
# From the main menu, select "Encryption/Decryption"
# Then choose the specific tool you want to use
```

### Packet Analysis

The Packet Analysis module provides tools for:

- Capturing network packets
- Analyzing packet content and statistics
- Saving packet captures for later analysis

**Usage:**

```bash
# From the main menu, select "Packet Analysis"
# Then choose the specific tool you want to use
```

**Permissions Required:**

- Administrator/root privileges
- Network adapter access

### Social Engineering Education

The Social Engineering Education module provides tools for:

- Generating phishing awareness templates
- Running a phishing awareness web server
- Creating social engineering assessment reports
- Learning about social engineering techniques

**Usage:**

```bash
# From the main menu, select "Social Engineering Education"
# Then choose the specific tool you want to use
```

**IMPORTANT:** These tools are for educational purposes only and should only be used in authorized security awareness training programs.

### AI-Powered Tools

The AI-Powered Tools module provides advanced cybersecurity capabilities using artificial intelligence:

- **AI Password Cracking Simulation**

  - Analyze password vulnerability to AI-based attacks
  - Simulate AI-powered password cracking techniques
  - Learn about AI-resistant password creation

- **AI Network Anomaly Detection**

  - Train AI on normal network traffic patterns
  - Detect unusual network behavior and potential threats
  - Generate detailed anomaly reports with recommendations

- **AI Phishing Email Generator & Detector**

  - Generate simulated phishing emails for awareness training
  - Analyze emails for phishing indicators using AI
  - Learn about advanced phishing techniques and defenses

- **AI Vulnerability Scanner**

  - Perform AI-enhanced vulnerability scanning of systems
  - Identify security weaknesses with higher accuracy
  - Generate comprehensive vulnerability reports

- **AI Malware Behavior Analyzer**
  - Analyze files for malicious behavior patterns
  - Identify potential malware families based on behavior
  - Generate detailed behavioral analysis reports

**Usage:**

```bash
# From the main menu, select "AI-Powered Tools"
# Then choose the specific AI tool you want to use
```

**IMPORTANT:** These AI tools are simulations for educational purposes only. They demonstrate how AI can be used in cybersecurity but do not implement actual AI algorithms.

### Mobile Device Analyzer

The Mobile Device Analyzer module provides tools for analyzing and testing the security of mobile devices:

- Analyze Android device security
- Check for security vulnerabilities on mobile devices
- Examine app permissions and security settings
- Generate security recommendations for mobile devices

**Usage:**

```bash
# From the main menu, select "Mobile Device Analyzer"
# Then choose the specific tool you want to use
```

**Permissions Required:**

- Android Debug Bridge (ADB) for Android device analysis
- Physical access to the device
- Developer mode enabled on Android devices

### Web Application Scanner

The Web Application Scanner module provides tools for identifying security vulnerabilities in web applications:

- Scan websites for common security issues
- Check for missing security headers
- Identify potential injection points
- Analyze form security and JavaScript vulnerabilities

**Usage:**

```bash
# From the main menu, select "Web Application Scanner"
# Then choose the specific tool you want to use
```

**IMPORTANT:** Only scan websites you own or have explicit permission to test.

### Steganography Tools

The Steganography Tools module provides capabilities for hiding and detecting hidden data in files:

- Hide text in images using LSB steganography
- Extract hidden text from images
- Hide files within other files
- Analyze files for potential hidden data

**Usage:**

```bash
# From the main menu, select "Steganography Tools"
# Then choose the specific tool you want to use
```

### Bluetooth Security Toolkit

The Bluetooth Security Toolkit module provides tools for analyzing Bluetooth device security:

- Scan for Bluetooth and BLE devices
- Analyze Bluetooth device security
- Test connectivity and identify vulnerabilities
- Generate security recommendations

**Usage:**

```bash
# From the main menu, select "Bluetooth Security Toolkit"
# Then choose the specific tool you want to use
```

**Permissions Required:**

- Bluetooth adapter
- Administrator/root privileges for some functions
- Linux OS for full functionality (simulation mode available on other platforms)

### IoT Security Scanner

The IoT Security Scanner module provides tools for analyzing the security of Internet of Things devices:

- Scan IoT devices for open ports and services
- Check for default credentials
- Analyze web interface security
- Generate comprehensive security reports

**Usage:**

```bash
# From the main menu, select "IoT Security Scanner"
# Then choose the specific tool you want to use
```

**IMPORTANT:** Only scan devices you own or have explicit permission to test.

## Usage Examples

### Example 1: Scanning a network for active hosts

1. Start the toolkit: `node index.js`
2. Select "Network Scanning" from the main menu
3. Choose "Scan network for active hosts"
4. Confirm that you have permission to scan the network
5. Review the results showing active hosts on the network

### Example 2: Checking password strength

1. Start the toolkit: `node index.js`
2. Select "Password Analysis" from the main menu
3. Choose "Check password strength"
4. Enter the password you want to analyze
5. Review the strength rating and recommendations

### Example 3: Generating a phishing awareness template

1. Start the toolkit: `node index.js`
2. Select "Social Engineering Education" from the main menu
3. Choose "Generate phishing awareness template"
4. Select the template type (email, SMS, website)
5. Enter the output file path
6. Confirm that you will only use it for authorized training
7. Use the generated template in your security awareness program

### Example 4: Using AI to analyze password vulnerability

1. Start the toolkit: `node index.js`
2. Select "AI-Powered Tools" from the main menu
3. Choose "AI Password Cracking Simulation"
4. Select "Analyze password vulnerability to AI"
5. Enter the password you want to analyze
6. Review the AI vulnerability analysis and recommendations

### Example 5: Analyzing Android device security

1. Start the toolkit: `node index.js`
2. Select "Mobile Device Analyzer" from the main menu
3. Choose "Analyze Android device"
4. Connect your Android device with USB debugging enabled
5. Review the security analysis and recommendations

### Example 6: Hiding text in an image

1. Start the toolkit: `node index.js`
2. Select "Steganography Tools" from the main menu
3. Choose "Hide text in image"
4. Enter the path to the carrier image
5. Enter the text to hide
6. Specify the output path
7. Use the generated steganographic image

## Troubleshooting

### Common Issues

1. **Permission errors when scanning:**

   - Ensure you're running the toolkit with administrator/root privileges
   - Check firewall settings that might be blocking the scans

2. **Missing dependencies:**

   - Run `npm install` to ensure all dependencies are installed
   - Some tools may require additional system packages

3. **Network adapter not found:**
   - Ensure your network adapter is properly configured
   - Some wireless functions require specific adapter capabilities

## Contributing

Contributions that improve the educational value of this toolkit are welcome. Please ensure all contributions adhere to ethical guidelines and include proper documentation.

1. Fork the repository
2. Create a feature branch: `git checkout -b new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin new-feature`
5. Submit a pull request

Please ensure your code follows the project's coding standards and includes appropriate documentation.
