# CyberSec Toolkit

A comprehensive collection of cybersecurity tools for educational purposes, security research, and authorized penetration testing.

## ⚠️ IMPORTANT LEGAL DISCLAIMER

This toolkit is provided for **EDUCATIONAL PURPOSES ONLY**. The tools contained in this repository are designed to be used by:

- Cybersecurity professionals
- Network administrators
- Security researchers
- Students learning about cybersecurity

**You MUST:**

- Only use these tools on systems you own
- Only test systems for which you have explicit written permission
- Comply with all local, state, and federal laws
- Use these tools responsibly and ethically

**Unauthorized use of these tools to access, probe, or attack systems you do not own or have permission to test is ILLEGAL and may result in:**

- Criminal charges
- Civil liability
- Severe legal penalties

The author(s) of this toolkit assume no liability and are not responsible for any misuse or damage caused by this toolkit.

## Overview

This toolkit contains various utilities for:

1. **Network Analysis**

   - Port scanning
   - Network mapping
   - Traffic analysis

2. **System Security**

   - Password strength analysis
   - Encryption/decryption utilities
   - System information gathering

3. **Wireless Security**

   - Wi-Fi network analysis
   - Bluetooth scanning

4. **Social Engineering Education**

   - Phishing simulation tools (for awareness training)
   - Security awareness materials

5. **AI-Powered Tools**

   - AI password cracking simulation
   - AI network anomaly detection
   - AI phishing email generator and detector
   - AI vulnerability scanner
   - AI malware behavior analyzer

6. **Mobile Device Security**

   - Android device security analysis
   - App permission analyzer
   - Security recommendations generator

7. **Web Application Security**

   - Web vulnerability scanner
   - Security header analyzer
   - Form and JavaScript security checker

8. **Steganography Tools**

   - Hide text in images
   - Extract hidden text from images
   - File-in-file hiding
   - Steganography detection

9. **Bluetooth Security**

   - Bluetooth device scanner
   - BLE security analyzer
   - Device security assessment

10. **IoT Security**
    - IoT device scanner
    - Default credential checker
    - Security vulnerability analyzer

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/cybersec-toolkit.git

# Navigate to the project directory
cd cybersec-toolkit

# Install dependencies
npm install
```

## Tools Overview

Each tool is documented in its respective directory with detailed usage instructions.

## Contributing

Contributions that improve the educational value of this toolkit are welcome. Please ensure all contributions adhere to ethical guidelines and include proper documentation.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
