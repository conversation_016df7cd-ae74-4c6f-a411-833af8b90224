{"name": "cybersec-toolkit", "version": "1.0.0", "description": "A comprehensive cybersecurity toolkit with MCP server ecosystem for educational purposes", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "test": "jest", "build": "tsc", "dev": "tsc --watch", "mcp:dev": "npm run build && node dist/mcp/index.js", "mcp:start": "node dist/mcp/index.js", "test:mcp": "jest --testPathPattern=mcp"}, "keywords": ["cybersecurity", "network", "security", "education", "penetration-testing", "mcp", "model-context-protocol", "ai-tools"], "author": "", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "commander": "^9.4.0", "inquirer": "^8.2.4", "node-nmap": "^4.0.0", "ora": "^5.4.1", "ping": "^0.4.2", "wifi-scanner": "^0.0.4", "node-ssh": "^13.0.0", "crypto-js": "^4.1.1", "express": "^4.18.1", "socket.io": "^4.5.2", "axios": "^0.27.2", "node-arp": "^1.0.6", "pcap": "^3.1.0", "systeminformation": "^5.12.6", "canvas": "^2.10.2", "cheerio": "^1.0.0-rc.12", "dns": "^0.2.2", "net": "^1.0.2", "bluetooth-serial-port": "^2.2.7", "@modelcontextprotocol/sdk": "^1.0.0", "zod": "^3.22.4", "ws": "^8.14.2", "uuid": "^9.0.1", "dotenv": "^16.3.1", "yaml": "^2.3.4", "fast-glob": "^3.3.2", "chokidar": "^3.5.3"}, "devDependencies": {"jest": "^29.0.3", "eslint": "^8.23.1", "@types/node": "^20.8.0", "@types/inquirer": "^9.0.3", "@types/chalk": "^2.2.0", "@types/crypto-js": "^4.1.2", "@types/ws": "^8.5.8", "@types/uuid": "^9.0.5", "typescript": "^5.2.2", "ts-jest": "^29.1.1", "nodemon": "^3.0.1"}}