{"name": "cybersec-toolkit", "version": "1.0.0", "description": "A collection of cybersecurity tools for educational purposes", "main": "index.js", "scripts": {"start": "node index.js", "test": "jest"}, "keywords": ["cybersecurity", "network", "security", "education", "penetration-testing"], "author": "", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "commander": "^9.4.0", "inquirer": "^8.2.4", "node-nmap": "^4.0.0", "ora": "^5.4.1", "ping": "^0.4.2", "wifi-scanner": "^0.0.4", "node-ssh": "^13.0.0", "crypto-js": "^4.1.1", "express": "^4.18.1", "socket.io": "^4.5.2", "axios": "^0.27.2", "node-arp": "^1.0.6", "pcap": "^3.1.0", "systeminformation": "^5.12.6", "canvas": "^2.10.2", "cheerio": "^1.0.0-rc.12", "dns": "^0.2.2", "net": "^1.0.2", "bluetooth-serial-port": "^2.2.7"}, "devDependencies": {"jest": "^29.0.3", "eslint": "^8.23.1"}}