{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowImportingTsExtensions": false, "noEmitOnError": false, "isolatedModules": true, "verbatimModuleSyntax": false}, "include": ["src/**/*", "mcp/**/*"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts", "dist"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}