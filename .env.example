# MCP Server Ecosystem Configuration
# Copy this file to .env and customize the values

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================

# Logging configuration
MCP_LOG_LEVEL=info
# Options: debug, info, warn, error

# Server port (if applicable for HTTP-based servers)
MCP_PORT=3000

# Maximum concurrent requests per server
MCP_MAX_CONCURRENT_REQUESTS=10

# Request timeout in milliseconds
MCP_REQUEST_TIMEOUT=30000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Enable authentication (recommended for production)
MCP_ENABLE_AUTH=false

# API key for authentication (generate a secure random key)
# MCP_API_KEY=your-secure-api-key-here

# Allowed file system paths (comma-separated)
# Restricts file operations to these directories
MCP_ALLOWED_PATHS=/tmp,/var/log,/home/<USER>/projects

# Enable rate limiting
MCP_ENABLE_RATE_LIMITING=true

# Rate limit: requests per minute per client
MCP_RATE_LIMIT_RPM=60

# =============================================================================
# FILE MANIPULATION SERVER
# =============================================================================

# Maximum file size for read operations (bytes)
FILE_MAX_SIZE=104857600
# Default: 100MB

# Enable file backup on write operations
FILE_ENABLE_BACKUP=true

# Backup directory
FILE_BACKUP_DIR=/tmp/mcp-backups

# =============================================================================
# SYSTEM MONITORING SERVER
# =============================================================================

# Default monitoring interval (seconds)
MONITOR_INTERVAL=60

# Enable system alerts
MONITOR_ENABLE_ALERTS=true

# Alert thresholds
MONITOR_CPU_THRESHOLD=80
MONITOR_MEMORY_THRESHOLD=85
MONITOR_DISK_THRESHOLD=90
MONITOR_TEMP_THRESHOLD=70

# =============================================================================
# VULNERABILITY SCANNING SERVER
# =============================================================================

# Enable vulnerability database updates
VULN_AUTO_UPDATE_DB=true

# Vulnerability database update interval (hours)
VULN_UPDATE_INTERVAL=24

# Default scan timeout (seconds)
VULN_SCAN_TIMEOUT=300

# Enable external vulnerability feeds
VULN_ENABLE_EXTERNAL_FEEDS=false

# External API keys (if using external vulnerability services)
# VULN_NVD_API_KEY=your-nvd-api-key
# VULN_SNYK_API_KEY=your-snyk-api-key

# =============================================================================
# CODE ANALYSIS SERVER
# =============================================================================

# Supported file extensions for analysis
CODE_SUPPORTED_EXTENSIONS=.js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.cs,.php,.rb,.go

# Maximum lines of code to analyze in one request
CODE_MAX_LINES=50000

# Enable dependency vulnerability scanning
CODE_ENABLE_DEPENDENCY_SCAN=true

# Code complexity threshold for warnings
CODE_COMPLEXITY_THRESHOLD=10

# =============================================================================
# GIT OPERATIONS SERVER
# =============================================================================

# Default Git command timeout (seconds)
GIT_COMMAND_TIMEOUT=30

# Maximum commits to retrieve in history
GIT_MAX_COMMITS=1000

# Enable Git hooks analysis
GIT_ANALYZE_HOOKS=true

# =============================================================================
# LOG ANALYSIS SERVER
# =============================================================================

# Maximum log file size to process (bytes)
LOG_MAX_FILE_SIZE=1073741824
# Default: 1GB

# Default log retention period (days)
LOG_RETENTION_DAYS=30

# Enable real-time log monitoring
LOG_ENABLE_REALTIME=true

# Log parsing timeout (seconds)
LOG_PARSE_TIMEOUT=120

# =============================================================================
# PROJECT MANAGEMENT SERVER
# =============================================================================

# Default project template
PROJECT_DEFAULT_TEMPLATE=software-development

# Enable time tracking
PROJECT_ENABLE_TIME_TRACKING=true

# Default hourly rate for cost calculations
PROJECT_DEFAULT_HOURLY_RATE=100

# Maximum team size per project
PROJECT_MAX_TEAM_SIZE=50

# =============================================================================
# DATA VALIDATION SERVER
# =============================================================================

# Maximum records to validate in one request
DATA_MAX_RECORDS=100000

# Enable automatic schema inference
DATA_AUTO_SCHEMA_INFERENCE=true

# Default validation strictness
DATA_DEFAULT_STRICTNESS=moderate
# Options: loose, moderate, strict

# Enable data profiling
DATA_ENABLE_PROFILING=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Enable caching
ENABLE_CACHING=true

# Cache TTL (seconds)
CACHE_TTL=3600

# Maximum cache size (MB)
CACHE_MAX_SIZE=256

# Enable compression for large responses
ENABLE_COMPRESSION=true

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Enable development mode (more verbose logging, etc.)
NODE_ENV=development

# Enable debug mode
DEBUG=false

# Enable hot reload for development
ENABLE_HOT_RELOAD=false

# =============================================================================
# INTEGRATION CONFIGURATION
# =============================================================================

# External service endpoints
# EXTERNAL_API_BASE_URL=https://api.example.com

# Webhook URLs for notifications
# WEBHOOK_ALERTS_URL=https://hooks.slack.com/your-webhook
# WEBHOOK_REPORTS_URL=https://hooks.discord.com/your-webhook

# Email configuration for notifications
# EMAIL_SMTP_HOST=smtp.gmail.com
# EMAIL_SMTP_PORT=587
# EMAIL_USERNAME=<EMAIL>
# EMAIL_PASSWORD=your-app-password

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Enable automatic backups
ENABLE_AUTO_BACKUP=false

# Backup interval (hours)
BACKUP_INTERVAL=24

# Backup retention period (days)
BACKUP_RETENTION=7

# Backup storage path
BACKUP_STORAGE_PATH=/var/backups/mcp

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Enable metrics collection
ENABLE_METRICS=true

# Metrics export format
METRICS_FORMAT=prometheus
# Options: prometheus, json, csv

# Health check interval (seconds)
HEALTH_CHECK_INTERVAL=30

# Enable distributed tracing
ENABLE_TRACING=false

# Tracing service endpoint
# TRACING_ENDPOINT=http://localhost:14268/api/traces
