/**
 * System Information Tool
 * 
 * This module provides system information gathering capabilities
 * for educational purposes.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const si = require('systeminformation');
const os = require('os');
const { exec } = require('child_process');

// Get basic system information
async function getBasicSystemInfo() {
  const spinner = ora('Gathering system information...').start();
  
  try {
    const [system, cpu, mem, osInfo, disk] = await Promise.all([
      si.system(),
      si.cpu(),
      si.mem(),
      si.osInfo(),
      si.diskLayout()
    ]);
    
    spinner.succeed('System information gathered');
    
    return {
      system,
      cpu,
      mem,
      osInfo,
      disk
    };
  } catch (error) {
    spinner.fail('Failed to gather system information');
    throw error;
  }
}

// Get network connections
async function getNetworkConnections() {
  const spinner = ora('Gathering network connections...').start();
  
  try {
    const connections = await si.networkConnections();
    spinner.succeed('Network connections gathered');
    return connections;
  } catch (error) {
    spinner.fail('Failed to gather network connections');
    throw error;
  }
}

// Get running processes
async function getRunningProcesses() {
  const spinner = ora('Gathering running processes...').start();
  
  try {
    const processes = await si.processes();
    spinner.succeed('Running processes gathered');
    return processes;
  } catch (error) {
    spinner.fail('Failed to gather running processes');
    throw error;
  }
}

// Get installed software
async function getInstalledSoftware() {
  const spinner = ora('Gathering installed software...').start();
  
  return new Promise((resolve, reject) => {
    if (process.platform === 'win32') {
      exec('wmic product get name,version', (error, stdout) => {
        if (error) {
          spinner.fail('Failed to gather installed software');
          reject(error);
          return;
        }
        
        const lines = stdout.split('\n').filter(line => line.trim() !== '');
        const software = [];
        
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line) {
            const parts = line.split('  ').filter(part => part.trim() !== '');
            if (parts.length >= 2) {
              software.push({
                name: parts[0].trim(),
                version: parts[1].trim()
              });
            }
          }
        }
        
        spinner.succeed('Installed software gathered');
        resolve(software);
      });
    } else if (process.platform === 'darwin') {
      exec('ls -la /Applications', (error, stdout) => {
        if (error) {
          spinner.fail('Failed to gather installed software');
          reject(error);
          return;
        }
        
        const lines = stdout.split('\n');
        const software = [];
        
        for (const line of lines) {
          if (line.includes('.app')) {
            const name = line.substring(line.lastIndexOf(' ') + 1);
            software.push({
              name: name,
              version: 'Unknown'
            });
          }
        }
        
        spinner.succeed('Installed software gathered');
        resolve(software);
      });
    } else {
      exec('dpkg-query -l', (error, stdout) => {
        if (error) {
          spinner.fail('Failed to gather installed software');
          reject(error);
          return;
        }
        
        const lines = stdout.split('\n');
        const software = [];
        
        for (let i = 5; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line) {
            const parts = line.split(/\s+/);
            if (parts.length >= 3) {
              software.push({
                name: parts[1],
                version: parts[2]
              });
            }
          }
        }
        
        spinner.succeed('Installed software gathered');
        resolve(software);
      });
    }
  });
}

// Display the menu for system information tools
async function menu() {
  console.log(chalk.cyan('\n=== System Information Tools ===\n'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a system information tool:',
      choices: [
        'Basic System Information',
        'Network Connections',
        'Running Processes',
        'Installed Software',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Basic System Information':
      try {
        const info = await getBasicSystemInfo();
        
        console.log(chalk.green('\nSystem Information:'));
        console.log(`Manufacturer: ${info.system.manufacturer}`);
        console.log(`Model: ${info.system.model}`);
        console.log(`Serial Number: ${info.system.serial}`);
        
        console.log(chalk.green('\nCPU Information:'));
        console.log(`Manufacturer: ${info.cpu.manufacturer}`);
        console.log(`Brand: ${info.cpu.brand}`);
        console.log(`Cores: ${info.cpu.cores}`);
        console.log(`Speed: ${info.cpu.speed} GHz`);
        
        console.log(chalk.green('\nMemory Information:'));
        console.log(`Total: ${(info.mem.total / (1024 * 1024 * 1024)).toFixed(2)} GB`);
        console.log(`Free: ${(info.mem.free / (1024 * 1024 * 1024)).toFixed(2)} GB`);
        console.log(`Used: ${(info.mem.used / (1024 * 1024 * 1024)).toFixed(2)} GB`);
        
        console.log(chalk.green('\nOperating System:'));
        console.log(`Platform: ${info.osInfo.platform}`);
        console.log(`Distro: ${info.osInfo.distro}`);
        console.log(`Release: ${info.osInfo.release}`);
        console.log(`Kernel: ${info.osInfo.kernel}`);
        console.log(`Arch: ${info.osInfo.arch}`);
        
        console.log(chalk.green('\nDisk Information:'));
        info.disk.forEach((disk, index) => {
          console.log(`\nDisk ${index + 1}:`);
          console.log(`  Device: ${disk.device}`);
          console.log(`  Type: ${disk.type}`);
          console.log(`  Size: ${(disk.size / (1024 * 1024 * 1024)).toFixed(2)} GB`);
          console.log(`  Vendor: ${disk.vendor}`);
        });
      } catch (error) {
        console.error(chalk.red('Error getting system information:'), error);
      }
      break;
      
    case 'Network Connections':
      try {
        const connections = await getNetworkConnections();
        
        console.log(chalk.green('\nNetwork Connections:'));
        console.log(chalk.yellow('PID\tLocal Address\t\tRemote Address\t\tState'));
        
        connections.forEach(conn => {
          console.log(`${conn.pid}\t${conn.localaddress}:${conn.localport}\t${conn.peeraddress}:${conn.peerport}\t${conn.state}`);
        });
        
        console.log(chalk.yellow(`\nTotal connections: ${connections.length}`));
      } catch (error) {
        console.error(chalk.red('Error getting network connections:'), error);
      }
      break;
      
    case 'Running Processes':
      try {
        const processes = await getRunningProcesses();
        
        console.log(chalk.green('\nRunning Processes:'));
        console.log(chalk.yellow('PID\tName\t\t\tCPU%\tMem%\tCommand'));
        
        processes.list.slice(0, 20).forEach(proc => {
          console.log(`${proc.pid}\t${proc.name.substring(0, 15).padEnd(15)}\t${proc.cpu.toFixed(1)}%\t${proc.mem.toFixed(1)}%\t${proc.command || ''}`);
        });
        
        console.log(chalk.yellow(`\nShowing 20/${processes.list.length} processes`));
        console.log(`Total processes: ${processes.all}`);
        console.log(`Running: ${processes.running}`);
        console.log(`Blocked: ${processes.blocked}`);
        console.log(`Sleeping: ${processes.sleeping}`);
      } catch (error) {
        console.error(chalk.red('Error getting running processes:'), error);
      }
      break;
      
    case 'Installed Software':
      try {
        const software = await getInstalledSoftware();
        
        console.log(chalk.green('\nInstalled Software:'));
        console.log(chalk.yellow('Name\t\t\t\tVersion'));
        
        software.slice(0, 20).forEach(app => {
          console.log(`${app.name.substring(0, 30).padEnd(30)}\t${app.version}`);
        });
        
        console.log(chalk.yellow(`\nShowing 20/${software.length} applications`));
      } catch (error) {
        console.error(chalk.red('Error getting installed software:'), error);
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another system information action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another system information action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  getBasicSystemInfo,
  getNetworkConnections,
  getRunningProcesses,
  getInstalledSoftware
};
