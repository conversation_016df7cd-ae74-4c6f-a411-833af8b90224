/**
 * Network Scanner Tool
 * 
 * This module provides network scanning capabilities for educational purposes.
 * ONLY use on networks you own or have explicit permission to scan.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const { exec } = require('child_process');
const nmap = require('node-nmap');
const ping = require('ping');
const os = require('os');

// Get local IP address
function getLocalIpAddress() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return '127.0.0.1';
}

// Ping a host to check if it's alive
async function pingHost(host) {
  const result = await ping.promise.probe(host);
  return {
    host: host,
    alive: result.alive,
    time: result.time,
    output: result.output
  };
}

// Scan network for active hosts
async function scanNetwork(subnet) {
  const spinner = ora('Scanning network...').start();
  
  return new Promise((resolve, reject) => {
    const quickscan = new nmap.QuickScan(subnet);
    
    quickscan.on('complete', function(data) {
      spinner.succeed('Network scan complete');
      resolve(data);
    });
    
    quickscan.on('error', function(error) {
      spinner.fail('Network scan failed');
      reject(error);
    });
    
    quickscan.startScan();
  });
}

// Port scan a specific host
async function portScan(host, portRange) {
  const spinner = ora(`Scanning ports on ${host}...`).start();
  
  return new Promise((resolve, reject) => {
    const portscan = new nmap.PortScanner(host, portRange);
    
    portscan.on('complete', function(data) {
      spinner.succeed(`Port scan of ${host} complete`);
      resolve(data);
    });
    
    portscan.on('error', function(error) {
      spinner.fail(`Port scan of ${host} failed`);
      reject(error);
    });
    
    portscan.startScan();
  });
}

// Display the menu for network scanning tools
async function menu() {
  console.log(chalk.cyan('\n=== Network Scanning Tools ===\n'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a network scanning tool:',
      choices: [
        'Ping a host',
        'Scan network for active hosts',
        'Port scan a host',
        'Get local network information',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Ping a host':
      const { host } = await inquirer.prompt([
        {
          type: 'input',
          name: 'host',
          message: 'Enter the host to ping:',
          default: '*******'
        }
      ]);
      
      try {
        const result = await pingHost(host);
        console.log(chalk.green('\nPing Results:'));
        console.log(`Host: ${result.host}`);
        console.log(`Status: ${result.alive ? 'Alive' : 'Unreachable'}`);
        if (result.alive) {
          console.log(`Response time: ${result.time} ms`);
        }
      } catch (error) {
        console.error(chalk.red('Error pinging host:'), error);
      }
      break;
      
    case 'Scan network for active hosts':
      const localIp = getLocalIpAddress();
      const subnet = `${localIp.split('.').slice(0, 3).join('.')}.1-254`;
      
      const { confirmScan } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmScan',
          message: `This will scan the subnet ${subnet}. Do you have permission to scan this network?`,
          default: false
        }
      ]);
      
      if (confirmScan) {
        try {
          const results = await scanNetwork(subnet);
          console.log(chalk.green('\nActive Hosts:'));
          results.forEach(host => {
            console.log(`${host.ip} - ${host.hostname || 'Unknown'}`);
          });
        } catch (error) {
          console.error(chalk.red('Error scanning network:'), error);
        }
      } else {
        console.log(chalk.yellow('Network scan cancelled.'));
      }
      break;
      
    case 'Port scan a host':
      const { targetHost, portRange } = await inquirer.prompt([
        {
          type: 'input',
          name: 'targetHost',
          message: 'Enter the host to scan:',
          default: getLocalIpAddress()
        },
        {
          type: 'input',
          name: 'portRange',
          message: 'Enter port range (e.g., 20-100):',
          default: '20-100'
        }
      ]);
      
      const { confirmPortScan } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmPortScan',
          message: `This will scan ports ${portRange} on ${targetHost}. Do you have permission to scan this host?`,
          default: false
        }
      ]);
      
      if (confirmPortScan) {
        try {
          const results = await portScan(targetHost, portRange);
          console.log(chalk.green('\nOpen Ports:'));
          if (results.length > 0 && results[0].openPorts) {
            results[0].openPorts.forEach(port => {
              console.log(`Port ${port.port} (${port.service})`);
            });
          } else {
            console.log('No open ports found in the specified range.');
          }
        } catch (error) {
          console.error(chalk.red('Error scanning ports:'), error);
        }
      } else {
        console.log(chalk.yellow('Port scan cancelled.'));
      }
      break;
      
    case 'Get local network information':
      console.log(chalk.green('\nLocal Network Information:'));
      const interfaces = os.networkInterfaces();
      Object.keys(interfaces).forEach(name => {
        console.log(`\nInterface: ${name}`);
        interfaces[name].forEach(iface => {
          console.log(`  Address: ${iface.address}`);
          console.log(`  Netmask: ${iface.netmask}`);
          console.log(`  Family: ${iface.family}`);
          console.log(`  MAC: ${iface.mac}`);
          console.log(`  Internal: ${iface.internal}`);
        });
      });
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another network scanning action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another network scanning action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  pingHost,
  scanNetwork,
  portScan,
  getLocalIpAddress
};
