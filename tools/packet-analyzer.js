/**
 * Packet Analyzer Tool
 * 
 * This module provides network packet analysis capabilities
 * for educational purposes. ONLY use on networks you own or
 * have explicit permission to analyze.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const { exec } = require('child_process');
const pcap = require('pcap');
const os = require('os');
const fs = require('fs');
const path = require('path');

// Get available network interfaces
function getNetworkInterfaces() {
  const interfaces = os.networkInterfaces();
  const result = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        result.push({
          name,
          address: iface.address,
          netmask: iface.netmask,
          mac: iface.mac
        });
      }
    }
  }
  
  return result;
}

// Capture packets on a specific interface
function capturePackets(interface, duration, filter, maxPackets) {
  return new Promise((resolve, reject) => {
    try {
      const pcapSession = pcap.createSession(interface, filter);
      const packets = [];
      let packetCount = 0;
      
      console.log(chalk.yellow(`\nStarting packet capture on ${interface}...`));
      console.log(chalk.yellow(`Press Ctrl+C to stop capture before the ${duration} second timeout.`));
      
      pcapSession.on('packet', (rawPacket) => {
        try {
          const packet = pcap.decode.packet(rawPacket);
          packets.push(packet);
          packetCount++;
          
          // Display basic info about the packet
          const ethernetLayer = packet.payload;
          if (ethernetLayer && ethernetLayer.payload) {
            const ipLayer = ethernetLayer.payload;
            if (ipLayer && ipLayer.payload) {
              const transportLayer = ipLayer.payload;
              
              let srcPort = 'N/A';
              let dstPort = 'N/A';
              let protocol = 'Unknown';
              
              if (transportLayer) {
                if (transportLayer.sport) srcPort = transportLayer.sport;
                if (transportLayer.dport) dstPort = transportLayer.dport;
                protocol = ipLayer.protocol || 'Unknown';
              }
              
              process.stdout.write(`\rCapturing: ${packetCount} packets | Last: ${ipLayer.saddr} -> ${ipLayer.daddr} | Protocol: ${protocol} | Ports: ${srcPort} -> ${dstPort}`);
            }
          }
          
          if (maxPackets && packetCount >= maxPackets) {
            pcapSession.close();
            resolve(packets);
          }
        } catch (err) {
          // Silently ignore packet decoding errors
        }
      });
      
      // Set timeout to stop capture after duration
      setTimeout(() => {
        pcapSession.close();
        console.log(chalk.green(`\n\nCapture complete. Captured ${packetCount} packets.`));
        resolve(packets);
      }, duration * 1000);
      
    } catch (error) {
      reject(error);
    }
  });
}

// Analyze captured packets
function analyzePackets(packets) {
  const analysis = {
    totalPackets: packets.length,
    protocols: {},
    ipAddresses: {},
    ports: {},
    packetSizes: {
      min: Number.MAX_SAFE_INTEGER,
      max: 0,
      avg: 0,
      total: 0
    }
  };
  
  packets.forEach(packet => {
    try {
      // Analyze packet size
      const packetSize = packet.pcap_header.len;
      analysis.packetSizes.total += packetSize;
      analysis.packetSizes.min = Math.min(analysis.packetSizes.min, packetSize);
      analysis.packetSizes.max = Math.max(analysis.packetSizes.max, packetSize);
      
      // Analyze Ethernet layer
      const ethernetLayer = packet.payload;
      if (!ethernetLayer) return;
      
      // Analyze IP layer
      const ipLayer = ethernetLayer.payload;
      if (!ipLayer) return;
      
      // Count protocols
      const protocol = ipLayer.protocol;
      if (protocol) {
        analysis.protocols[protocol] = (analysis.protocols[protocol] || 0) + 1;
      }
      
      // Count IP addresses
      if (ipLayer.saddr) {
        analysis.ipAddresses[ipLayer.saddr] = (analysis.ipAddresses[ipLayer.saddr] || 0) + 1;
      }
      if (ipLayer.daddr) {
        analysis.ipAddresses[ipLayer.daddr] = (analysis.ipAddresses[ipLayer.daddr] || 0) + 1;
      }
      
      // Analyze transport layer
      const transportLayer = ipLayer.payload;
      if (!transportLayer) return;
      
      // Count ports
      if (transportLayer.sport) {
        analysis.ports[transportLayer.sport] = (analysis.ports[transportLayer.sport] || 0) + 1;
      }
      if (transportLayer.dport) {
        analysis.ports[transportLayer.dport] = (analysis.ports[transportLayer.dport] || 0) + 1;
      }
    } catch (err) {
      // Silently ignore packet analysis errors
    }
  });
  
  // Calculate average packet size
  if (analysis.totalPackets > 0) {
    analysis.packetSizes.avg = Math.round(analysis.packetSizes.total / analysis.totalPackets);
  }
  
  // If no packets were successfully analyzed, set min to 0
  if (analysis.packetSizes.min === Number.MAX_SAFE_INTEGER) {
    analysis.packetSizes.min = 0;
  }
  
  return analysis;
}

// Save packet capture to file
function savePacketsToFile(packets, filePath) {
  try {
    const data = JSON.stringify(packets, null, 2);
    fs.writeFileSync(filePath, data);
    return true;
  } catch (error) {
    console.error(chalk.red('Error saving packets to file:'), error);
    return false;
  }
}

// Display the menu for packet analysis tools
async function menu() {
  console.log(chalk.cyan('\n=== Packet Analysis Tools ===\n'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a packet analysis tool:',
      choices: [
        'Capture and analyze packets',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Capture and analyze packets':
      const interfaces = getNetworkInterfaces();
      
      if (interfaces.length === 0) {
        console.log(chalk.yellow('No suitable network interfaces found.'));
        break;
      }
      
      const { selectedInterface, duration, filter, maxPackets, saveToFile, filePath } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedInterface',
          message: 'Select a network interface:',
          choices: interfaces.map(iface => ({
            name: `${iface.name} (${iface.address})`,
            value: iface.name
          }))
        },
        {
          type: 'number',
          name: 'duration',
          message: 'Capture duration in seconds:',
          default: 10,
          validate: value => value > 0 ? true : 'Duration must be greater than 0'
        },
        {
          type: 'input',
          name: 'filter',
          message: 'Packet filter (leave empty for all packets):',
          default: ''
        },
        {
          type: 'number',
          name: 'maxPackets',
          message: 'Maximum number of packets to capture (0 for unlimited):',
          default: 100
        },
        {
          type: 'confirm',
          name: 'saveToFile',
          message: 'Save captured packets to file?',
          default: false
        },
        {
          type: 'input',
          name: 'filePath',
          message: 'File path to save packets:',
          default: './packet_capture.json',
          when: answers => answers.saveToFile
        }
      ]);
      
      const { confirmCapture } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmCapture',
          message: `This will capture network packets on interface ${selectedInterface}. Do you have permission to perform this capture?`,
          default: false
        }
      ]);
      
      if (confirmCapture) {
        try {
          const packets = await capturePackets(
            selectedInterface,
            duration,
            filter,
            maxPackets > 0 ? maxPackets : null
          );
          
          if (packets.length === 0) {
            console.log(chalk.yellow('No packets captured.'));
            break;
          }
          
          console.log(chalk.green('\nAnalyzing captured packets...'));
          const analysis = analyzePackets(packets);
          
          console.log(chalk.green('\nPacket Analysis Results:'));
          console.log(`Total Packets: ${analysis.totalPackets}`);
          
          console.log(chalk.yellow('\nProtocol Distribution:'));
          Object.keys(analysis.protocols).forEach(protocol => {
            const count = analysis.protocols[protocol];
            const percentage = ((count / analysis.totalPackets) * 100).toFixed(2);
            console.log(`${protocol}: ${count} (${percentage}%)`);
          });
          
          console.log(chalk.yellow('\nTop IP Addresses:'));
          Object.entries(analysis.ipAddresses)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .forEach(([ip, count]) => {
              const percentage = ((count / analysis.totalPackets) * 100).toFixed(2);
              console.log(`${ip}: ${count} (${percentage}%)`);
            });
          
          console.log(chalk.yellow('\nTop Ports:'));
          Object.entries(analysis.ports)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .forEach(([port, count]) => {
              const percentage = ((count / analysis.totalPackets) * 100).toFixed(2);
              console.log(`Port ${port}: ${count} (${percentage}%)`);
            });
          
          console.log(chalk.yellow('\nPacket Size Statistics:'));
          console.log(`Minimum: ${analysis.packetSizes.min} bytes`);
          console.log(`Maximum: ${analysis.packetSizes.max} bytes`);
          console.log(`Average: ${analysis.packetSizes.avg} bytes`);
          
          if (saveToFile) {
            if (savePacketsToFile(packets, filePath)) {
              console.log(chalk.green(`\nPackets saved to ${filePath}`));
            } else {
              console.log(chalk.red(`\nFailed to save packets to ${filePath}`));
            }
          }
        } catch (error) {
          console.error(chalk.red('Error capturing packets:'), error);
        }
      } else {
        console.log(chalk.yellow('Packet capture cancelled.'));
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another packet analysis action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another packet analysis action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  getNetworkInterfaces,
  capturePackets,
  analyzePackets,
  savePacketsToFile
};
