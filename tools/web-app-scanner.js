/**
 * Web Application Scanner
 * 
 * This module provides tools for scanning web applications for common vulnerabilities
 * for educational purposes ONLY.
 * 
 * IMPORTANT: Only use on websites you own or have explicit permission to test.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const axios = require('axios');
const cheerio = require('cheerio');
const url = require('url');
const fs = require('fs');
const path = require('path');

// Common web vulnerabilities to check for
const vulnerabilityChecks = {
  // Headers and security configurations
  securityHeaders: [
    { name: 'X-XSS-Protection', description: 'Helps prevent XSS attacks in older browsers', severity: 'Medium' },
    { name: 'X-Content-Type-Options', description: 'Prevents MIME type sniffing', severity: 'Medium' },
    { name: 'X-Frame-Options', description: 'Prevents clickjacking attacks', severity: 'Medium' },
    { name: 'Content-Security-Policy', description: 'Prevents various attacks including XSS and data injection', severity: 'High' },
    { name: 'Strict-Transport-Security', description: 'Enforces HTTPS connections', severity: 'High' },
    { name: 'Referrer-Policy', description: 'Controls how much referrer information is included with requests', severity: 'Low' }
  ],
  
  // Common input fields that might be vulnerable
  sensitiveInputs: [
    { type: 'password', description: 'Password field might be vulnerable to interception if not using HTTPS', severity: 'High' },
    { type: 'text', name: /username|email|login/i, description: 'Login credentials might be vulnerable if not properly secured', severity: 'Medium' },
    { type: 'hidden', description: 'Hidden fields might contain sensitive data or be vulnerable to manipulation', severity: 'Medium' }
  ],
  
  // Common JavaScript vulnerabilities
  javascriptIssues: [
    { pattern: /document\.write\(/i, description: 'document.write() can enable XSS attacks', severity: 'Medium' },
    { pattern: /eval\(/i, description: 'eval() can execute arbitrary code and enable XSS attacks', severity: 'High' },
    { pattern: /innerHTML|outerHTML/i, description: 'innerHTML/outerHTML can enable XSS if user input is not sanitized', severity: 'Medium' },
    { pattern: /localStorage|sessionStorage/i, description: 'Client-side storage might contain sensitive data', severity: 'Low' }
  ],
  
  // Form vulnerabilities
  formIssues: [
    { check: 'method', value: 'get', description: 'Form uses GET method which exposes data in URL', severity: 'Medium' },
    { check: 'autocomplete', value: 'on', description: 'Form has autocomplete enabled which might store sensitive data', severity: 'Low' },
    { check: 'action', value: /^http:/i, description: 'Form submits data over unencrypted HTTP', severity: 'High' }
  ]
};

// Validate URL format
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// Ensure URL has protocol
function ensureProtocol(urlString) {
  if (!urlString.startsWith('http://') && !urlString.startsWith('https://')) {
    return 'https://' + urlString;
  }
  return urlString;
}

// Scan a website for basic information
async function scanWebsite(targetUrl) {
  const spinner = ora(`Scanning ${targetUrl}...`).start();
  
  try {
    // Make request to the target URL
    const response = await axios.get(targetUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      },
      timeout: 10000,
      maxRedirects: 5
    });
    
    spinner.succeed(`Scan of ${targetUrl} complete`);
    
    // Parse the HTML content
    const $ = cheerio.load(response.data);
    
    // Extract basic information
    const title = $('title').text().trim();
    const metaDescription = $('meta[name="description"]').attr('content') || 'Not found';
    const links = $('a').map((i, el) => $(el).attr('href')).get();
    const forms = $('form').length;
    const scripts = $('script').length;
    const inputFields = $('input').length;
    
    // Extract server information from headers
    const server = response.headers['server'] || 'Not disclosed';
    const poweredBy = response.headers['x-powered-by'] || 'Not disclosed';
    
    // Check if site is using HTTPS
    const isHttps = targetUrl.startsWith('https://');
    
    return {
      url: targetUrl,
      title,
      metaDescription,
      server,
      poweredBy,
      isHttps,
      statusCode: response.status,
      contentType: response.headers['content-type'],
      links: links.length,
      uniqueDomains: getUniqueDomains(links, targetUrl),
      forms,
      scripts,
      inputFields,
      headers: response.headers,
      html: response.data
    };
  } catch (error) {
    spinner.fail(`Failed to scan ${targetUrl}`);
    throw error;
  }
}

// Get unique domains from links
function getUniqueDomains(links, baseUrl) {
  const baseHostname = new URL(baseUrl).hostname;
  const domains = new Set();
  
  links.forEach(link => {
    try {
      // Handle relative URLs
      const absoluteUrl = new URL(link, baseUrl);
      const hostname = absoluteUrl.hostname;
      
      if (hostname && hostname !== baseHostname) {
        domains.add(hostname);
      }
    } catch (e) {
      // Ignore invalid URLs
    }
  });
  
  return Array.from(domains);
}

// Check for common security headers
function checkSecurityHeaders(headers) {
  const results = [];
  
  vulnerabilityChecks.securityHeaders.forEach(header => {
    const headerName = header.name.toLowerCase();
    const hasHeader = Object.keys(headers).some(h => h.toLowerCase() === headerName);
    
    results.push({
      name: header.name,
      present: hasHeader,
      value: hasHeader ? headers[Object.keys(headers).find(h => h.toLowerCase() === headerName)] : null,
      description: header.description,
      severity: header.severity
    });
  });
  
  return results;
}

// Check for form vulnerabilities
function checkFormVulnerabilities($) {
  const results = [];
  
  $('form').each((i, form) => {
    const formElement = $(form);
    const formAction = formElement.attr('action') || '';
    const formMethod = (formElement.attr('method') || 'get').toLowerCase();
    const formAutocomplete = formElement.attr('autocomplete');
    const formId = formElement.attr('id') || `form-${i+1}`;
    
    // Check form method
    if (formMethod === 'get') {
      results.push({
        form: formId,
        issue: 'Uses GET method',
        description: 'Form uses GET method which exposes data in URL',
        severity: 'Medium'
      });
    }
    
    // Check form autocomplete
    if (formAutocomplete !== 'off') {
      results.push({
        form: formId,
        issue: 'Autocomplete not disabled',
        description: 'Form has autocomplete enabled which might store sensitive data',
        severity: 'Low'
      });
    }
    
    // Check if form submits over HTTP
    if (formAction.startsWith('http:')) {
      results.push({
        form: formId,
        issue: 'Submits over HTTP',
        description: 'Form submits data over unencrypted HTTP',
        severity: 'High'
      });
    }
    
    // Check for CSRF protection
    const hasCSRFToken = formElement.find('input[name*=csrf], input[name*=token], input[name*=nonce]').length > 0;
    if (!hasCSRFToken) {
      results.push({
        form: formId,
        issue: 'No CSRF protection',
        description: 'Form appears to lack CSRF protection tokens',
        severity: 'High'
      });
    }
    
    // Check for sensitive inputs
    formElement.find('input').each((j, input) => {
      const inputElement = $(input);
      const inputType = inputElement.attr('type') || 'text';
      const inputName = inputElement.attr('name') || '';
      
      // Check password fields
      if (inputType === 'password') {
        results.push({
          form: formId,
          issue: 'Password field',
          description: 'Password field might be vulnerable to interception if not using HTTPS',
          severity: 'High'
        });
      }
      
      // Check for sensitive input names
      if (inputType === 'text' && /username|email|login|account/i.test(inputName)) {
        results.push({
          form: formId,
          issue: 'Credential input',
          description: 'Login credential field might be vulnerable if not properly secured',
          severity: 'Medium'
        });
      }
    });
  });
  
  return results;
}

// Check for JavaScript vulnerabilities
function checkJavaScriptVulnerabilities($) {
  const results = [];
  
  // Extract all script content
  const scriptContents = [];
  $('script').each((i, script) => {
    const content = $(script).html();
    if (content) {
      scriptContents.push(content);
    }
  });
  
  // Check for inline event handlers
  $('[onclick], [onload], [onmouseover], [onmouseout], [onerror]').each((i, el) => {
    results.push({
      issue: 'Inline event handler',
      description: 'Inline event handlers can be vulnerable to XSS attacks',
      severity: 'Medium',
      element: $(el).prop('tagName')
    });
  });
  
  // Check script contents for vulnerable patterns
  scriptContents.forEach((content, i) => {
    vulnerabilityChecks.javascriptIssues.forEach(issue => {
      if (issue.pattern.test(content)) {
        results.push({
          issue: `Potentially unsafe JavaScript: ${issue.pattern.toString().replace(/^\/|\/i$/g, '')}`,
          description: issue.description,
          severity: issue.severity,
          scriptIndex: i + 1
        });
      }
    });
  });
  
  return results;
}

// Check for potential injection points
function checkInjectionPoints($) {
  const results = [];
  
  // Check URL parameters
  const urlParams = $('a[href*="?"]').length;
  if (urlParams > 0) {
    results.push({
      issue: 'URL parameters',
      description: `Found ${urlParams} links with URL parameters, potential SQL injection or XSS points`,
      severity: 'Medium'
    });
  }
  
  // Check input fields without validation attributes
  const inputsWithoutValidation = $('input[type="text"]:not([pattern]):not([maxlength])').length;
  if (inputsWithoutValidation > 0) {
    results.push({
      issue: 'Inputs without validation',
      description: `Found ${inputsWithoutValidation} text inputs without client-side validation`,
      severity: 'Medium'
    });
  }
  
  // Check for search forms (common injection points)
  const searchForms = $('form:has(input[name*=search]), form[action*=search]').length;
  if (searchForms > 0) {
    results.push({
      issue: 'Search functionality',
      description: 'Search forms are common SQL injection and XSS targets',
      severity: 'Medium'
    });
  }
  
  return results;
}

// Generate a comprehensive vulnerability report
function generateVulnerabilityReport(scanResults, securityHeaders, formVulnerabilities, jsVulnerabilities, injectionPoints) {
  // Count issues by severity
  const severityCounts = {
    High: 0,
    Medium: 0,
    Low: 0
  };
  
  // Count security headers
  const missingHeaders = securityHeaders.filter(h => !h.present);
  missingHeaders.forEach(h => {
    severityCounts[h.severity]++;
  });
  
  // Count form vulnerabilities
  formVulnerabilities.forEach(v => {
    severityCounts[v.severity]++;
  });
  
  // Count JS vulnerabilities
  jsVulnerabilities.forEach(v => {
    severityCounts[v.severity]++;
  });
  
  // Count injection points
  injectionPoints.forEach(v => {
    severityCounts[v.severity]++;
  });
  
  // Calculate risk score (0-100)
  const riskScore = Math.min(100, (severityCounts.High * 10) + (severityCounts.Medium * 5) + (severityCounts.Low * 2));
  
  // Determine overall risk level
  let riskLevel;
  if (riskScore >= 70) {
    riskLevel = 'High';
  } else if (riskScore >= 40) {
    riskLevel = 'Medium';
  } else if (riskScore >= 10) {
    riskLevel = 'Low';
  } else {
    riskLevel = 'Minimal';
  }
  
  // Generate report
  let report = `# Web Application Security Scan Report
Generated: ${new Date().toLocaleString()}

## Target Information
- URL: ${scanResults.url}
- Title: ${scanResults.title}
- Server: ${scanResults.server}
- Powered By: ${scanResults.poweredBy}
- HTTPS: ${scanResults.isHttps ? 'Yes' : 'No'}

## Risk Assessment
- Risk Level: ${riskLevel}
- Risk Score: ${riskScore}/100
- High Severity Issues: ${severityCounts.High}
- Medium Severity Issues: ${severityCounts.Medium}
- Low Severity Issues: ${severityCounts.Low}

## Security Headers
`;

  securityHeaders.forEach(header => {
    report += `- ${header.name}: ${header.present ? 'Present' : chalk.red('Missing')} ${header.present ? '' : `(${header.severity} Risk)`}\n`;
    if (header.present) {
      report += `  Value: ${header.value}\n`;
    } else {
      report += `  Description: ${header.description}\n`;
    }
  });

  report += `
## Form Vulnerabilities
`;

  if (formVulnerabilities.length === 0) {
    report += 'No form vulnerabilities detected.\n';
  } else {
    formVulnerabilities.forEach(vuln => {
      report += `- Form: ${vuln.form}\n`;
      report += `  Issue: ${vuln.issue} (${vuln.severity} Risk)\n`;
      report += `  Description: ${vuln.description}\n`;
    });
  }

  report += `
## JavaScript Vulnerabilities
`;

  if (jsVulnerabilities.length === 0) {
    report += 'No JavaScript vulnerabilities detected.\n';
  } else {
    jsVulnerabilities.forEach(vuln => {
      report += `- Issue: ${vuln.issue} (${vuln.severity} Risk)\n`;
      report += `  Description: ${vuln.description}\n`;
    });
  }

  report += `
## Potential Injection Points
`;

  if (injectionPoints.length === 0) {
    report += 'No potential injection points detected.\n';
  } else {
    injectionPoints.forEach(point => {
      report += `- Issue: ${point.issue} (${point.severity} Risk)\n`;
      report += `  Description: ${point.description}\n`;
    });
  }

  report += `
## Recommendations
`;

  // Add recommendations based on findings
  if (!scanResults.isHttps) {
    report += '- Implement HTTPS to encrypt all traffic\n';
  }
  
  missingHeaders.forEach(header => {
    report += `- Add the ${header.name} header to improve security\n`;
  });
  
  if (formVulnerabilities.some(v => v.issue === 'No CSRF protection')) {
    report += '- Implement CSRF tokens in all forms\n';
  }
  
  if (formVulnerabilities.some(v => v.issue === 'Submits over HTTP')) {
    report += '- Ensure all forms submit over HTTPS\n';
  }
  
  if (jsVulnerabilities.length > 0) {
    report += '- Review JavaScript code for unsafe patterns\n';
    report += '- Implement Content Security Policy (CSP) to mitigate XSS risks\n';
  }
  
  if (injectionPoints.length > 0) {
    report += '- Implement proper input validation and sanitization\n';
    report += '- Use parameterized queries for database operations\n';
    report += '- Consider using a Web Application Firewall (WAF)\n';
  }
  
  report += `
## Disclaimer
This scan is for educational purposes only and may not detect all vulnerabilities.
A comprehensive security assessment should include manual testing by security professionals.
`;

  return report;
}

// Display the menu for web application scanning
async function menu() {
  console.log(chalk.cyan('\n=== Web Application Scanner ===\n'));
  console.log(chalk.yellow('This tool scans web applications for common security vulnerabilities.'));
  console.log(chalk.red('IMPORTANT: Only scan websites you own or have explicit permission to test.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a web scanning action:',
      choices: [
        'Scan website for vulnerabilities',
        'Back to main menu'
      ]
    }
  ]);
  
  if (action === 'Back to main menu') {
    return;
  }
  
  // Get target URL
  const { targetUrl } = await inquirer.prompt([
    {
      type: 'input',
      name: 'targetUrl',
      message: 'Enter the URL to scan:',
      validate: value => {
        if (!value) return 'Please enter a URL';
        const urlWithProtocol = ensureProtocol(value);
        if (!isValidUrl(urlWithProtocol)) return 'Please enter a valid URL';
        return true;
      },
      filter: value => ensureProtocol(value)
    }
  ]);
  
  // Confirm scan
  const { confirmScan } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirmScan',
      message: `This will scan ${targetUrl} for vulnerabilities. Do you have permission to scan this website?`,
      default: false
    }
  ]);
  
  if (!confirmScan) {
    console.log(chalk.yellow('Scan cancelled.'));
    return;
  }
  
  try {
    // Perform the scan
    const scanResults = await scanWebsite(targetUrl);
    
    console.log(chalk.green('\nBasic Information:'));
    console.log(`URL: ${scanResults.url}`);
    console.log(`Title: ${scanResults.title}`);
    console.log(`Server: ${scanResults.server}`);
    console.log(`Powered By: ${scanResults.poweredBy}`);
    console.log(`HTTPS: ${scanResults.isHttps ? chalk.green('Yes') : chalk.red('No')}`);
    console.log(`Status Code: ${scanResults.statusCode}`);
    console.log(`Content Type: ${scanResults.contentType}`);
    
    console.log(chalk.green('\nContent Overview:'));
    console.log(`Links: ${scanResults.links}`);
    console.log(`External Domains: ${scanResults.uniqueDomains.length}`);
    console.log(`Forms: ${scanResults.forms}`);
    console.log(`Scripts: ${scanResults.scripts}`);
    console.log(`Input Fields: ${scanResults.inputFields}`);
    
    // Load HTML for further analysis
    const $ = cheerio.load(scanResults.html);
    
    // Check security headers
    console.log(chalk.yellow('\nSecurity Headers:'));
    const securityHeaders = checkSecurityHeaders(scanResults.headers);
    securityHeaders.forEach(header => {
      console.log(`${header.name}: ${header.present ? chalk.green('Present') : chalk.red('Missing')}`);
    });
    
    // Check form vulnerabilities
    console.log(chalk.yellow('\nForm Security:'));
    const formVulnerabilities = checkFormVulnerabilities($);
    if (formVulnerabilities.length === 0) {
      console.log(chalk.green('No form vulnerabilities detected.'));
    } else {
      formVulnerabilities.forEach(vuln => {
        console.log(chalk.red(`- ${vuln.issue} (${vuln.severity} Risk): ${vuln.description}`));
      });
    }
    
    // Check JavaScript vulnerabilities
    console.log(chalk.yellow('\nJavaScript Security:'));
    const jsVulnerabilities = checkJavaScriptVulnerabilities($);
    if (jsVulnerabilities.length === 0) {
      console.log(chalk.green('No JavaScript vulnerabilities detected.'));
    } else {
      jsVulnerabilities.forEach(vuln => {
        console.log(chalk.red(`- ${vuln.issue} (${vuln.severity} Risk): ${vuln.description}`));
      });
    }
    
    // Check for potential injection points
    console.log(chalk.yellow('\nPotential Injection Points:'));
    const injectionPoints = checkInjectionPoints($);
    if (injectionPoints.length === 0) {
      console.log(chalk.green('No obvious injection points detected.'));
    } else {
      injectionPoints.forEach(point => {
        console.log(chalk.red(`- ${point.issue} (${point.severity} Risk): ${point.description}`));
      });
    }
    
    // Generate and save report
    const { saveReport } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'saveReport',
        message: 'Would you like to save a detailed vulnerability report?',
        default: true
      }
    ]);
    
    if (saveReport) {
      const report = generateVulnerabilityReport(
        scanResults,
        securityHeaders,
        formVulnerabilities,
        jsVulnerabilities,
        injectionPoints
      );
      
      const reportPath = './web_vulnerability_report.md';
      fs.writeFileSync(reportPath, report);
      console.log(chalk.green(`\nDetailed report saved to ${reportPath}`));
    }
    
  } catch (error) {
    console.error(chalk.red('Error scanning website:'), error.message);
  }
  
  // Ask if the user wants to scan another website
  const { anotherScan } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherScan',
      message: 'Would you like to scan another website?',
      default: true
    }
  ]);
  
  if (anotherScan) {
    await menu();
  }
}

module.exports = {
  menu,
  scanWebsite,
  checkSecurityHeaders,
  checkFormVulnerabilities,
  checkJavaScriptVulnerabilities,
  checkInjectionPoints,
  generateVulnerabilityReport
};
