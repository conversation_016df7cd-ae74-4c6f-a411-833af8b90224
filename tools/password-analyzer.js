/**
 * Password Analyzer Tool
 * 
 * This module provides password strength analysis and generation capabilities
 * for educational purposes.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const ora = require('ora');

// Check password strength
function checkPasswordStrength(password) {
  let score = 0;
  let feedback = [];
  
  // Length check
  if (password.length < 8) {
    feedback.push('Password is too short (minimum 8 characters)');
  } else {
    score += Math.min(2, Math.floor(password.length / 8));
  }
  
  // Complexity checks
  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('Add lowercase letters');
  
  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('Add uppercase letters');
  
  if (/[0-9]/.test(password)) score += 1;
  else feedback.push('Add numbers');
  
  if (/[^a-zA-Z0-9]/.test(password)) score += 1;
  else feedback.push('Add special characters');
  
  // Repetition check
  if (/(.)\1{2,}/.test(password)) {
    score -= 1;
    feedback.push('Avoid repeated characters');
  }
  
  // Sequential characters check
  const sequences = ['abcdefghijklmnopqrstuvwxyz', '01234567890', 'qwertyuiop', 'asdfghjkl', 'zxcvbnm'];
  for (const seq of sequences) {
    for (let i = 0; i < seq.length - 2; i++) {
      const fragment = seq.substring(i, i + 3);
      if (password.toLowerCase().includes(fragment)) {
        score -= 1;
        feedback.push('Avoid sequential characters');
        break;
      }
    }
  }
  
  // Common password check (simplified)
  const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'welcome', 'letmein'];
  if (commonPasswords.includes(password.toLowerCase())) {
    score = 0;
    feedback.push('This is a commonly used password');
  }
  
  // Calculate final score (0-5)
  score = Math.max(0, Math.min(5, score));
  
  // Map score to strength description
  let strength;
  switch (score) {
    case 0:
      strength = 'Very Weak';
      break;
    case 1:
      strength = 'Weak';
      break;
    case 2:
      strength = 'Moderate';
      break;
    case 3:
      strength = 'Good';
      break;
    case 4:
      strength = 'Strong';
      break;
    case 5:
      strength = 'Very Strong';
      break;
  }
  
  return {
    score,
    strength,
    feedback: feedback.length > 0 ? feedback : ['No specific suggestions']
  };
}

// Generate a secure random password
function generatePassword(options) {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  let chars = '';
  if (options.includeLowercase) chars += lowercase;
  if (options.includeUppercase) chars += uppercase;
  if (options.includeNumbers) chars += numbers;
  if (options.includeSpecial) chars += special;
  
  if (chars.length === 0) {
    chars = lowercase + uppercase + numbers;
  }
  
  let password = '';
  const randomBytes = crypto.randomBytes(options.length);
  
  for (let i = 0; i < options.length; i++) {
    const randomIndex = randomBytes[i] % chars.length;
    password += chars[randomIndex];
  }
  
  return password;
}

// Hash a password using various algorithms
function hashPassword(password, algorithm) {
  switch (algorithm) {
    case 'md5':
      return crypto.createHash('md5').update(password).digest('hex');
    case 'sha1':
      return crypto.createHash('sha1').update(password).digest('hex');
    case 'sha256':
      return crypto.createHash('sha256').update(password).digest('hex');
    case 'sha512':
      return crypto.createHash('sha512').update(password).digest('hex');
    case 'bcrypt':
      // Note: In a real implementation, you would use a proper bcrypt library
      return 'Bcrypt requires a specialized library';
    default:
      return 'Unknown algorithm';
  }
}

// Display the menu for password analysis tools
async function menu() {
  console.log(chalk.cyan('\n=== Password Analysis Tools ===\n'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a password tool:',
      choices: [
        'Check password strength',
        'Generate secure password',
        'Hash a password',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Check password strength':
      const { password } = await inquirer.prompt([
        {
          type: 'password',
          name: 'password',
          message: 'Enter a password to check:',
          mask: '*'
        }
      ]);
      
      const result = checkPasswordStrength(password);
      
      console.log(chalk.green('\nPassword Strength Analysis:'));
      console.log(`Strength: ${getStrengthColor(result.strength)(result.strength)}`);
      console.log(`Score: ${result.score}/5`);
      console.log(chalk.yellow('\nFeedback:'));
      result.feedback.forEach(item => console.log(`- ${item}`));
      break;
      
    case 'Generate secure password':
      const options = await inquirer.prompt([
        {
          type: 'number',
          name: 'length',
          message: 'Password length:',
          default: 16,
          validate: value => value >= 8 ? true : 'Password should be at least 8 characters'
        },
        {
          type: 'confirm',
          name: 'includeLowercase',
          message: 'Include lowercase letters?',
          default: true
        },
        {
          type: 'confirm',
          name: 'includeUppercase',
          message: 'Include uppercase letters?',
          default: true
        },
        {
          type: 'confirm',
          name: 'includeNumbers',
          message: 'Include numbers?',
          default: true
        },
        {
          type: 'confirm',
          name: 'includeSpecial',
          message: 'Include special characters?',
          default: true
        }
      ]);
      
      const generatedPassword = generatePassword(options);
      const strengthResult = checkPasswordStrength(generatedPassword);
      
      console.log(chalk.green('\nGenerated Password:'));
      console.log(generatedPassword);
      console.log(`\nStrength: ${getStrengthColor(strengthResult.strength)(strengthResult.strength)}`);
      break;
      
    case 'Hash a password':
      const { passwordToHash, algorithm } = await inquirer.prompt([
        {
          type: 'password',
          name: 'passwordToHash',
          message: 'Enter a password to hash:',
          mask: '*'
        },
        {
          type: 'list',
          name: 'algorithm',
          message: 'Select hashing algorithm:',
          choices: ['md5', 'sha1', 'sha256', 'sha512', 'bcrypt']
        }
      ]);
      
      const hashedPassword = hashPassword(passwordToHash, algorithm);
      
      console.log(chalk.green('\nHashed Password:'));
      console.log(`Algorithm: ${algorithm}`);
      console.log(`Hash: ${hashedPassword}`);
      
      if (algorithm === 'md5' || algorithm === 'sha1') {
        console.log(chalk.red('\nWarning: This algorithm is considered insecure for password storage.'));
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another password analysis action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another password analysis action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

// Helper function to get color based on password strength
function getStrengthColor(strength) {
  switch (strength) {
    case 'Very Weak':
      return chalk.red;
    case 'Weak':
      return chalk.red;
    case 'Moderate':
      return chalk.yellow;
    case 'Good':
      return chalk.green;
    case 'Strong':
      return chalk.green.bold;
    case 'Very Strong':
      return chalk.green.bold;
    default:
      return chalk.white;
  }
}

module.exports = {
  menu,
  checkPasswordStrength,
  generatePassword,
  hashPassword
};
