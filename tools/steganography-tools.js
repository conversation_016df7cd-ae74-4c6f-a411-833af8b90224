/**
 * Steganography Tools
 * 
 * This module provides tools for hiding and detecting hidden data in various file types
 * for educational purposes ONLY.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { exec } = require('child_process');
const { createCanvas, loadImage } = require('canvas');

// Check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Get file extension
function getFileExtension(filePath) {
  return path.extname(filePath).toLowerCase();
}

// Hide text in an image using LSB (Least Significant Bit) steganography
async function hideTextInImage(imagePath, outputPath, text, password = '') {
  const spinner = ora('Hiding text in image...').start();
  
  try {
    // Load the image
    const image = await loadImage(fs.readFileSync(imagePath));
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    
    // Encrypt text if password is provided
    let dataToHide = text;
    if (password) {
      const cipher = crypto.createCipher('aes-256-ctr', password);
      dataToHide = cipher.update(text, 'utf8', 'hex') + cipher.final('hex');
    }
    
    // Convert text to binary
    const binaryData = Array.from(dataToHide).map(char => 
      char.charCodeAt(0).toString(2).padStart(8, '0')
    ).join('');
    
    // Add length prefix to binary data (32 bits for length)
    const lengthPrefix = binaryData.length.toString(2).padStart(32, '0');
    const fullBinaryData = lengthPrefix + binaryData;
    
    // Check if image has enough pixels to store the data
    if (fullBinaryData.length > pixels.length / 4 * 3) {
      spinner.fail('Image is too small to hide this amount of text');
      throw new Error('Image is too small to hide this amount of text');
    }
    
    // Embed data in LSB of each color channel (R,G,B)
    let bitIndex = 0;
    for (let i = 0; i < pixels.length; i += 4) {
      // Skip alpha channel (i+3)
      for (let j = 0; j < 3; j++) {
        if (bitIndex < fullBinaryData.length) {
          // Clear the LSB
          pixels[i + j] &= 0xFE;
          // Set the LSB according to our data
          pixels[i + j] |= parseInt(fullBinaryData[bitIndex]);
          bitIndex++;
        } else {
          break;
        }
      }
      if (bitIndex >= fullBinaryData.length) {
        break;
      }
    }
    
    // Put the modified pixel data back
    ctx.putImageData(imageData, 0, 0);
    
    // Save the image
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(outputPath, buffer);
    
    spinner.succeed('Text hidden in image successfully');
    return true;
  } catch (error) {
    spinner.fail('Failed to hide text in image');
    throw error;
  }
}

// Extract hidden text from an image using LSB steganography
async function extractTextFromImage(imagePath, password = '') {
  const spinner = ora('Extracting hidden text from image...').start();
  
  try {
    // Load the image
    const image = await loadImage(fs.readFileSync(imagePath));
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    
    // Extract binary data from LSB of each color channel
    let binaryData = '';
    for (let i = 0; i < pixels.length; i += 4) {
      // Skip alpha channel (i+3)
      for (let j = 0; j < 3; j++) {
        binaryData += pixels[i + j] & 1;
        if (binaryData.length >= 32 && binaryData.length === 32 + parseInt(binaryData.substring(0, 32), 2)) {
          break;
        }
      }
      if (binaryData.length >= 32 && binaryData.length === 32 + parseInt(binaryData.substring(0, 32), 2)) {
        break;
      }
    }
    
    // Extract length and data
    const dataLength = parseInt(binaryData.substring(0, 32), 2);
    const extractedBinary = binaryData.substring(32, 32 + dataLength);
    
    // Convert binary to text
    let extractedText = '';
    for (let i = 0; i < extractedBinary.length; i += 8) {
      const byte = extractedBinary.substring(i, i + 8);
      extractedText += String.fromCharCode(parseInt(byte, 2));
    }
    
    // Decrypt text if password is provided
    if (password) {
      try {
        const decipher = crypto.createDecipher('aes-256-ctr', password);
        extractedText = decipher.update(extractedText, 'hex', 'utf8') + decipher.final('utf8');
      } catch (error) {
        spinner.fail('Failed to decrypt hidden text. Incorrect password?');
        throw new Error('Failed to decrypt hidden text. Incorrect password?');
      }
    }
    
    spinner.succeed('Hidden text extracted successfully');
    return extractedText;
  } catch (error) {
    spinner.fail('Failed to extract hidden text from image');
    throw error;
  }
}

// Hide a file within another file (file binding)
function hideFileInFile(carrierPath, hiddenPath, outputPath) {
  const spinner = ora('Hiding file...').start();
  
  try {
    // Read both files
    const carrierFile = fs.readFileSync(carrierPath);
    const hiddenFile = fs.readFileSync(hiddenPath);
    
    // Create a simple header to identify the hidden file
    const header = Buffer.from('STEG' + path.basename(hiddenPath) + '|' + hiddenFile.length + '|');
    
    // Combine the files
    const combined = Buffer.concat([carrierFile, header, hiddenFile]);
    
    // Write the output file
    fs.writeFileSync(outputPath, combined);
    
    spinner.succeed('File hidden successfully');
    return true;
  } catch (error) {
    spinner.fail('Failed to hide file');
    throw error;
  }
}

// Extract a hidden file from a carrier file
function extractFileFromFile(carrierPath, outputDir) {
  const spinner = ora('Extracting hidden file...').start();
  
  try {
    // Read the carrier file
    const carrierFile = fs.readFileSync(carrierPath);
    
    // Look for the steganography header
    const fileContent = carrierFile.toString();
    const headerIndex = fileContent.indexOf('STEG');
    
    if (headerIndex === -1) {
      spinner.fail('No hidden file found');
      throw new Error('No hidden file found');
    }
    
    // Parse the header
    const headerEndIndex = fileContent.indexOf('|', headerIndex + 4);
    const fileSizeEndIndex = fileContent.indexOf('|', headerEndIndex + 1);
    
    const hiddenFileName = fileContent.substring(headerIndex + 4, headerEndIndex);
    const hiddenFileSize = parseInt(fileContent.substring(headerEndIndex + 1, fileSizeEndIndex));
    
    // Extract the hidden file
    const hiddenFileData = carrierFile.slice(fileSizeEndIndex + 1, fileSizeEndIndex + 1 + hiddenFileSize);
    
    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Write the hidden file
    const outputPath = path.join(outputDir, hiddenFileName);
    fs.writeFileSync(outputPath, hiddenFileData);
    
    spinner.succeed(`Hidden file extracted to ${outputPath}`);
    return outputPath;
  } catch (error) {
    spinner.fail('Failed to extract hidden file');
    throw error;
  }
}

// Analyze a file for potential steganography
async function analyzeFileForSteganography(filePath) {
  const spinner = ora('Analyzing file for hidden data...').start();
  
  try {
    const fileExt = getFileExtension(filePath);
    const fileSize = fs.statSync(filePath).size;
    const results = {
      filePath,
      fileType: fileExt,
      fileSize,
      suspiciousIndicators: [],
      conclusion: 'No obvious signs of steganography detected'
    };
    
    // Read file header (first 20 bytes)
    const fileHeader = fs.readFileSync(filePath, { length: 20 });
    
    // Check for common steganography signatures
    if (fileHeader.includes('STEG')) {
      results.suspiciousIndicators.push('Contains "STEG" signature');
    }
    
    // Image-specific checks
    if (['.png', '.jpg', '.jpeg', '.bmp', '.gif'].includes(fileExt)) {
      // Load the image
      const image = await loadImage(fs.readFileSync(filePath));
      const canvas = createCanvas(image.width, image.height);
      const ctx = canvas.getContext('2d');
      ctx.drawImage(image, 0, 0);
      
      // Get image data
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const pixels = imageData.data;
      
      // Check for LSB patterns
      let lsbPatternScore = 0;
      const sampleSize = Math.min(1000, pixels.length / 4);
      
      for (let i = 0; i < sampleSize * 4; i += 4) {
        // Check LSB distribution (should be roughly 50/50 in normal images)
        for (let j = 0; j < 3; j++) {
          if ((pixels[i + j] & 1) === 1) {
            lsbPatternScore++;
          }
        }
      }
      
      const lsbRatio = lsbPatternScore / (sampleSize * 3);
      
      // If LSB ratio is very close to 0.5, it might be suspicious
      if (Math.abs(lsbRatio - 0.5) < 0.05) {
        results.suspiciousIndicators.push('LSB distribution is suspiciously uniform (potential LSB steganography)');
      }
      
      // Check for unusual file size
      if (fileSize > image.width * image.height * 3 * 1.5) {
        results.suspiciousIndicators.push('File size is larger than expected for this image resolution');
      }
    }
    
    // Audio/video file checks
    if (['.mp3', '.wav', '.mp4', '.avi'].includes(fileExt)) {
      // Check for unusual file size
      if (fileSize > 10 * 1024 * 1024) { // 10MB
        // Read the last 1KB of the file
        const buffer = Buffer.alloc(1024);
        const fd = fs.openSync(filePath, 'r');
        fs.readSync(fd, buffer, 0, 1024, fileSize - 1024);
        fs.closeSync(fd);
        
        // Check for non-media data patterns
        const hasTextPatterns = /[a-zA-Z0-9]{10,}/.test(buffer.toString());
        if (hasTextPatterns) {
          results.suspiciousIndicators.push('Contains text-like patterns at the end of the file');
        }
      }
    }
    
    // Check for appended data
    const fileSignatures = {
      '.png': [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
      '.jpg': [0xFF, 0xD8],
      '.pdf': [0x25, 0x50, 0x44, 0x46],
      '.zip': [0x50, 0x4B, 0x03, 0x04]
    };
    
    if (fileSignatures[fileExt]) {
      // Read the whole file
      const fileContent = fs.readFileSync(filePath);
      
      // Check for multiple occurrences of file signatures
      const signature = Buffer.from(fileSignatures[fileExt]);
      let pos = fileContent.indexOf(signature);
      let signatureCount = 0;
      
      while (pos !== -1) {
        signatureCount++;
        pos = fileContent.indexOf(signature, pos + 1);
      }
      
      if (signatureCount > 1) {
        results.suspiciousIndicators.push(`Contains ${signatureCount} file signatures (potential appended data)`);
      }
    }
    
    // Update conclusion based on findings
    if (results.suspiciousIndicators.length > 0) {
      results.conclusion = 'File contains suspicious indicators of potential steganography';
    }
    
    spinner.succeed('File analysis complete');
    return results;
  } catch (error) {
    spinner.fail('Failed to analyze file');
    throw error;
  }
}

// Display the menu for steganography tools
async function menu() {
  console.log(chalk.cyan('\n=== Steganography Tools ===\n'));
  console.log(chalk.yellow('This tool provides capabilities for hiding and detecting hidden data in files.'));
  console.log(chalk.red('IMPORTANT: For educational purposes only.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a steganography action:',
      choices: [
        'Hide text in image',
        'Extract text from image',
        'Hide file in another file',
        'Extract hidden file',
        'Analyze file for steganography',
        'Back to main menu'
      ]
    }
  ]);
  
  if (action === 'Back to main menu') {
    return;
  }
  
  try {
    switch (action) {
      case 'Hide text in image':
        const { carrierImage, hiddenText, usePassword, password, outputImagePath } = await inquirer.prompt([
          {
            type: 'input',
            name: 'carrierImage',
            message: 'Enter path to carrier image (PNG recommended):',
            validate: value => fileExists(value) ? true : 'File does not exist'
          },
          {
            type: 'input',
            name: 'hiddenText',
            message: 'Enter text to hide:',
            validate: value => value ? true : 'Please enter text to hide'
          },
          {
            type: 'confirm',
            name: 'usePassword',
            message: 'Encrypt the hidden text with a password?',
            default: false
          },
          {
            type: 'password',
            name: 'password',
            message: 'Enter encryption password:',
            when: answers => answers.usePassword,
            validate: value => value ? true : 'Please enter a password'
          },
          {
            type: 'input',
            name: 'outputImagePath',
            message: 'Enter path for output image:',
            default: answers => {
              const ext = path.extname(answers.carrierImage);
              const baseName = path.basename(answers.carrierImage, ext);
              return path.join(path.dirname(answers.carrierImage), `${baseName}_steg.png`);
            }
          }
        ]);
        
        await hideTextInImage(carrierImage, outputImagePath, hiddenText, usePassword ? password : '');
        console.log(chalk.green(`\nText hidden in image. Output saved to: ${outputImagePath}`));
        break;
        
      case 'Extract text from image':
        const { stegImage, hasPassword, extractPassword } = await inquirer.prompt([
          {
            type: 'input',
            name: 'stegImage',
            message: 'Enter path to image with hidden text:',
            validate: value => fileExists(value) ? true : 'File does not exist'
          },
          {
            type: 'confirm',
            name: 'hasPassword',
            message: 'Is the hidden text encrypted with a password?',
            default: false
          },
          {
            type: 'password',
            name: 'extractPassword',
            message: 'Enter decryption password:',
            when: answers => answers.hasPassword,
            validate: value => value ? true : 'Please enter a password'
          }
        ]);
        
        const extractedText = await extractTextFromImage(stegImage, hasPassword ? extractPassword : '');
        console.log(chalk.green('\nExtracted hidden text:'));
        console.log(chalk.white(extractedText));
        break;
        
      case 'Hide file in another file':
        const { carrierFile, hiddenFile, outputFilePath } = await inquirer.prompt([
          {
            type: 'input',
            name: 'carrierFile',
            message: 'Enter path to carrier file:',
            validate: value => fileExists(value) ? true : 'File does not exist'
          },
          {
            type: 'input',
            name: 'hiddenFile',
            message: 'Enter path to file to hide:',
            validate: value => fileExists(value) ? true : 'File does not exist'
          },
          {
            type: 'input',
            name: 'outputFilePath',
            message: 'Enter path for output file:',
            default: answers => {
              const ext = path.extname(answers.carrierFile);
              const baseName = path.basename(answers.carrierFile, ext);
              return path.join(path.dirname(answers.carrierFile), `${baseName}_steg${ext}`);
            }
          }
        ]);
        
        hideFileInFile(carrierFile, hiddenFile, outputFilePath);
        console.log(chalk.green(`\nFile hidden successfully. Output saved to: ${outputFilePath}`));
        break;
        
      case 'Extract hidden file':
        const { stegFile, outputDir } = await inquirer.prompt([
          {
            type: 'input',
            name: 'stegFile',
            message: 'Enter path to file with hidden data:',
            validate: value => fileExists(value) ? true : 'File does not exist'
          },
          {
            type: 'input',
            name: 'outputDir',
            message: 'Enter directory to save extracted file:',
            default: './extracted'
          }
        ]);
        
        const extractedFilePath = extractFileFromFile(stegFile, outputDir);
        console.log(chalk.green(`\nHidden file extracted to: ${extractedFilePath}`));
        break;
        
      case 'Analyze file for steganography':
        const { fileToAnalyze } = await inquirer.prompt([
          {
            type: 'input',
            name: 'fileToAnalyze',
            message: 'Enter path to file to analyze:',
            validate: value => fileExists(value) ? true : 'File does not exist'
          }
        ]);
        
        const analysisResults = await analyzeFileForSteganography(fileToAnalyze);
        
        console.log(chalk.green('\nSteganography Analysis Results:'));
        console.log(`File: ${analysisResults.filePath}`);
        console.log(`Type: ${analysisResults.fileType}`);
        console.log(`Size: ${(analysisResults.fileSize / 1024).toFixed(2)} KB`);
        
        console.log(chalk.yellow('\nSuspicious Indicators:'));
        if (analysisResults.suspiciousIndicators.length === 0) {
          console.log('None detected');
        } else {
          analysisResults.suspiciousIndicators.forEach((indicator, index) => {
            console.log(`${index + 1}. ${indicator}`);
          });
        }
        
        console.log(chalk.green('\nConclusion:'));
        console.log(analysisResults.conclusion);
        break;
    }
  } catch (error) {
    console.error(chalk.red('Error:'), error.message);
  }
  
  // Ask if the user wants to perform another steganography action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another steganography action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  hideTextInImage,
  extractTextFromImage,
  hideFileInFile,
  extractFileFromFile,
  analyzeFileForSteganography
};
