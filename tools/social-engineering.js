/**
 * Social Engineering Education Tool
 * 
 * This module provides social engineering awareness and education tools
 * for educational purposes ONLY. These tools should be used ONLY for
 * authorized security awareness training.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const express = require('express');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// Generate a phishing awareness template
function generatePhishingTemplate(type) {
  let template = '';
  
  switch (type) {
    case 'email':
      template = `
Subject: [PHISHING AWARENESS TRAINING] Urgent: Your Account Needs Attention

Dear [Employee Name],

We have detected unusual activity on your account. To secure your account, please click the link below to verify your identity:

[PHISHING AWARENESS LINK]

This email is part of your company's security awareness training program. 
If this were a real phishing attempt, clicking the link could have:
- Stolen your credentials
- Installed malware on your device
- Compromised company data

Remember to always:
1. Check the sender's email address carefully
2. Hover over links before clicking them
3. Be suspicious of urgent requests
4. Contact IT directly if you're unsure

Thank you for participating in this security exercise.

Regards,
[Company] Security Team
`;
      break;
      
    case 'sms':
      template = `
[PHISHING AWARENESS TRAINING]

ALERT: Your bank account has been temporarily suspended. Verify your identity at:
[PHISHING AWARENESS LINK]

This is a security awareness training message. In a real attack, this could steal your banking credentials.

Remember:
- Banks never ask for personal information via SMS
- Don't click suspicious links
- Call your bank directly using the number on your card
`;
      break;
      
    case 'website':
      template = `
<!DOCTYPE html>
<html>
<head>
  <title>Login - Secure Portal</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
    .container { width: 100%; max-width: 400px; margin: 100px auto; padding: 20px; background-color: white; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .header { text-align: center; margin-bottom: 20px; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px; }
    button { width: 100%; padding: 10px; background-color: #0066cc; color: white; border: none; border-radius: 3px; cursor: pointer; }
    .alert { background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px; margin-top: 20px; display: none; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2>Secure Portal Login</h2>
      <p>Please enter your credentials to continue</p>
    </div>
    
    <form id="loginForm">
      <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" required>
      </div>
      
      <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required>
      </div>
      
      <button type="submit">Login</button>
    </form>
    
    <div class="alert" id="phishingAlert">
      <strong>PHISHING AWARENESS TRAINING</strong>
      <p>This is a simulated phishing page created for security awareness training.</p>
      <p>In a real attack, your credentials would have been stolen.</p>
      <p>Always verify the URL and look for HTTPS before entering credentials.</p>
    </div>
  </div>
  
  <script>
    document.getElementById('loginForm').addEventListener('submit', function(e) {
      e.preventDefault();
      document.getElementById('phishingAlert').style.display = 'block';
    });
  </script>
</body>
</html>
`;
      break;
  }
  
  return template;
}

// Start a phishing awareness web server
function startPhishingAwarenessServer(port) {
  return new Promise((resolve, reject) => {
    try {
      const app = express();
      
      // Create a temporary directory for templates
      const tempDir = path.join(__dirname, 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir);
      }
      
      // Create the phishing template file
      const templatePath = path.join(tempDir, 'phishing-template.html');
      fs.writeFileSync(templatePath, generatePhishingTemplate('website'));
      
      // Serve the template
      app.get('/', (req, res) => {
        res.sendFile(templatePath);
      });
      
      // Handle form submission
      app.post('/login', (req, res) => {
        res.send(`
          <h1>PHISHING AWARENESS TRAINING</h1>
          <p>This was a simulated phishing page for security awareness training.</p>
          <p>In a real attack, your credentials would have been stolen.</p>
        `);
      });
      
      // Start the server
      const server = app.listen(port, () => {
        console.log(chalk.green(`\nPhishing awareness server started on http://localhost:${port}`));
        console.log(chalk.yellow('This is for educational purposes only. Use only in authorized training.'));
        resolve(server);
      });
    } catch (error) {
      reject(error);
    }
  });
}

// Generate a social engineering report
function generateSocialEngineeringReport(organization) {
  const report = `
# Social Engineering Vulnerability Assessment Report
## For: ${organization}
## Date: ${new Date().toLocaleDateString()}

## Executive Summary

This report outlines potential social engineering vulnerabilities identified during a simulated assessment. The findings are intended to help improve security awareness and reduce the risk of successful social engineering attacks.

## Key Findings

1. **Email Security Awareness**
   - Employees may benefit from additional training on identifying phishing emails
   - Email filtering systems should be regularly updated
   - Implement DMARC, SPF, and DKIM email authentication

2. **Phone-Based Social Engineering**
   - Establish clear procedures for verifying caller identity
   - Create protocols for handling sensitive information requests
   - Regular training on vishing (voice phishing) techniques

3. **Physical Security**
   - Tailgating prevention awareness
   - Visitor management procedures
   - Clean desk policy enforcement

4. **Online Presence**
   - Excessive information sharing on social media
   - Public professional profiles revealing organizational structure
   - Information that could be used in targeted attacks

## Recommendations

1. **Regular Security Awareness Training**
   - Conduct quarterly phishing simulations
   - Provide role-specific training for high-risk positions
   - Create a security culture through ongoing education

2. **Policy Development**
   - Create clear procedures for handling sensitive requests
   - Implement verification protocols for unusual requests
   - Establish an incident reporting system

3. **Technical Controls**
   - Enhance email filtering systems
   - Implement multi-factor authentication
   - Regular security assessments

## Conclusion

Social engineering remains one of the most effective attack vectors. By implementing the recommendations in this report, ${organization} can significantly reduce the risk of successful social engineering attacks.

---

This report is for educational and security improvement purposes only.
`;

  return report;
}

// Display the menu for social engineering education tools
async function menu() {
  console.log(chalk.cyan('\n=== Social Engineering Education Tools ===\n'));
  console.log(chalk.red('IMPORTANT: These tools are for EDUCATIONAL PURPOSES ONLY.'));
  console.log(chalk.red('Only use these tools for authorized security awareness training.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a social engineering education tool:',
      choices: [
        'Generate phishing awareness template',
        'Start phishing awareness web server',
        'Generate social engineering assessment report',
        'Social engineering techniques information',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Generate phishing awareness template':
      const { templateType, outputPath } = await inquirer.prompt([
        {
          type: 'list',
          name: 'templateType',
          message: 'Select template type:',
          choices: ['email', 'sms', 'website']
        },
        {
          type: 'input',
          name: 'outputPath',
          message: 'Enter output file path:',
          default: answers => `./phishing-template-${answers.templateType}.${answers.templateType === 'website' ? 'html' : 'txt'}`
        }
      ]);
      
      const { confirmTemplate } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmTemplate',
          message: 'This will generate a phishing template for educational purposes. Do you confirm this will only be used for authorized security awareness training?',
          default: false
        }
      ]);
      
      if (confirmTemplate) {
        try {
          const template = generatePhishingTemplate(templateType);
          fs.writeFileSync(outputPath, template);
          console.log(chalk.green(`\nPhishing awareness template saved to ${outputPath}`));
          
          console.log(chalk.yellow('\nIMPORTANT REMINDER:'));
          console.log(chalk.yellow('This template is for educational purposes only.'));
          console.log(chalk.yellow('Only use it for authorized security awareness training.'));
        } catch (error) {
          console.error(chalk.red('Error generating template:'), error);
        }
      } else {
        console.log(chalk.yellow('Template generation cancelled.'));
      }
      break;
      
    case 'Start phishing awareness web server':
      const { serverPort } = await inquirer.prompt([
        {
          type: 'number',
          name: 'serverPort',
          message: 'Enter port for the phishing awareness server:',
          default: 3000,
          validate: value => value >= 1024 && value <= 65535 ? true : 'Port must be between 1024 and 65535'
        }
      ]);
      
      const { confirmServer } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmServer',
          message: 'This will start a web server simulating a phishing page for educational purposes. Do you confirm this will only be used for authorized security awareness training?',
          default: false
        }
      ]);
      
      if (confirmServer) {
        try {
          const server = await startPhishingAwarenessServer(serverPort);
          
          console.log(chalk.green('\nPhishing awareness server is running.'));
          console.log(chalk.green(`Access it at: http://localhost:${serverPort}`));
          
          console.log(chalk.yellow('\nPress Ctrl+C to stop the server when finished.'));
          
          // Keep the server running until the user decides to stop it
          await new Promise(resolve => {
            process.on('SIGINT', () => {
              server.close();
              console.log(chalk.green('\nPhishing awareness server stopped.'));
              resolve();
            });
          });
        } catch (error) {
          console.error(chalk.red('Error starting server:'), error);
        }
      } else {
        console.log(chalk.yellow('Server start cancelled.'));
      }
      break;
      
    case 'Generate social engineering assessment report':
      const { organization, reportPath } = await inquirer.prompt([
        {
          type: 'input',
          name: 'organization',
          message: 'Enter organization name:',
          default: 'Example Organization'
        },
        {
          type: 'input',
          name: 'reportPath',
          message: 'Enter output file path:',
          default: './social-engineering-report.md'
        }
      ]);
      
      try {
        const report = generateSocialEngineeringReport(organization);
        fs.writeFileSync(reportPath, report);
        console.log(chalk.green(`\nSocial engineering assessment report saved to ${reportPath}`));
      } catch (error) {
        console.error(chalk.red('Error generating report:'), error);
      }
      break;
      
    case 'Social engineering techniques information':
      console.log(chalk.green('\nCommon Social Engineering Techniques:'));
      
      console.log(chalk.yellow('\n1. Phishing'));
      console.log('- Sending deceptive emails that appear to be from legitimate sources');
      console.log('- Often contains urgent requests, suspicious links, or attachments');
      console.log('- Variants include spear phishing (targeted), whaling (executives), and vishing (voice)');
      
      console.log(chalk.yellow('\n2. Pretexting'));
      console.log('- Creating a fabricated scenario to obtain information');
      console.log('- Often involves impersonating co-workers, police, bank officials, or other trusted entities');
      console.log('- Relies on building false trust through a convincing narrative');
      
      console.log(chalk.yellow('\n3. Baiting'));
      console.log('- Offering something enticing to spark curiosity');
      console.log('- Examples include infected USB drives, malicious downloads, or fake promotions');
      console.log('- Exploits human curiosity and desire for free items');
      
      console.log(chalk.yellow('\n4. Quid Pro Quo'));
      console.log('- Offering a service or benefit in exchange for information');
      console.log('- Common example: impersonating IT support and offering help');
      console.log('- Exploits people\'s willingness to accept help');
      
      console.log(chalk.yellow('\n5. Tailgating/Piggybacking'));
      console.log('- Gaining physical access to restricted areas by following authorized personnel');
      console.log('- Often accompanied by a pretext (carrying heavy items, appearing to be a new employee)');
      console.log('- Exploits people\'s helpfulness and courtesy');
      
      console.log(chalk.yellow('\n6. Watering Hole Attacks'));
      console.log('- Compromising websites frequently visited by the target');
      console.log('- Particularly effective against specific groups or organizations');
      console.log('- Difficult to detect as they leverage trusted websites');
      
      console.log(chalk.green('\nDefense Strategies:'));
      console.log('- Regular security awareness training');
      console.log('- Verification procedures for sensitive requests');
      console.log('- Multi-factor authentication');
      console.log('- Healthy skepticism toward unexpected requests');
      console.log('- Clear reporting channels for suspicious activities');
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another social engineering education action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another social engineering education action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  generatePhishingTemplate,
  startPhishingAwarenessServer,
  generateSocialEngineeringReport
};
