/**
 * AI Cybersecurity Tools Index
 * 
 * This module provides access to various AI-powered cybersecurity tools
 * for educational purposes ONLY.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');

// Import AI tools
const passwordCracker = require('./password-cracker');
const anomalyDetector = require('./anomaly-detector');
const phishingAnalyzer = require('./phishing-analyzer');
const vulnerabilityScanner = require('./vulnerability-scanner');
const malwareAnalyzer = require('./malware-analyzer');

// Display the menu for AI cybersecurity tools
async function menu() {
  console.log(chalk.cyan('\n=== AI-Powered Cybersecurity Tools ===\n'));
  console.log(chalk.yellow('These tools demonstrate how AI can be used in cybersecurity.'));
  console.log(chalk.red('IMPORTANT: All tools are for educational purposes only.'));
  
  const { tool } = await inquirer.prompt([
    {
      type: 'list',
      name: 'tool',
      message: 'Select an AI cybersecurity tool:',
      choices: [
        'AI Password Cracking Simulation',
        'AI Network Anomaly Detection',
        'AI Phishing Email Generator & Detector',
        'AI Vulnerability Scanner',
        'AI Malware Behavior Analyzer',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (tool) {
    case 'AI Password Cracking Simulation':
      await passwordCracker.menu();
      break;
      
    case 'AI Network Anomaly Detection':
      await anomalyDetector.menu();
      break;
      
    case 'AI Phishing Email Generator & Detector':
      await phishingAnalyzer.menu();
      break;
      
    case 'AI Vulnerability Scanner':
      await vulnerabilityScanner.menu();
      break;
      
    case 'AI Malware Behavior Analyzer':
      await malwareAnalyzer.menu();
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Return to AI tools menu after tool execution
  await menu();
}

module.exports = {
  menu,
  tools: {
    passwordCracker,
    anomalyDetector,
    phishingAnalyzer,
    vulnerabilityScanner,
    malwareAnalyzer
  }
};
