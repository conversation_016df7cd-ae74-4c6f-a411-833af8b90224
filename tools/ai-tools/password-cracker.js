/**
 * AI Password Cracker Simulation Tool
 * 
 * This module simulates how AI could be used to crack passwords
 * for educational purposes ONLY. It demonstrates password vulnerabilities
 * without actually implementing real cracking capabilities.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const crypto = require('crypto');

// Common password patterns
const commonPatterns = [
  { pattern: /^[a-z]+$/, description: 'Lowercase letters only' },
  { pattern: /^[A-Z]+$/, description: 'Uppercase letters only' },
  { pattern: /^[0-9]+$/, description: 'Numbers only' },
  { pattern: /^[a-z0-9]+$/, description: 'Lowercase letters and numbers' },
  { pattern: /^[A-Za-z0-9]+$/, description: 'Letters and numbers' },
  { pattern: /^[a-z]+[0-9]{1,4}$/, description: 'Lowercase word followed by numbers' },
  { pattern: /^[A-Z][a-z]+[0-9]{1,4}$/, description: 'Capitalized word followed by numbers' },
  { pattern: /^(password|pass|admin|user|login)[0-9]*$/i, description: 'Common password base words' },
  { pattern: /^[0-9]{4,8}$/, description: 'PIN-like numbers' },
  { pattern: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[0-9]{1,4}$/i, description: 'Month followed by numbers' },
  { pattern: /^[a-z]+[!@#$%^&*]$/, description: 'Word followed by a single special character' },
  { pattern: /^qwerty|asdfgh|zxcvbn/i, description: 'Keyboard pattern' },
  { pattern: /^(19|20)[0-9]{2}$/, description: 'Year format' },
  { pattern: /^[a-z0-9]+[!@#$%^&*][0-9]+$/i, description: 'Word/numbers with special character in middle' },
  { pattern: /(.)\1{2,}/, description: 'Contains repeated characters' }
];

// Dictionary of common passwords
const commonPasswords = [
  'password', '123456', 'qwerty', 'admin', 'welcome', 
  'login', '123456789', '12345678', 'abc123', 'password1', 
  'admin123', 'qwerty123', '1q2w3e', '12345', 'qwerty1', 
  'test123', '123123', '1234', '1234567', '123', 
  'monkey', 'dragon', 'baseball', 'football', 'letmein',
  'master', 'hello', 'freedom', 'whatever', 'qazwsx',
  'trustno1', 'shadow', 'sunshine', 'iloveyou', 'princess'
];

// Simulate AI password analysis
function analyzePassword(password) {
  // Identify patterns in the password
  const matchedPatterns = [];
  for (const { pattern, description } of commonPatterns) {
    if (pattern.test(password)) {
      matchedPatterns.push(description);
    }
  }
  
  // Check if it's a common password
  const isCommon = commonPasswords.some(common => 
    common.toLowerCase() === password.toLowerCase() ||
    password.toLowerCase().includes(common.toLowerCase())
  );
  
  // Calculate entropy (a measure of password strength)
  const entropy = calculateEntropy(password);
  
  // Estimate cracking time based on entropy and patterns
  const crackingTime = estimateCrackingTime(password, entropy, matchedPatterns.length, isCommon);
  
  return {
    matchedPatterns,
    isCommon,
    entropy,
    crackingTime
  };
}

// Calculate password entropy
function calculateEntropy(password) {
  let charset = 0;
  if (/[a-z]/.test(password)) charset += 26;
  if (/[A-Z]/.test(password)) charset += 26;
  if (/[0-9]/.test(password)) charset += 10;
  if (/[^a-zA-Z0-9]/.test(password)) charset += 33;
  
  return Math.log2(Math.pow(charset, password.length));
}

// Estimate password cracking time
function estimateCrackingTime(password, entropy, patternCount, isCommon) {
  // Base cracking speeds (passwords per second)
  const speeds = {
    bruteforce: 1000000000, // 1 billion/second for modern hardware
    patternBased: 10000000000, // 10 billion/second with pattern optimization
    dictionary: 100000000000, // 100 billion/second with dictionary
    aiAssisted: 1000000000000 // 1 trillion/second with AI optimization
  };
  
  let crackingSpeed;
  
  if (isCommon) {
    crackingSpeed = speeds.dictionary;
  } else if (patternCount > 0) {
    crackingSpeed = speeds.patternBased;
  } else {
    crackingSpeed = speeds.bruteforce;
  }
  
  // AI assistance factor (simulated)
  crackingSpeed = crackingSpeed * (1 + (patternCount * 0.5));
  
  // Calculate time in seconds
  const combinations = Math.pow(2, entropy);
  let seconds = combinations / crackingSpeed;
  
  // If it's a common password or has many patterns, drastically reduce time
  if (isCommon) {
    seconds = seconds * 0.0001; // Much faster for common passwords
  } else if (patternCount > 2) {
    seconds = seconds * 0.01; // Much faster for passwords with multiple patterns
  }
  
  return formatTime(seconds);
}

// Format time in a human-readable format
function formatTime(seconds) {
  if (seconds < 0.001) {
    return 'Instantly';
  } else if (seconds < 1) {
    return 'Less than a second';
  } else if (seconds < 60) {
    return `${Math.round(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} minutes`;
  } else if (seconds < 86400) {
    return `${Math.round(seconds / 3600)} hours`;
  } else if (seconds < 31536000) {
    return `${Math.round(seconds / 86400)} days`;
  } else if (seconds < 315360000) {
    return `${Math.round(seconds / 31536000)} years`;
  } else {
    return 'Centuries';
  }
}

// Simulate AI-based password cracking (for educational purposes only)
async function simulatePasswordCracking(passwordHash, hashType, knownInfo) {
  const spinner = ora('Simulating AI-powered password cracking...').start();
  
  // This is a simulation - we're not actually cracking anything
  return new Promise(resolve => {
    // Simulate processing time
    setTimeout(() => {
      spinner.succeed('AI password analysis complete');
      
      // Generate a simulated result
      const result = {
        success: Math.random() > 0.3, // 70% success rate for simulation
        timeTaken: `${(Math.random() * 10).toFixed(2)} seconds`,
        methodsUsed: []
      };
      
      // Add methods based on known info
      if (knownInfo.includes('name')) {
        result.methodsUsed.push('Name-based mutation patterns');
      }
      if (knownInfo.includes('dob')) {
        result.methodsUsed.push('Date of birth combinations');
      }
      if (knownInfo.includes('common')) {
        result.methodsUsed.push('Common password dictionary');
      }
      
      // Add some general methods
      result.methodsUsed.push('Neural network pattern recognition');
      result.methodsUsed.push('Markov chain probability modeling');
      result.methodsUsed.push('Transformer-based password prediction');
      
      // If no specific methods, use generic ones
      if (result.methodsUsed.length < 3) {
        result.methodsUsed.push('Behavioral pattern analysis');
        result.methodsUsed.push('Statistical frequency analysis');
      }
      
      resolve(result);
    }, 3000 + Math.random() * 2000); // Random delay for realism
  });
}

// Generate password hash for demonstration
function generateHash(password, algorithm) {
  switch (algorithm) {
    case 'md5':
      return crypto.createHash('md5').update(password).digest('hex');
    case 'sha1':
      return crypto.createHash('sha1').update(password).digest('hex');
    case 'sha256':
      return crypto.createHash('sha256').update(password).digest('hex');
    default:
      return crypto.createHash('md5').update(password).digest('hex');
  }
}

// Display the menu for AI password cracking tools
async function menu() {
  console.log(chalk.cyan('\n=== AI Password Cracking Simulation ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.red('No actual password cracking is performed.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an AI password tool:',
      choices: [
        'Analyze password vulnerability to AI',
        'Simulate AI password cracking',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Analyze password vulnerability to AI':
      const { password } = await inquirer.prompt([
        {
          type: 'password',
          name: 'password',
          message: 'Enter a password to analyze:',
          mask: '*'
        }
      ]);
      
      const analysis = analyzePassword(password);
      
      console.log(chalk.green('\nAI Vulnerability Analysis:'));
      console.log(`Password Entropy: ${analysis.entropy.toFixed(2)} bits`);
      console.log(`Estimated AI Cracking Time: ${analysis.crackingTime}`);
      
      if (analysis.isCommon) {
        console.log(chalk.red('\nHigh Risk: This is a common password or contains common elements.'));
      }
      
      if (analysis.matchedPatterns.length > 0) {
        console.log(chalk.yellow('\nDetected Patterns:'));
        analysis.matchedPatterns.forEach(pattern => {
          console.log(`- ${pattern}`);
        });
      }
      
      console.log(chalk.yellow('\nAI Vulnerability Factors:'));
      if (analysis.entropy < 40) {
        console.log('- Low entropy makes this password highly vulnerable to AI cracking');
      } else if (analysis.entropy < 60) {
        console.log('- Medium entropy provides some resistance, but still vulnerable to advanced AI');
      } else {
        console.log('- High entropy provides good resistance against current AI cracking methods');
      }
      
      if (analysis.matchedPatterns.length > 0) {
        console.log('- Recognizable patterns make this password easier for AI to predict');
      }
      
      if (analysis.isCommon) {
        console.log('- Common password elements are the first targets for AI cracking attempts');
      }
      
      console.log(chalk.green('\nRecommendations:'));
      if (analysis.entropy < 60 || analysis.matchedPatterns.length > 0 || analysis.isCommon) {
        console.log('- Use a longer, more complex password with no recognizable patterns');
        console.log('- Include a mix of uppercase, lowercase, numbers, and special characters');
        console.log('- Avoid any dictionary words or common substitutions');
        console.log('- Consider using a password manager to generate and store strong passwords');
      } else {
        console.log('- This password has good resistance to current AI cracking methods');
        console.log('- Continue to use complex, high-entropy passwords for all accounts');
      }
      break;
      
    case 'Simulate AI password cracking':
      const { hashType, knownInfo } = await inquirer.prompt([
        {
          type: 'password',
          name: 'passwordToHash',
          message: 'Enter a password to hash (for simulation purposes):',
          mask: '*'
        },
        {
          type: 'list',
          name: 'hashType',
          message: 'Select hash algorithm:',
          choices: ['md5', 'sha1', 'sha256']
        },
        {
          type: 'checkbox',
          name: 'knownInfo',
          message: 'Select known information about the target (increases success probability):',
          choices: [
            { name: 'User\'s name', value: 'name' },
            { name: 'Date of birth', value: 'dob' },
            { name: 'Uses common passwords', value: 'common' },
            { name: 'Previous password patterns', value: 'patterns' }
          ]
        }
      ]);
      
      const { passwordToHash } = await inquirer.prompt([]);
      const passwordHash = generateHash(passwordToHash, hashType);
      
      console.log(chalk.yellow(`\nPassword Hash (${hashType}): ${passwordHash}`));
      
      const { confirmSimulation } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmSimulation',
          message: 'This will simulate an AI-powered password cracking attempt. Continue?',
          default: true
        }
      ]);
      
      if (confirmSimulation) {
        try {
          const result = await simulatePasswordCracking(passwordHash, hashType, knownInfo);
          
          console.log(chalk.green('\nSimulation Results:'));
          console.log(`Success: ${result.success ? chalk.red('Yes (simulated)') : chalk.green('No')}`);
          console.log(`Time Taken: ${result.timeTaken}`);
          
          console.log(chalk.yellow('\nAI Methods Simulated:'));
          result.methodsUsed.forEach(method => {
            console.log(`- ${method}`);
          });
          
          console.log(chalk.red('\nIMPORTANT REMINDER:'));
          console.log(chalk.red('This is only a simulation for educational purposes.'));
          console.log(chalk.red('No actual password cracking was performed.'));
          
          if (result.success) {
            console.log(chalk.yellow('\nThis simulation shows that the password could potentially be vulnerable.'));
            console.log(chalk.yellow('Consider using stronger passwords and multi-factor authentication.'));
          } else {
            console.log(chalk.green('\nThis simulation shows the password resisted the simulated attack.'));
            console.log(chalk.green('Continue using strong, unique passwords for all accounts.'));
          }
        } catch (error) {
          console.error(chalk.red('Error in simulation:'), error);
        }
      } else {
        console.log(chalk.yellow('Simulation cancelled.'));
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another AI password action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another AI password analysis action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  analyzePassword,
  simulatePasswordCracking
};
