/**
 * AI-Based Malware Behavior Analyzer
 * 
 * This module simulates AI-powered behavioral analysis of potential malware
 * for educational purposes ONLY. It demonstrates how AI can identify malicious
 * behavior patterns without relying solely on signatures.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { exec } = require('child_process');

// Behavioral indicators of malicious activity
const behavioralIndicators = {
  // File system behaviors
  fileSystem: [
    { id: 'FS001', name: 'Self-replication', severity: 'High', description: 'Program creates copies of itself in multiple locations' },
    { id: 'FS002', name: 'System file modification', severity: 'Critical', description: 'Attempts to modify critical system files' },
    { id: 'FS003', name: 'Mass file encryption', severity: 'Critical', description: 'Encrypts multiple user files (potential ransomware)' },
    { id: 'FS004', name: 'Hidden file creation', severity: 'Medium', description: 'Creates hidden files or directories' },
    { id: 'FS005', name: 'File extension change', severity: 'Medium', description: 'Changes file extensions of existing files' },
    { id: 'FS006', name: 'Sensitive directory access', severity: 'High', description: 'Attempts to access sensitive system directories' }
  ],
  
  // Registry behaviors (Windows-specific)
  registry: [
    { id: 'REG001', name: 'Autorun modification', severity: 'High', description: 'Modifies registry to enable autorun at startup' },
    { id: 'REG002', name: 'Security settings modification', severity: 'Critical', description: 'Attempts to modify security settings in registry' },
    { id: 'REG003', name: 'Browser settings modification', severity: 'Medium', description: 'Modifies browser settings via registry' },
    { id: 'REG004', name: 'System configuration changes', severity: 'High', description: 'Makes changes to system configuration in registry' }
  ],
  
  // Network behaviors
  network: [
    { id: 'NET001', name: 'Command & Control communication', severity: 'Critical', description: 'Communicates with known C&C servers' },
    { id: 'NET002', name: 'Data exfiltration', severity: 'High', description: 'Sends large amounts of data to external servers' },
    { id: 'NET003', name: 'DNS requests to suspicious domains', severity: 'Medium', description: 'Makes DNS requests to newly registered or suspicious domains' },
    { id: 'NET004', name: 'Unusual port usage', severity: 'Medium', description: 'Uses non-standard ports for communication' },
    { id: 'NET005', name: 'Network scanning', severity: 'High', description: 'Performs scanning of other hosts on the network' },
    { id: 'NET006', name: 'Encrypted communication', severity: 'Low', description: 'Uses encrypted communication channels' }
  ],
  
  // Process behaviors
  process: [
    { id: 'PROC001', name: 'Process injection', severity: 'Critical', description: 'Injects code into other running processes' },
    { id: 'PROC002', name: 'Privilege escalation attempts', severity: 'Critical', description: 'Attempts to escalate privileges' },
    { id: 'PROC003', name: 'Anti-analysis techniques', severity: 'High', description: 'Uses techniques to evade analysis or detection' },
    { id: 'PROC004', name: 'Excessive resource usage', severity: 'Medium', description: 'Consumes excessive system resources' },
    { id: 'PROC005', name: 'Process hollowing', severity: 'Critical', description: 'Creates a process in suspended state and replaces its memory' },
    { id: 'PROC006', name: 'Keylogging behavior', severity: 'High', description: 'Monitors keyboard input across applications' }
  ],
  
  // Memory behaviors
  memory: [
    { id: 'MEM001', name: 'Code unpacking', severity: 'Medium', description: 'Unpacks or decrypts code at runtime' },
    { id: 'MEM002', name: 'Heap spraying', severity: 'High', description: 'Performs heap spraying techniques' },
    { id: 'MEM003', name: 'Shellcode execution', severity: 'Critical', description: 'Executes shellcode from memory' },
    { id: 'MEM004', name: 'Stack/heap overflow attempts', severity: 'High', description: 'Attempts to exploit stack or heap overflows' }
  ]
};

// Malware family characteristics
const malwareFamilies = [
  {
    name: 'Ransomware',
    behaviors: ['FS003', 'NET001', 'PROC003', 'FS005'],
    description: 'Encrypts user files and demands payment for decryption',
    examples: ['WannaCry', 'Ryuk', 'Locky', 'CryptoLocker']
  },
  {
    name: 'Trojan',
    behaviors: ['REG001', 'NET002', 'PROC001', 'FS004'],
    description: 'Disguises as legitimate software while performing malicious actions',
    examples: ['Zeus', 'Emotet', 'Trickbot', 'Dridex']
  },
  {
    name: 'Rootkit',
    behaviors: ['PROC002', 'PROC005', 'FS002', 'REG002'],
    description: 'Hides its presence while maintaining privileged access to the system',
    examples: ['ZeroAccess', 'Necurs', 'TDSS', 'Alureon']
  },
  {
    name: 'Spyware',
    behaviors: ['NET002', 'PROC006', 'REG003', 'NET003'],
    description: 'Collects information about users without their knowledge',
    examples: ['Pegasus', 'DarkHotel', 'FinFisher', 'HackingTeam RCS']
  },
  {
    name: 'Worm',
    behaviors: ['FS001', 'NET005', 'PROC004', 'NET004'],
    description: 'Self-replicates and spreads to other systems automatically',
    examples: ['Conficker', 'Stuxnet', 'Mydoom', 'ILOVEYOU']
  },
  {
    name: 'Botnet Client',
    behaviors: ['NET001', 'REG001', 'PROC003', 'NET006'],
    description: 'Connects to a command and control server to receive instructions',
    examples: ['Mirai', 'Gameover Zeus', 'Necurs', 'Kelihos']
  },
  {
    name: 'Cryptominer',
    behaviors: ['PROC004', 'NET001', 'NET006', 'MEM001'],
    description: 'Uses system resources to mine cryptocurrency without permission',
    examples: ['Coinhive', 'Cryptoloot', 'XMRig', 'JSEcoin']
  },
  {
    name: 'Backdoor',
    behaviors: ['NET004', 'REG001', 'PROC001', 'FS004'],
    description: 'Provides unauthorized remote access to a system',
    examples: ['PlugX', 'Gh0st RAT', 'Poison Ivy', 'DarkComet']
  }
];

// AI-based malware behavior analyzer class
class MalwareBehaviorAnalyzer {
  constructor() {
    this.behavioralIndicators = behavioralIndicators;
    this.malwareFamilies = malwareFamilies;
    this.detectedBehaviors = [];
    this.analysisResults = null;
  }
  
  // Analyze a file for malicious behaviors
  async analyzeFile(filePath, options = {}) {
    const spinner = ora('AI analyzing file behavior patterns...').start();
    
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        spinner.fail('File not found');
        throw new Error('File not found');
      }
      
      // Get file information
      const fileInfo = this.getFileInfo(filePath);
      
      // Simulate behavioral analysis
      return new Promise(resolve => {
        setTimeout(() => {
          // Detect behaviors based on file type and simulated execution
          this.detectedBehaviors = this.simulateBehaviorDetection(fileInfo, options);
          
          // Analyze detected behaviors
          this.analysisResults = this.analyzeBehaviors(this.detectedBehaviors);
          
          spinner.succeed('Behavioral analysis complete');
          resolve(this.analysisResults);
        }, 3000 + Math.random() * 2000); // Simulate analysis time
      });
    } catch (error) {
      spinner.fail('Analysis failed');
      throw error;
    }
  }
  
  // Get basic file information
  getFileInfo(filePath) {
    const stats = fs.statSync(filePath);
    const fileExtension = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath);
    
    // Calculate file hash
    const fileBuffer = fs.readFileSync(filePath);
    const md5Hash = crypto.createHash('md5').update(fileBuffer).digest('hex');
    const sha256Hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
    
    // Determine file type
    let fileType = 'unknown';
    if (['.exe', '.dll', '.sys', '.scr'].includes(fileExtension)) {
      fileType = 'executable';
    } else if (['.js', '.vbs', '.ps1', '.bat', '.cmd'].includes(fileExtension)) {
      fileType = 'script';
    } else if (['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf'].includes(fileExtension)) {
      fileType = 'document';
    } else if (['.zip', '.rar', '.7z', '.tar', '.gz'].includes(fileExtension)) {
      fileType = 'archive';
    }
    
    return {
      path: filePath,
      name: fileName,
      extension: fileExtension,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      md5: md5Hash,
      sha256: sha256Hash,
      type: fileType
    };
  }
  
  // Simulate behavior detection based on file info
  simulateBehaviorDetection(fileInfo, options) {
    const detectedBehaviors = [];
    const { thoroughness = 0.7 } = options;
    
    // Select behaviors based on file type and random factors
    // This is a simulation - in a real system, this would be based on actual dynamic analysis
    
    // Get all behavior IDs
    const allBehaviorIds = [];
    Object.keys(this.behavioralIndicators).forEach(category => {
      this.behavioralIndicators[category].forEach(behavior => {
        allBehaviorIds.push(behavior.id);
      });
    });
    
    // Determine how many behaviors to detect based on thoroughness
    const numBehaviorsToDetect = Math.floor(Math.random() * 10) + 1;
    
    // For executables, higher chance of detecting malicious behaviors
    if (fileInfo.type === 'executable') {
      // Select random behaviors
      for (let i = 0; i < numBehaviorsToDetect; i++) {
        const randomBehaviorId = allBehaviorIds[Math.floor(Math.random() * allBehaviorIds.length)];
        
        // Find the behavior details
        let behaviorDetails = null;
        for (const category in this.behavioralIndicators) {
          const found = this.behavioralIndicators[category].find(b => b.id === randomBehaviorId);
          if (found) {
            behaviorDetails = { ...found, category };
            break;
          }
        }
        
        if (behaviorDetails && !detectedBehaviors.some(b => b.id === randomBehaviorId)) {
          detectedBehaviors.push({
            ...behaviorDetails,
            confidence: (0.5 + Math.random() * 0.5).toFixed(2),
            details: this.generateBehaviorDetails(behaviorDetails, fileInfo)
          });
        }
      }
    } 
    // For scripts, focus on script-based behaviors
    else if (fileInfo.type === 'script') {
      // Scripts are more likely to have registry and process behaviors
      const scriptBehaviors = [
        ...this.behavioralIndicators.registry,
        ...this.behavioralIndicators.process,
        this.behavioralIndicators.fileSystem.find(b => b.id === 'FS004'),
        this.behavioralIndicators.network.find(b => b.id === 'NET001')
      ];
      
      for (let i = 0; i < Math.min(numBehaviorsToDetect, 5); i++) {
        const randomBehavior = scriptBehaviors[Math.floor(Math.random() * scriptBehaviors.length)];
        if (randomBehavior && !detectedBehaviors.some(b => b.id === randomBehavior.id)) {
          const category = randomBehavior.id.startsWith('REG') ? 'registry' : 
                          randomBehavior.id.startsWith('PROC') ? 'process' :
                          randomBehavior.id.startsWith('FS') ? 'fileSystem' : 'network';
          
          detectedBehaviors.push({
            ...randomBehavior,
            category,
            confidence: (0.5 + Math.random() * 0.5).toFixed(2),
            details: this.generateBehaviorDetails(randomBehavior, fileInfo)
          });
        }
      }
    }
    // For documents, focus on potential exploit behaviors
    else if (fileInfo.type === 'document') {
      // Documents are more likely to have memory and process behaviors
      const documentBehaviors = [
        ...this.behavioralIndicators.memory,
        this.behavioralIndicators.process.find(b => b.id === 'PROC003'),
        this.behavioralIndicators.network.find(b => b.id === 'NET001'),
        this.behavioralIndicators.network.find(b => b.id === 'NET002')
      ];
      
      for (let i = 0; i < Math.min(numBehaviorsToDetect, 3); i++) {
        const randomBehavior = documentBehaviors[Math.floor(Math.random() * documentBehaviors.length)];
        if (randomBehavior && !detectedBehaviors.some(b => b.id === randomBehavior.id)) {
          const category = randomBehavior.id.startsWith('MEM') ? 'memory' : 
                          randomBehavior.id.startsWith('PROC') ? 'process' : 'network';
          
          detectedBehaviors.push({
            ...randomBehavior,
            category,
            confidence: (0.5 + Math.random() * 0.5).toFixed(2),
            details: this.generateBehaviorDetails(randomBehavior, fileInfo)
          });
        }
      }
    }
    
    return detectedBehaviors;
  }
  
  // Generate detailed information about a detected behavior
  generateBehaviorDetails(behavior, fileInfo) {
    switch (behavior.id) {
      case 'FS001': // Self-replication
        return {
          replicationPaths: [
            '/tmp/malware_copy.bin',
            '/home/<USER>/hidden_malware.bin',
            'C:\\Windows\\System32\\svchost_malicious.exe'
          ],
          replicationMethod: 'File copy with modified names'
        };
        
      case 'FS002': // System file modification
        return {
          targetFiles: [
            'C:\\Windows\\System32\\drivers\\etc\\hosts',
            '/etc/hosts',
            'C:\\Windows\\System32\\ntdll.dll'
          ],
          modificationType: 'Binary patching'
        };
        
      case 'FS003': // Mass file encryption
        return {
          encryptionAlgorithm: 'AES-256',
          fileTypes: ['.doc', '.pdf', '.jpg', '.mp4', '.zip'],
          ransom: 'Demands 0.5 Bitcoin payment'
        };
        
      case 'NET001': // Command & Control communication
        return {
          servers: [
            'malicious-server.com:8080',
            '*************:443',
            'evil-domain.net:8443'
          ],
          protocol: 'HTTPS with custom encryption',
          dataExchanged: '24KB'
        };
        
      case 'NET002': // Data exfiltration
        return {
          dataType: 'User documents and browser data',
          destination: 'suspicious-cloud.com',
          volume: '15MB'
        };
        
      case 'PROC001': // Process injection
        return {
          targetProcesses: [
            'explorer.exe',
            'svchost.exe',
            'chrome.exe'
          ],
          injectionMethod: 'CreateRemoteThread'
        };
        
      case 'PROC002': // Privilege escalation
        return {
          technique: 'UAC bypass using fodhelper.exe',
          targetPrivilege: 'SYSTEM',
          exploitedVulnerability: 'CVE-2019-1388'
        };
        
      case 'REG001': // Autorun modification
        return {
          registryKeys: [
            'HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
            'HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce'
          ],
          persistence: 'Survives system reboot'
        };
        
      case 'MEM001': // Code unpacking
        return {
          unpackingMethod: 'Custom deobfuscation routine',
          unpackedSize: '250KB',
          layers: '3 layers of packing detected'
        };
        
      default:
        return {
          detectionTime: new Date().toISOString(),
          source: 'AI behavioral analysis engine'
        };
    }
  }
  
  // Analyze detected behaviors to identify malware family and risk level
  analyzeBehaviors(detectedBehaviors) {
    if (detectedBehaviors.length === 0) {
      return {
        isMalicious: false,
        riskLevel: 'Low',
        malwareFamily: null,
        confidence: 0,
        detectedBehaviors: [],
        recommendation: 'File appears to be benign based on behavioral analysis.'
      };
    }
    
    // Extract behavior IDs
    const behaviorIds = detectedBehaviors.map(b => b.id);
    
    // Calculate risk level based on behavior severities
    let riskScore = 0;
    const severityWeights = {
      'Critical': 10,
      'High': 5,
      'Medium': 2,
      'Low': 1
    };
    
    detectedBehaviors.forEach(behavior => {
      riskScore += severityWeights[behavior.severity] * behavior.confidence;
    });
    
    // Normalize risk score to 0-100
    riskScore = Math.min(100, Math.round(riskScore * 10));
    
    // Determine risk level
    let riskLevel;
    if (riskScore >= 70) {
      riskLevel = 'Critical';
    } else if (riskScore >= 40) {
      riskLevel = 'High';
    } else if (riskScore >= 20) {
      riskLevel = 'Medium';
    } else {
      riskLevel = 'Low';
    }
    
    // Identify potential malware family
    const familyMatches = [];
    this.malwareFamilies.forEach(family => {
      // Count how many behaviors match this family
      const matchingBehaviors = family.behaviors.filter(b => behaviorIds.includes(b));
      const matchPercentage = matchingBehaviors.length / family.behaviors.length;
      
      if (matchPercentage > 0) {
        familyMatches.push({
          name: family.name,
          confidence: matchPercentage,
          description: family.description,
          examples: family.examples
        });
      }
    });
    
    // Sort by confidence
    familyMatches.sort((a, b) => b.confidence - a.confidence);
    
    // Generate recommendation based on risk level
    let recommendation;
    if (riskLevel === 'Critical' || riskLevel === 'High') {
      recommendation = 'This file exhibits highly suspicious behaviors consistent with malware. It should be quarantined immediately and removed from the system.';
    } else if (riskLevel === 'Medium') {
      recommendation = 'This file shows some suspicious behaviors. Consider isolating it and performing additional analysis before use.';
    } else {
      recommendation = 'This file shows minimal suspicious behaviors, but caution is still advised. Verify the source before use.';
    }
    
    return {
      isMalicious: riskLevel === 'Critical' || riskLevel === 'High',
      riskLevel,
      riskScore,
      malwareFamily: familyMatches.length > 0 ? familyMatches[0] : null,
      potentialFamilies: familyMatches,
      detectedBehaviors,
      recommendation
    };
  }
  
  // Generate a comprehensive analysis report
  generateReport(fileInfo) {
    if (!this.analysisResults) {
      throw new Error('No analysis results available');
    }
    
    const results = this.analysisResults;
    
    // Generate report
    let report = `# AI Malware Behavior Analysis Report
Generated: ${new Date().toLocaleString()}

## File Information
- **Filename**: ${fileInfo.name}
- **File Type**: ${fileInfo.type}
- **File Size**: ${(fileInfo.size / 1024).toFixed(2)} KB
- **MD5 Hash**: ${fileInfo.md5}
- **SHA256 Hash**: ${fileInfo.sha256}
- **Created**: ${fileInfo.created.toLocaleString()}
- **Modified**: ${fileInfo.modified.toLocaleString()}

## Analysis Summary
- **Risk Level**: ${results.riskLevel}
- **Risk Score**: ${results.riskScore}/100
- **Malicious**: ${results.isMalicious ? 'Yes' : 'No'}
`;

    if (results.malwareFamily) {
      report += `- **Likely Malware Family**: ${results.malwareFamily.name} (${(results.malwareFamily.confidence * 100).toFixed(0)}% confidence)
- **Family Description**: ${results.malwareFamily.description}
- **Known Examples**: ${results.malwareFamily.examples.join(', ')}
`;
    }

    report += `
## Detected Behaviors
`;

    if (results.detectedBehaviors.length === 0) {
      report += 'No suspicious behaviors detected.\n';
    } else {
      results.detectedBehaviors.forEach((behavior, index) => {
        report += `
### ${index + 1}. ${behavior.name} (${behavior.severity})
- **Category**: ${behavior.category}
- **Confidence**: ${behavior.confidence * 100}%
- **Description**: ${behavior.description}
- **Details**:
`;

        for (const [key, value] of Object.entries(behavior.details)) {
          if (Array.isArray(value)) {
            report += `  - ${key}: ${value.join(', ')}\n`;
          } else {
            report += `  - ${key}: ${value}\n`;
          }
        }
      });
    }
    
    // Add other potential malware families if any
    if (results.potentialFamilies.length > 1) {
      report += `
## Other Potential Malware Families
`;
      
      results.potentialFamilies.slice(1).forEach((family, index) => {
        report += `
### ${index + 1}. ${family.name} (${(family.confidence * 100).toFixed(0)}% confidence)
- **Description**: ${family.description}
- **Known Examples**: ${family.examples.join(', ')}
`;
      });
    }
    
    // Add recommendation
    report += `
## Recommendation
${results.recommendation}
`;

    // Add mitigation steps based on risk level
    report += `
## Mitigation Steps
`;

    if (results.riskLevel === 'Critical' || results.riskLevel === 'High') {
      report += `
1. Immediately quarantine and remove the file from the system
2. Scan the entire system with updated antivirus software
3. Check for persistence mechanisms (startup entries, scheduled tasks, etc.)
4. Monitor network traffic for suspicious connections
5. Consider changing passwords for sensitive accounts if the system was compromised
`;
    } else if (results.riskLevel === 'Medium') {
      report += `
1. Isolate the file in a secure location
2. Verify the file source and purpose
3. Consider submitting the file to additional analysis services
4. Monitor system behavior if the file has been executed
`;
    } else {
      report += `
1. Verify the file source before use
2. Keep security software updated
3. Follow standard security practices
`;
    }
    
    return report;
  }
}

// Display the menu for AI malware behavior analyzer
async function menu() {
  console.log(chalk.cyan('\n=== AI Malware Behavior Analyzer ===\n'));
  console.log(chalk.yellow('This tool simulates how AI can analyze file behavior to detect malware.'));
  console.log(chalk.red('IMPORTANT: This is for educational purposes only.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a malware analysis action:',
      choices: [
        'Analyze file behavior',
        'Back to main menu'
      ]
    }
  ]);
  
  if (action === 'Back to main menu') {
    return;
  }
  
  // Get file to analyze
  const { filePath } = await inquirer.prompt([
    {
      type: 'input',
      name: 'filePath',
      message: 'Enter path to file for analysis:',
      validate: value => {
        if (!value) return 'Please enter a file path';
        if (!fs.existsSync(value)) return 'File does not exist';
        return true;
      }
    }
  ]);
  
  // Get analysis options
  const { thoroughness, saveReport } = await inquirer.prompt([
    {
      type: 'list',
      name: 'thoroughness',
      message: 'Select analysis thoroughness:',
      choices: [
        { name: 'Quick Analysis (faster, less thorough)', value: 0.3 },
        { name: 'Standard Analysis (balanced)', value: 0.6 },
        { name: 'Deep Analysis (slower, more thorough)', value: 1.0 }
      ]
    },
    {
      type: 'confirm',
      name: 'saveReport',
      message: 'Save analysis report to file?',
      default: true
    }
  ]);
  
  try {
    // Create analyzer instance
    const analyzer = new MalwareBehaviorAnalyzer();
    
    // Get file information
    const fileInfo = analyzer.getFileInfo(filePath);
    
    console.log(chalk.green('\nFile Information:'));
    console.log(`Filename: ${fileInfo.name}`);
    console.log(`Type: ${fileInfo.type}`);
    console.log(`Size: ${(fileInfo.size / 1024).toFixed(2)} KB`);
    console.log(`MD5: ${fileInfo.md5}`);
    
    // Perform behavioral analysis
    const results = await analyzer.analyzeFile(filePath, { thoroughness });
    
    // Display analysis results
    console.log(chalk.green('\nBehavioral Analysis Results:'));
    console.log(`Risk Level: ${getRiskLevelColor(results.riskLevel)(results.riskLevel)}`);
    console.log(`Risk Score: ${results.riskScore}/100`);
    console.log(`Malicious: ${results.isMalicious ? chalk.red('Yes') : chalk.green('No')}`);
    
    if (results.malwareFamily) {
      console.log(chalk.yellow('\nLikely Malware Family:'));
      console.log(`Name: ${results.malwareFamily.name}`);
      console.log(`Confidence: ${(results.malwareFamily.confidence * 100).toFixed(0)}%`);
      console.log(`Description: ${results.malwareFamily.description}`);
      console.log(`Known Examples: ${results.malwareFamily.examples.join(', ')}`);
    }
    
    console.log(chalk.yellow('\nDetected Behaviors:'));
    if (results.detectedBehaviors.length === 0) {
      console.log('No suspicious behaviors detected.');
    } else {
      results.detectedBehaviors.forEach((behavior, index) => {
        console.log(`${index + 1}. ${behavior.name} (${behavior.severity}) - ${behavior.confidence * 100}% confidence`);
        console.log(`   ${behavior.description}`);
      });
    }
    
    console.log(chalk.green('\nRecommendation:'));
    console.log(results.recommendation);
    
    // Save report if requested
    if (saveReport) {
      const reportPath = './ai_malware_analysis_report.md';
      const report = analyzer.generateReport(fileInfo);
      fs.writeFileSync(reportPath, report);
      console.log(chalk.green(`\nDetailed report saved to ${reportPath}`));
    }
    
  } catch (error) {
    console.error(chalk.red('Error during malware analysis:'), error);
  }
  
  // Ask if the user wants to perform another analysis
  const { anotherAnalysis } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAnalysis',
      message: 'Would you like to analyze another file?',
      default: true
    }
  ]);
  
  if (anotherAnalysis) {
    await menu();
  }
}

// Helper function to get color based on risk level
function getRiskLevelColor(riskLevel) {
  switch (riskLevel) {
    case 'Critical':
      return chalk.red.bold;
    case 'High':
      return chalk.red;
    case 'Medium':
      return chalk.yellow;
    case 'Low':
      return chalk.green;
    default:
      return chalk.white;
  }
}

module.exports = {
  menu,
  MalwareBehaviorAnalyzer
};
