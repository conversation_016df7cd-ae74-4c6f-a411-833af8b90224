/**
 * AI Network Anomaly Detection Tool
 * 
 * This module simulates AI-based network traffic analysis to detect
 * anomalies and potential security threats for educational purposes.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs');
const path = require('path');
const pcap = require('pcap');
const { exec } = require('child_process');

// Simulated AI model for network traffic analysis
class NetworkAIModel {
  constructor() {
    this.trainingStatus = 'untrained';
    this.trainingData = [];
    this.anomalyThreshold = 0.7;
    this.knownPatterns = [
      { name: 'Port Scanning', features: ['multiple_ports', 'short_connections', 'sequential'] },
      { name: 'Data Exfiltration', features: ['large_outbound', 'unusual_destination', 'encrypted'] },
      { name: 'Brute Force', features: ['repeated_auth', 'failed_connections', 'single_target'] },
      { name: 'Command & Control', features: ['periodic_beaconing', 'unusual_protocol', 'encrypted'] },
      { name: 'DDoS', features: ['high_volume', 'distributed_sources', 'similar_packets'] },
      { name: 'Malware Communication', features: ['unusual_dns', 'encrypted', 'unusual_timing'] }
    ];
  }
  
  // Train the model on network data
  async train(trafficData, duration) {
    const spinner = ora('Training AI model on network traffic...').start();
    
    return new Promise(resolve => {
      // Simulate training process
      setTimeout(() => {
        this.trainingData = trafficData;
        this.trainingStatus = 'trained';
        
        spinner.succeed(`AI model trained on ${trafficData.length} traffic samples`);
        resolve({
          success: true,
          baselineFeatures: this.extractBaselineFeatures(trafficData)
        });
      }, duration * 1000);
    });
  }
  
  // Extract baseline features from training data
  extractBaselineFeatures(trafficData) {
    // This is a simulation - in a real system, this would perform actual feature extraction
    return {
      averagePacketSize: Math.floor(300 + Math.random() * 700),
      commonPorts: [80, 443, 53, 22, 123],
      trafficVolume: Math.floor(1000 + Math.random() * 9000) + ' packets/minute',
      protocolDistribution: {
        TCP: Math.floor(50 + Math.random() * 30) + '%',
        UDP: Math.floor(10 + Math.random() * 20) + '%',
        ICMP: Math.floor(1 + Math.random() * 5) + '%',
        Other: Math.floor(1 + Math.random() * 10) + '%'
      },
      connectionDuration: {
        average: Math.floor(10 + Math.random() * 50) + ' seconds',
        max: Math.floor(100 + Math.random() * 500) + ' seconds'
      }
    };
  }
  
  // Analyze traffic for anomalies
  async analyzeTraffic(trafficData) {
    if (this.trainingStatus !== 'trained') {
      throw new Error('AI model must be trained before analysis');
    }
    
    const spinner = ora('AI analyzing network traffic for anomalies...').start();
    
    return new Promise(resolve => {
      // Simulate analysis process
      setTimeout(() => {
        const anomalies = this.detectAnomalies(trafficData);
        spinner.succeed('AI analysis complete');
        resolve(anomalies);
      }, 3000 + Math.random() * 2000);
    });
  }
  
  // Detect anomalies in traffic data
  detectAnomalies(trafficData) {
    // This is a simulation - in a real system, this would use actual ML algorithms
    const anomalies = [];
    
    // Randomly detect some anomalies for simulation purposes
    if (Math.random() > 0.3) { // 70% chance to detect something
      const numAnomalies = Math.floor(Math.random() * 3) + 1;
      
      for (let i = 0; i < numAnomalies; i++) {
        // Pick a random pattern
        const pattern = this.knownPatterns[Math.floor(Math.random() * this.knownPatterns.length)];
        
        // Create a simulated anomaly
        anomalies.push({
          type: pattern.name,
          confidence: (0.7 + Math.random() * 0.3).toFixed(2),
          features: pattern.features,
          source: this.generateRandomIP(),
          destination: this.generateRandomIP(),
          timestamp: new Date().toISOString(),
          details: this.generateAnomalyDetails(pattern.name)
        });
      }
    }
    
    return anomalies;
  }
  
  // Generate random IP for simulation
  generateRandomIP() {
    return `${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;
  }
  
  // Generate details for specific anomaly types
  generateAnomalyDetails(anomalyType) {
    switch (anomalyType) {
      case 'Port Scanning':
        return {
          ports: Array.from({ length: 10 }, () => Math.floor(Math.random() * 65536)),
          scanType: ['SYN', 'FIN', 'XMAS', 'NULL'][Math.floor(Math.random() * 4)],
          duration: Math.floor(Math.random() * 60) + ' seconds'
        };
        
      case 'Data Exfiltration':
        return {
          dataVolume: Math.floor(Math.random() * 1000) + ' MB',
          protocol: ['HTTPS', 'DNS', 'ICMP', 'Custom'][Math.floor(Math.random() * 4)],
          destination: ['Unknown', 'Non-business', 'Foreign'][Math.floor(Math.random() * 3)]
        };
        
      case 'Brute Force':
        return {
          attempts: Math.floor(Math.random() * 1000),
          service: ['SSH', 'FTP', 'RDP', 'Web Login'][Math.floor(Math.random() * 4)],
          successRate: (Math.random() * 0.1).toFixed(2)
        };
        
      case 'Command & Control':
        return {
          beaconInterval: Math.floor(Math.random() * 60) + ' seconds',
          communicationType: ['Encrypted', 'Obfuscated', 'Steganography'][Math.floor(Math.random() * 3)],
          dataExchanged: Math.floor(Math.random() * 100) + ' KB'
        };
        
      case 'DDoS':
        return {
          attackType: ['SYN Flood', 'UDP Flood', 'HTTP Flood', 'Amplification'][Math.floor(Math.random() * 4)],
          sourceCount: Math.floor(Math.random() * 1000),
          packetRate: Math.floor(Math.random() * 100000) + ' packets/second'
        };
        
      case 'Malware Communication':
        return {
          malwareFamily: ['Generic Trojan', 'Ransomware', 'Botnet', 'Backdoor'][Math.floor(Math.random() * 4)],
          communicationMethod: ['DNS Tunneling', 'Encrypted HTTP', 'Custom Protocol'][Math.floor(Math.random() * 3)],
          dataExchanged: Math.floor(Math.random() * 100) + ' KB'
        };
        
      default:
        return {
          unknownType: true,
          severity: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)]
        };
    }
  }
}

// Capture network traffic for analysis
async function captureTraffic(interface, duration) {
  const spinner = ora(`Capturing network traffic on ${interface} for ${duration} seconds...`).start();
  
  return new Promise((resolve, reject) => {
    try {
      const pcapSession = pcap.createSession(interface, '');
      const packets = [];
      
      pcapSession.on('packet', (rawPacket) => {
        try {
          const packet = pcap.decode.packet(rawPacket);
          packets.push(packet);
        } catch (err) {
          // Silently ignore packet decoding errors
        }
      });
      
      // Set timeout to stop capture after duration
      setTimeout(() => {
        pcapSession.close();
        spinner.succeed(`Captured ${packets.length} packets for analysis`);
        resolve(packets);
      }, duration * 1000);
      
    } catch (error) {
      spinner.fail('Failed to capture network traffic');
      reject(error);
    }
  });
}

// Get available network interfaces
function getNetworkInterfaces() {
  const interfaces = require('os').networkInterfaces();
  const result = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        result.push({
          name,
          address: iface.address,
          netmask: iface.netmask,
          mac: iface.mac
        });
      }
    }
  }
  
  return result;
}

// Generate a report from anomaly findings
function generateAnomalyReport(anomalies, baselineFeatures) {
  let report = `# AI Network Anomaly Detection Report
Generated: ${new Date().toLocaleString()}

## Baseline Network Profile
- Average Packet Size: ${baselineFeatures.averagePacketSize} bytes
- Common Ports: ${baselineFeatures.commonPorts.join(', ')}
- Traffic Volume: ${baselineFeatures.trafficVolume}
- Protocol Distribution:
  - TCP: ${baselineFeatures.protocolDistribution.TCP}
  - UDP: ${baselineFeatures.protocolDistribution.UDP}
  - ICMP: ${baselineFeatures.protocolDistribution.ICMP}
  - Other: ${baselineFeatures.protocolDistribution.Other}
- Connection Duration:
  - Average: ${baselineFeatures.connectionDuration.average}
  - Maximum: ${baselineFeatures.connectionDuration.max}

## Detected Anomalies
`;

  if (anomalies.length === 0) {
    report += 'No anomalies detected in the analyzed traffic.\n';
  } else {
    anomalies.forEach((anomaly, index) => {
      report += `
### Anomaly ${index + 1}: ${anomaly.type}
- Confidence: ${anomaly.confidence * 100}%
- Source: ${anomaly.source}
- Destination: ${anomaly.destination}
- Timestamp: ${anomaly.timestamp}
- Detected Features: ${anomaly.features.join(', ')}
- Details:
`;

      for (const [key, value] of Object.entries(anomaly.details)) {
        report += `  - ${key}: ${value}\n`;
      }
      
      report += '\n';
    });
    
    report += `
## Recommendations
`;

    // Add recommendations based on detected anomalies
    const anomalyTypes = new Set(anomalies.map(a => a.type));
    
    if (anomalyTypes.has('Port Scanning')) {
      report += '- Review firewall rules to block unauthorized port scanning\n';
      report += '- Implement network segmentation to limit scan exposure\n';
      report += '- Consider deploying a network IPS to block scanning activity\n';
    }
    
    if (anomalyTypes.has('Data Exfiltration')) {
      report += '- Implement data loss prevention (DLP) solutions\n';
      report += '- Review and restrict outbound traffic to unknown destinations\n';
      report += '- Monitor for unusual data transfer patterns\n';
    }
    
    if (anomalyTypes.has('Brute Force')) {
      report += '- Implement account lockout policies\n';
      report += '- Enable multi-factor authentication\n';
      report += '- Use fail2ban or similar tools to block repeated login attempts\n';
    }
    
    if (anomalyTypes.has('Command & Control')) {
      report += '- Investigate potentially compromised systems\n';
      report += '- Block communication to known C2 servers\n';
      report += '- Implement DNS filtering and monitoring\n';
    }
    
    if (anomalyTypes.has('DDoS')) {
      report += '- Implement DDoS protection services\n';
      report += '- Configure rate limiting on network equipment\n';
      report += '- Develop a DDoS response plan\n';
    }
    
    if (anomalyTypes.has('Malware Communication')) {
      report += '- Scan potentially infected systems\n';
      report += '- Update antivirus and EDR solutions\n';
      report += '- Block communication to malicious domains\n';
    }
  }
  
  return report;
}

// Display the menu for AI network anomaly detection
async function menu() {
  console.log(chalk.cyan('\n=== AI Network Anomaly Detection ===\n'));
  console.log(chalk.yellow('This tool simulates how AI can detect unusual network behavior.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an AI network analysis action:',
      choices: [
        'Train AI on normal network traffic',
        'Analyze traffic for anomalies',
        'Back to main menu'
      ]
    }
  ]);
  
  // Create AI model instance if it doesn't exist
  if (!global.networkAIModel) {
    global.networkAIModel = new NetworkAIModel();
  }
  
  const aiModel = global.networkAIModel;
  
  switch (action) {
    case 'Train AI on normal network traffic':
      const interfaces = getNetworkInterfaces();
      
      if (interfaces.length === 0) {
        console.log(chalk.yellow('No suitable network interfaces found.'));
        break;
      }
      
      const { selectedInterface, trainingDuration } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedInterface',
          message: 'Select a network interface for traffic capture:',
          choices: interfaces.map(iface => ({
            name: `${iface.name} (${iface.address})`,
            value: iface.name
          }))
        },
        {
          type: 'number',
          name: 'trainingDuration',
          message: 'Training duration in seconds:',
          default: 10,
          validate: value => value > 0 ? true : 'Duration must be greater than 0'
        }
      ]);
      
      const { confirmTraining } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmTraining',
          message: `This will capture network traffic on ${selectedInterface} for ${trainingDuration} seconds to train the AI. Continue?`,
          default: true
        }
      ]);
      
      if (confirmTraining) {
        try {
          // Capture traffic for training
          const trafficData = await captureTraffic(selectedInterface, trainingDuration);
          
          if (trafficData.length === 0) {
            console.log(chalk.yellow('No packets captured. Try a different interface or longer duration.'));
            break;
          }
          
          // Train the AI model
          const trainingResult = await aiModel.train(trafficData, 3); // 3 seconds for training simulation
          
          console.log(chalk.green('\nAI Model Training Complete'));
          console.log(chalk.yellow('\nBaseline Network Profile:'));
          console.log(`Average Packet Size: ${trainingResult.baselineFeatures.averagePacketSize} bytes`);
          console.log(`Common Ports: ${trainingResult.baselineFeatures.commonPorts.join(', ')}`);
          console.log(`Traffic Volume: ${trainingResult.baselineFeatures.trafficVolume}`);
          console.log(`Protocol Distribution:`);
          Object.entries(trainingResult.baselineFeatures.protocolDistribution).forEach(([protocol, percentage]) => {
            console.log(`  ${protocol}: ${percentage}`);
          });
          
          console.log(chalk.green('\nThe AI model is now ready to detect anomalies.'));
          
        } catch (error) {
          console.error(chalk.red('Error during AI training:'), error);
        }
      } else {
        console.log(chalk.yellow('Training cancelled.'));
      }
      break;
      
    case 'Analyze traffic for anomalies':
      if (aiModel.trainingStatus !== 'trained') {
        console.log(chalk.yellow('The AI model must be trained before analyzing traffic.'));
        console.log(chalk.yellow('Please select "Train AI on normal network traffic" first.'));
        break;
      }
      
      const availableInterfaces = getNetworkInterfaces();
      
      if (availableInterfaces.length === 0) {
        console.log(chalk.yellow('No suitable network interfaces found.'));
        break;
      }
      
      const { analyzeInterface, analyzeDuration, saveReport } = await inquirer.prompt([
        {
          type: 'list',
          name: 'analyzeInterface',
          message: 'Select a network interface for anomaly detection:',
          choices: availableInterfaces.map(iface => ({
            name: `${iface.name} (${iface.address})`,
            value: iface.name
          }))
        },
        {
          type: 'number',
          name: 'analyzeDuration',
          message: 'Traffic capture duration in seconds:',
          default: 15,
          validate: value => value > 0 ? true : 'Duration must be greater than 0'
        },
        {
          type: 'confirm',
          name: 'saveReport',
          message: 'Save analysis report to file?',
          default: true
        }
      ]);
      
      const { confirmAnalysis } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmAnalysis',
          message: `This will capture and analyze network traffic on ${analyzeInterface} for ${analyzeDuration} seconds. Continue?`,
          default: true
        }
      ]);
      
      if (confirmAnalysis) {
        try {
          // Capture traffic for analysis
          const trafficData = await captureTraffic(analyzeInterface, analyzeDuration);
          
          if (trafficData.length === 0) {
            console.log(chalk.yellow('No packets captured. Try a different interface or longer duration.'));
            break;
          }
          
          // Analyze the traffic
          const anomalies = await aiModel.analyzeTraffic(trafficData);
          
          console.log(chalk.green('\nAI Anomaly Detection Results:'));
          
          if (anomalies.length === 0) {
            console.log(chalk.green('No anomalies detected in the analyzed traffic.'));
          } else {
            console.log(chalk.yellow(`Detected ${anomalies.length} potential anomalies:`));
            
            anomalies.forEach((anomaly, index) => {
              console.log(chalk.red(`\nAnomaly ${index + 1}: ${anomaly.type}`));
              console.log(`Confidence: ${anomaly.confidence * 100}%`);
              console.log(`Source: ${anomaly.source}`);
              console.log(`Destination: ${anomaly.destination}`);
              console.log(`Timestamp: ${anomaly.timestamp}`);
              console.log(`Detected Features: ${anomaly.features.join(', ')}`);
              
              console.log(chalk.yellow('\nDetails:'));
              for (const [key, value] of Object.entries(anomaly.details)) {
                console.log(`  ${key}: ${value}`);
              }
            });
            
            // Display recommendations
            console.log(chalk.green('\nRecommendations:'));
            const anomalyTypes = new Set(anomalies.map(a => a.type));
            
            if (anomalyTypes.has('Port Scanning')) {
              console.log('- Review firewall rules to block unauthorized port scanning');
              console.log('- Implement network segmentation to limit scan exposure');
            }
            
            if (anomalyTypes.has('Data Exfiltration')) {
              console.log('- Implement data loss prevention (DLP) solutions');
              console.log('- Review and restrict outbound traffic to unknown destinations');
            }
            
            if (anomalyTypes.has('Brute Force')) {
              console.log('- Implement account lockout policies');
              console.log('- Enable multi-factor authentication');
            }
            
            if (anomalyTypes.has('Command & Control')) {
              console.log('- Investigate potentially compromised systems');
              console.log('- Block communication to known C2 servers');
            }
            
            if (anomalyTypes.has('DDoS')) {
              console.log('- Implement DDoS protection services');
              console.log('- Configure rate limiting on network equipment');
            }
            
            if (anomalyTypes.has('Malware Communication')) {
              console.log('- Scan potentially infected systems');
              console.log('- Update antivirus and EDR solutions');
            }
          }
          
          // Save report if requested
          if (saveReport) {
            const reportPath = './ai_anomaly_report.md';
            const report = generateAnomalyReport(anomalies, aiModel.extractBaselineFeatures(trafficData));
            fs.writeFileSync(reportPath, report);
            console.log(chalk.green(`\nDetailed report saved to ${reportPath}`));
          }
          
        } catch (error) {
          console.error(chalk.red('Error during anomaly detection:'), error);
        }
      } else {
        console.log(chalk.yellow('Analysis cancelled.'));
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another AI network analysis action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another AI network analysis action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  NetworkAIModel,
  captureTraffic,
  getNetworkInterfaces,
  generateAnomalyReport
};
