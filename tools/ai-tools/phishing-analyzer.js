/**
 * AI Phishing Email Generator and Detector
 * 
 * This module simulates AI-based phishing email generation and detection
 * for educational purposes ONLY. It demonstrates how AI can be used both
 * to create and detect sophisticated phishing attempts.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs');
const path = require('path');

// Phishing email templates for different scenarios
const phishingTemplates = {
  banking: {
    subjects: [
      "Important: Your account has been temporarily limited",
      "Security Alert: Unusual activity detected",
      "Action Required: Verify your account information",
      "Your account access will be revoked",
      "Urgent: Unauthorized transaction detected"
    ],
    bodies: [
      `Dear valued customer,

We have detected unusual activity on your account. To ensure your account security, we have temporarily limited some features.

To restore full access to your account, please verify your information by clicking the link below:

[PHISHING LINK]

If you do not verify your information within 24 hours, your account access may be suspended.

Thank you for your cooperation.

Sincerely,
[BANK NAME] Security Team`,

      `Dear [CUSTOMER NAME],

Our security system has detected an unauthorized login attempt to your account from an unrecognized device.

IP Address: [RANDOM IP]
Location: [RANDOM LOCATION]
Date and Time: [CURRENT DATE]

If this was not you, please secure your account immediately by following these steps:

1. Click here to verify your identity: [PHISHING LINK]
2. Update your security information
3. Review recent account activity

Your immediate attention is required.

Regards,
[BANK NAME] Fraud Prevention Team`
    ]
  },
  
  corporate: {
    subjects: [
      "Action required: Update your company credentials",
      "Important: Your email storage is full",
      "IT Department: Mandatory security update",
      "HR: Update to employee benefits portal",
      "Urgent: Your account will be deactivated"
    ],
    bodies: [
      `Dear [EMPLOYEE NAME],

The IT department is performing mandatory security updates to all employee accounts.

To maintain access to company resources, please update your credentials by clicking the link below:

[PHISHING LINK]

This update is mandatory and must be completed by end of day.

Thank you for your cooperation.

IT Department
[COMPANY NAME]`,

      `Dear Colleague,

Our records indicate that your email storage is almost full. If no action is taken, you may lose the ability to send or receive emails.

To increase your storage capacity, please validate your account:

[PHISHING LINK]

This is an automated message. Please do not reply.

IT Support
[COMPANY NAME]`
    ]
  },
  
  social: {
    subjects: [
      "Someone logged into your account from a new device",
      "Your account has been limited - verify your identity",
      "You have 3 unread messages from [SOCIAL MEDIA]",
      "Your password was reset - confirm this change",
      "Your profile has been reported - review now"
    ],
    bodies: [
      `Hi [USER],

We noticed a login to your account from a new device.

Device: [RANDOM DEVICE]
Location: [RANDOM LOCATION]
Time: [CURRENT TIME]

If this wasn't you, your account may have been compromised.

Secure your account now: [PHISHING LINK]

Thanks,
[SOCIAL MEDIA] Security Team`,

      `Dear [USER],

Your account was recently reported for violating our community guidelines.

After reviewing your account activity, we have temporarily limited some features of your account.

To restore full access and avoid permanent suspension, please verify your identity:

[PHISHING LINK]

If you believe this is an error, you can appeal through the link above.

[SOCIAL MEDIA] Trust & Safety Team`
    ]
  }
};

// AI phishing email generator
function generatePhishingEmail(targetType, customization) {
  // Select template based on target type
  const template = phishingTemplates[targetType];
  if (!template) {
    throw new Error(`Unknown target type: ${targetType}`);
  }
  
  // Select random subject and body
  const subject = template.subjects[Math.floor(Math.random() * template.subjects.length)];
  let body = template.bodies[Math.floor(Math.random() * template.bodies.length)];
  
  // Apply customizations
  if (customization.companyName) {
    body = body.replace(/\[COMPANY NAME\]/g, customization.companyName);
    body = body.replace(/\[BANK NAME\]/g, customization.companyName);
  }
  
  if (customization.recipientName) {
    body = body.replace(/\[EMPLOYEE NAME\]/g, customization.recipientName);
    body = body.replace(/\[CUSTOMER NAME\]/g, customization.recipientName);
    body = body.replace(/\[USER\]/g, customization.recipientName);
  }
  
  if (customization.phishingLink) {
    body = body.replace(/\[PHISHING LINK\]/g, customization.phishingLink);
  } else {
    // Generate a fake but realistic-looking URL
    const domains = {
      banking: ['secure-banklogin.com', 'account-verify-now.com', 'banking-secure-portal.net'],
      corporate: ['corporate-login-portal.com', 'company-verification.net', 'employee-portal-secure.com'],
      social: ['social-account-verify.com', 'login-secure-profile.net', 'account-security-check.com']
    };
    
    const domain = domains[targetType][Math.floor(Math.random() * domains[targetType].length)];
    const fakeUrl = `https://${domain}/verify?id=${Math.random().toString(36).substring(2, 15)}`;
    body = body.replace(/\[PHISHING LINK\]/g, fakeUrl);
  }
  
  // Replace other placeholders with random data
  body = body.replace(/\[RANDOM IP\]/g, `${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`);
  
  const locations = ['New York, USA', 'London, UK', 'Moscow, Russia', 'Beijing, China', 'Unknown Location'];
  body = body.replace(/\[RANDOM LOCATION\]/g, locations[Math.floor(Math.random() * locations.length)]);
  
  const devices = ['Windows PC', 'Android Device', 'iPhone', 'Mac', 'Linux Server', 'Unknown Device'];
  body = body.replace(/\[RANDOM DEVICE\]/g, devices[Math.floor(Math.random() * devices.length)]);
  
  body = body.replace(/\[CURRENT DATE\]/g, new Date().toLocaleDateString());
  body = body.replace(/\[CURRENT TIME\]/g, new Date().toLocaleTimeString());
  
  // Add social media name if needed
  const socialPlatforms = ['Facebook', 'Instagram', 'Twitter', 'LinkedIn', 'TikTok'];
  body = body.replace(/\[SOCIAL MEDIA\]/g, socialPlatforms[Math.floor(Math.random() * socialPlatforms.length)]);
  
  return {
    subject,
    body
  };
}

// AI phishing email detector
function analyzePhishingEmail(emailContent) {
  // This is a simulation of AI-based phishing detection
  // In a real implementation, this would use NLP and ML models
  
  // Phishing indicators to check for
  const indicators = [
    { pattern: /urgent|immediate|alert|attention required|security|verify|confirm|validate|authenticate|update|limited/i, name: 'Urgency language', weight: 0.4 },
    { pattern: /account|password|login|sign in|credential|username|user name|billing|payment|credit card|ssn|social security/i, name: 'Credential-related terms', weight: 0.5 },
    { pattern: /click|link|verify|confirm|validate|authenticate|update|check|review|sign in/i, name: 'Action requests', weight: 0.3 },
    { pattern: /suspicious|unusual|unauthorized|fraud|security|breach|hack|compromised|risk/i, name: 'Security threat language', weight: 0.6 },
    { pattern: /https?:\/\/(?!(?:www\.)?(?:google\.com|facebook\.com|microsoft\.com|apple\.com|amazon\.com|paypal\.com))[^\s]+/i, name: 'Suspicious URL', weight: 0.7 },
    { pattern: /dear customer|valued customer|account holder|member/i, name: 'Generic greeting', weight: 0.3 },
    { pattern: /bank|account|paypal|ebay|amazon|netflix|facebook|microsoft|apple|google/i, name: 'Brand impersonation', weight: 0.5 },
    { pattern: /suspended|disabled|locked|limited|restricted|blocked|hold|frozen/i, name: 'Account status threats', weight: 0.6 },
    { pattern: /verify your identity|confirm your information|update your details|check your account/i, name: 'Information verification requests', weight: 0.5 },
    { pattern: /within 24 hours|immediately|urgent|right away|as soon as possible|now/i, name: 'Time pressure tactics', weight: 0.6 },
    { pattern: /copyright|legal|terms|policy|all rights reserved/i, name: 'Legal-sounding language', weight: 0.2 },
    { pattern: /attachment|download|open|file|document|pdf|doc|xlsx/i, name: 'Attachment references', weight: 0.4 }
  ];
  
  // Check for indicators
  let totalScore = 0;
  const detectedIndicators = [];
  
  for (const indicator of indicators) {
    if (indicator.pattern.test(emailContent)) {
      totalScore += indicator.weight;
      detectedIndicators.push({
        name: indicator.name,
        weight: indicator.weight,
        examples: emailContent.match(indicator.pattern)
      });
    }
  }
  
  // Normalize score to 0-1 range
  const normalizedScore = Math.min(totalScore / 3, 1);
  
  // Determine classification
  let classification;
  if (normalizedScore >= 0.7) {
    classification = 'Highly Likely Phishing';
  } else if (normalizedScore >= 0.4) {
    classification = 'Suspicious';
  } else {
    classification = 'Likely Legitimate';
  }
  
  return {
    phishingScore: normalizedScore.toFixed(2),
    classification,
    detectedIndicators,
    techniquesUsed: identifyPhishingTechniques(emailContent, detectedIndicators)
  };
}

// Identify specific phishing techniques used
function identifyPhishingTechniques(emailContent, detectedIndicators) {
  const techniques = [];
  
  // Check for specific techniques based on indicators and content
  if (detectedIndicators.some(i => i.name === 'Urgency language' || i.name === 'Time pressure tactics')) {
    techniques.push('Creating a sense of urgency');
  }
  
  if (detectedIndicators.some(i => i.name === 'Account status threats')) {
    techniques.push('Fear-based manipulation');
  }
  
  if (detectedIndicators.some(i => i.name === 'Brand impersonation')) {
    techniques.push('Brand impersonation');
  }
  
  if (detectedIndicators.some(i => i.name === 'Generic greeting')) {
    techniques.push('Mass-targeting (non-personalized)');
  }
  
  if (detectedIndicators.some(i => i.name === 'Suspicious URL')) {
    techniques.push('Deceptive links');
  }
  
  if (/login|sign in|username|password/i.test(emailContent)) {
    techniques.push('Credential harvesting');
  }
  
  if (/security|secure|protection|safety/i.test(emailContent)) {
    techniques.push('False security claims');
  }
  
  if (/won|winner|prize|reward|gift|free/i.test(emailContent)) {
    techniques.push('Prize or reward bait');
  }
  
  if (/tax|irs|government|official|authority/i.test(emailContent)) {
    techniques.push('Authority impersonation');
  }
  
  if (/attachment|download|open|file|document/i.test(emailContent)) {
    techniques.push('Malicious attachment lure');
  }
  
  return techniques.length > 0 ? techniques : ['Generic phishing attempt'];
}

// Display the menu for AI phishing tools
async function menu() {
  console.log(chalk.cyan('\n=== AI Phishing Email Generator & Detector ===\n'));
  console.log(chalk.red('IMPORTANT: This tool is for educational purposes only.'));
  console.log(chalk.red('The generator demonstrates how AI can create convincing phishing emails.'));
  console.log(chalk.red('The detector shows how AI can identify phishing attempts.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an AI phishing tool:',
      choices: [
        'Generate phishing email example',
        'Analyze email for phishing indicators',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Generate phishing email example':
      const { targetType, recipientName, companyName, saveToFile, filePath } = await inquirer.prompt([
        {
          type: 'list',
          name: 'targetType',
          message: 'Select phishing email type:',
          choices: [
            { name: 'Banking/Financial', value: 'banking' },
            { name: 'Corporate/Business', value: 'corporate' },
            { name: 'Social Media', value: 'social' }
          ]
        },
        {
          type: 'input',
          name: 'recipientName',
          message: 'Enter recipient name (for personalization):',
          default: 'User'
        },
        {
          type: 'input',
          name: 'companyName',
          message: 'Enter company/organization name:',
          default: answers => {
            if (answers.targetType === 'banking') return 'SecureBank';
            if (answers.targetType === 'corporate') return 'ACME Corporation';
            return 'SocialConnect';
          }
        },
        {
          type: 'confirm',
          name: 'saveToFile',
          message: 'Save generated email to file?',
          default: false
        },
        {
          type: 'input',
          name: 'filePath',
          message: 'Enter file path:',
          default: './phishing_example.txt',
          when: answers => answers.saveToFile
        }
      ]);
      
      const { confirmGeneration } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmGeneration',
          message: 'This will generate a simulated phishing email for educational purposes. Do you confirm this will only be used for security awareness?',
          default: false
        }
      ]);
      
      if (confirmGeneration) {
        try {
          const spinner = ora('AI generating phishing email example...').start();
          
          // Simulate AI processing time
          setTimeout(() => {
            spinner.succeed('Phishing email generated');
            
            const phishingEmail = generatePhishingEmail(targetType, {
              recipientName,
              companyName,
              phishingLink: null // Auto-generate
            });
            
            console.log(chalk.green('\nGenerated Phishing Email:'));
            console.log(chalk.yellow(`Subject: ${phishingEmail.subject}`));
            console.log(chalk.white('\n' + phishingEmail.body));
            
            // Analyze the generated email to show its phishing indicators
            const analysis = analyzePhishingEmail(phishingEmail.subject + '\n' + phishingEmail.body);
            
            console.log(chalk.red('\nPhishing Indicators:'));
            console.log(`Phishing Score: ${analysis.phishingScore} (${analysis.classification})`);
            
            console.log(chalk.yellow('\nTechniques Used:'));
            analysis.techniquesUsed.forEach(technique => {
              console.log(`- ${technique}`);
            });
            
            // Save to file if requested
            if (saveToFile) {
              const content = `Subject: ${phishingEmail.subject}\n\n${phishingEmail.body}\n\n---\nThis is a simulated phishing email for educational purposes only.\nPhishing Score: ${analysis.phishingScore} (${analysis.classification})`;
              fs.writeFileSync(filePath, content);
              console.log(chalk.green(`\nPhishing example saved to ${filePath}`));
            }
            
            console.log(chalk.red('\nREMINDER: This example is for educational purposes only.'));
            console.log(chalk.red('Use only for security awareness training.'));
            
          }, 2000);
        } catch (error) {
          console.error(chalk.red('Error generating phishing email:'), error);
        }
      } else {
        console.log(chalk.yellow('Generation cancelled.'));
      }
      break;
      
    case 'Analyze email for phishing indicators':
      const { analysisMethod } = await inquirer.prompt([
        {
          type: 'list',
          name: 'analysisMethod',
          message: 'How would you like to provide the email content?',
          choices: [
            { name: 'Enter email text', value: 'text' },
            { name: 'Load from file', value: 'file' }
          ]
        }
      ]);
      
      let emailContent = '';
      
      if (analysisMethod === 'text') {
        const { emailSubject, emailBody } = await inquirer.prompt([
          {
            type: 'input',
            name: 'emailSubject',
            message: 'Enter email subject:',
            validate: value => value ? true : 'Please enter an email subject'
          },
          {
            type: 'editor',
            name: 'emailBody',
            message: 'Enter email body:',
            validate: value => value ? true : 'Please enter an email body'
          }
        ]);
        
        emailContent = emailSubject + '\n\n' + emailBody;
      } else {
        const { emailFilePath } = await inquirer.prompt([
          {
            type: 'input',
            name: 'emailFilePath',
            message: 'Enter path to email file:',
            validate: value => {
              if (!value) return 'Please enter a file path';
              if (!fs.existsSync(value)) return 'File does not exist';
              return true;
            }
          }
        ]);
        
        emailContent = fs.readFileSync(emailFilePath, 'utf8');
      }
      
      try {
        const spinner = ora('AI analyzing email for phishing indicators...').start();
        
        // Simulate AI processing time
        setTimeout(() => {
          spinner.succeed('Analysis complete');
          
          const analysis = analyzePhishingEmail(emailContent);
          
          console.log(chalk.green('\nPhishing Analysis Results:'));
          console.log(`Phishing Probability: ${(analysis.phishingScore * 100).toFixed(0)}%`);
          console.log(`Classification: ${getClassificationColor(analysis.classification)(analysis.classification)}`);
          
          console.log(chalk.yellow('\nDetected Indicators:'));
          if (analysis.detectedIndicators.length === 0) {
            console.log('No phishing indicators detected');
          } else {
            analysis.detectedIndicators.forEach(indicator => {
              console.log(`- ${indicator.name} (Weight: ${indicator.weight})`);
              if (indicator.examples && indicator.examples.length > 0) {
                console.log(`  Example: "${indicator.examples[0]}"`);
              }
            });
          }
          
          console.log(chalk.yellow('\nPhishing Techniques:'));
          analysis.techniquesUsed.forEach(technique => {
            console.log(`- ${technique}`);
          });
          
          console.log(chalk.green('\nRecommendations:'));
          if (analysis.classification === 'Highly Likely Phishing') {
            console.log('- Do not respond to this email');
            console.log('- Do not click any links or download any attachments');
            console.log('- Report this email to your IT security team');
            console.log('- Delete the email from your inbox');
          } else if (analysis.classification === 'Suspicious') {
            console.log('- Exercise caution with this email');
            console.log('- Verify the sender through other channels before taking any action');
            console.log('- Do not click links or download attachments unless verified');
            console.log('- Consider reporting this email to your IT security team');
          } else {
            console.log('- This email appears legitimate based on the analysis');
            console.log('- However, always remain vigilant for phishing attempts');
          }
          
        }, 3000);
      } catch (error) {
        console.error(chalk.red('Error analyzing email:'), error);
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another AI phishing action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another AI phishing analysis action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

// Helper function to get color based on classification
function getClassificationColor(classification) {
  switch (classification) {
    case 'Highly Likely Phishing':
      return chalk.red.bold;
    case 'Suspicious':
      return chalk.yellow;
    case 'Likely Legitimate':
      return chalk.green;
    default:
      return chalk.white;
  }
}

module.exports = {
  menu,
  generatePhishingEmail,
  analyzePhishingEmail
};
