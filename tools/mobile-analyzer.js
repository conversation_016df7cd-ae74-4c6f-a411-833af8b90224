/**
 * Mobile Device Analyzer
 * 
 * This module provides tools for analyzing and testing the security of mobile devices
 * (Android and iOS) for educational purposes ONLY.
 * 
 * IMPORTANT: Only use on devices you own or have explicit permission to test.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Check if ADB (Android Debug Bridge) is installed
async function checkAdbInstalled() {
  return new Promise((resolve) => {
    exec('adb version', (error) => {
      resolve(!error);
    });
  });
}

// Get connected Android devices
async function getConnectedAndroidDevices() {
  return new Promise((resolve, reject) => {
    exec('adb devices', (error, stdout) => {
      if (error) {
        reject(error);
        return;
      }
      
      const lines = stdout.split('\n').filter(line => line.trim() !== '' && !line.includes('List of devices'));
      const devices = [];
      
      lines.forEach(line => {
        const [id, status] = line.split('\t');
        if (id && status) {
          devices.push({ id: id.trim(), status: status.trim() });
        }
      });
      
      resolve(devices);
    });
  });
}

// Get Android device information
async function getAndroidDeviceInfo(deviceId) {
  const spinner = ora('Gathering Android device information...').start();
  
  try {
    // Get device model
    const modelPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell getprop ro.product.model`, (error, stdout) => {
        resolve(error ? 'Unknown' : stdout.trim());
      });
    });
    
    // Get Android version
    const versionPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell getprop ro.build.version.release`, (error, stdout) => {
        resolve(error ? 'Unknown' : stdout.trim());
      });
    });
    
    // Get security patch level
    const securityPatchPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell getprop ro.build.version.security_patch`, (error, stdout) => {
        resolve(error ? 'Unknown' : stdout.trim());
      });
    });
    
    // Get if device is rooted
    const rootedPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell which su`, (error, stdout) => {
        resolve(!error && stdout.trim() !== '');
      });
    });
    
    // Get installed apps
    const appsPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell pm list packages -3`, (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        
        const apps = stdout.split('\n')
          .filter(line => line.trim() !== '')
          .map(line => line.replace('package:', '').trim());
        
        resolve(apps);
      });
    });
    
    const [model, version, securityPatch, rooted, apps] = await Promise.all([
      modelPromise, versionPromise, securityPatchPromise, rootedPromise, appsPromise
    ]);
    
    spinner.succeed('Device information gathered');
    
    return {
      model,
      version,
      securityPatch,
      rooted,
      apps
    };
  } catch (error) {
    spinner.fail('Failed to gather device information');
    throw error;
  }
}

// Analyze Android app permissions
async function analyzeAppPermissions(deviceId, packageName) {
  const spinner = ora(`Analyzing permissions for ${packageName}...`).start();
  
  try {
    const result = await new Promise((resolve, reject) => {
      exec(`adb -s ${deviceId} shell dumpsys package ${packageName} | grep permission`, (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }
        
        const permissions = stdout.split('\n')
          .filter(line => line.includes('permission.'))
          .map(line => line.trim());
        
        resolve(permissions);
      });
    });
    
    spinner.succeed('App permissions analyzed');
    return result;
  } catch (error) {
    spinner.fail('Failed to analyze app permissions');
    throw error;
  }
}

// Check for common Android security issues
async function checkAndroidSecurityIssues(deviceId) {
  const spinner = ora('Checking for security issues...').start();
  
  try {
    // Check if USB debugging is enabled
    const usbDebuggingPromise = new Promise((resolve) => {
      // USB debugging must be enabled for ADB to work, so it's always true
      resolve(true);
    });
    
    // Check if device encryption is enabled
    const encryptionPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell getprop ro.crypto.state`, (error, stdout) => {
        resolve(!error && stdout.trim() === 'encrypted');
      });
    });
    
    // Check if unknown sources is enabled
    const unknownSourcesPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell settings get secure install_non_market_apps`, (error, stdout) => {
        resolve(!error && stdout.trim() === '1');
      });
    });
    
    // Check for developer options
    const developerOptionsPromise = new Promise((resolve) => {
      exec(`adb -s ${deviceId} shell settings get global development_settings_enabled`, (error, stdout) => {
        resolve(!error && stdout.trim() === '1');
      });
    });
    
    const [usbDebugging, encryption, unknownSources, developerOptions] = await Promise.all([
      usbDebuggingPromise, encryptionPromise, unknownSourcesPromise, developerOptionsPromise
    ]);
    
    spinner.succeed('Security check complete');
    
    return {
      usbDebugging,
      encryption,
      unknownSources,
      developerOptions
    };
  } catch (error) {
    spinner.fail('Failed to check security issues');
    throw error;
  }
}

// Simulate iOS device analysis (since direct iOS analysis requires jailbreak or special tools)
async function simulateIosDeviceAnalysis() {
  const spinner = ora('Simulating iOS device analysis...').start();
  
  return new Promise(resolve => {
    setTimeout(() => {
      spinner.succeed('iOS analysis simulation complete');
      
      // Simulated iOS device info
      resolve({
        model: 'iPhone Simulation',
        version: 'iOS 15.0 (simulated)',
        securityIssues: [
          'Simulation only - no real device connected',
          'For real iOS analysis, you would need specialized tools like:',
          '- libimobiledevice for non-jailbroken devices',
          '- SSH access for jailbroken devices',
          '- Commercial tools like Cellebrite or GrayKey for forensic analysis'
        ],
        recommendations: [
          'Keep iOS updated to latest version',
          'Avoid jailbreaking production devices',
          'Enable device encryption',
          'Use strong passcode (not just 6 digits)',
          'Enable two-factor authentication for Apple ID',
          'Review app permissions regularly'
        ]
      });
    }, 2000);
  });
}

// Generate security recommendations for Android
function generateAndroidRecommendations(deviceInfo, securityIssues) {
  const recommendations = [];
  
  // Version-based recommendations
  if (deviceInfo.version) {
    const versionNumber = parseFloat(deviceInfo.version);
    if (versionNumber < 10) {
      recommendations.push('Update Android to at least version 10 for better security features');
    }
  }
  
  // Security patch recommendations
  if (deviceInfo.securityPatch) {
    const patchDate = new Date(deviceInfo.securityPatch);
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    if (patchDate < sixMonthsAgo) {
      recommendations.push('Security patch is more than 6 months old. Update your device');
    }
  }
  
  // Root-based recommendations
  if (deviceInfo.rooted) {
    recommendations.push('Device is rooted, which significantly reduces security. Consider unrooting for sensitive use');
  }
  
  // Security issues recommendations
  if (securityIssues.usbDebugging) {
    recommendations.push('Disable USB debugging when not needed for development');
  }
  
  if (!securityIssues.encryption) {
    recommendations.push('Enable full-disk encryption in device settings');
  }
  
  if (securityIssues.unknownSources) {
    recommendations.push('Disable installation from unknown sources when not needed');
  }
  
  if (securityIssues.developerOptions) {
    recommendations.push('Disable developer options when not actively developing');
  }
  
  // General recommendations
  recommendations.push('Use a strong screen lock (PIN, pattern, or password)');
  recommendations.push('Install security updates promptly when available');
  recommendations.push('Review app permissions regularly');
  recommendations.push('Use a security app from a reputable vendor');
  recommendations.push('Avoid installing apps from unofficial sources');
  
  return recommendations;
}

// Display the menu for mobile device analysis
async function menu() {
  console.log(chalk.cyan('\n=== Mobile Device Analyzer ===\n'));
  console.log(chalk.yellow('This tool provides mobile device security analysis capabilities.'));
  console.log(chalk.red('IMPORTANT: Only use on devices you own or have explicit permission to test.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a mobile analysis action:',
      choices: [
        'Analyze Android device',
        'Analyze iOS device (simulation)',
        'Back to main menu'
      ]
    }
  ]);
  
  if (action === 'Back to main menu') {
    return;
  }
  
  if (action === 'Analyze Android device') {
    // Check if ADB is installed
    const adbInstalled = await checkAdbInstalled();
    
    if (!adbInstalled) {
      console.log(chalk.red('\nError: Android Debug Bridge (ADB) is not installed or not in PATH.'));
      console.log(chalk.yellow('Please install ADB to use Android device analysis features:'));
      console.log('- For Windows: Download Android SDK Platform Tools');
      console.log('- For macOS: Use Homebrew with "brew install android-platform-tools"');
      console.log('- For Linux: Use package manager, e.g., "sudo apt install adb"');
      return;
    }
    
    // Get connected Android devices
    try {
      const devices = await getConnectedAndroidDevices();
      
      if (devices.length === 0) {
        console.log(chalk.yellow('\nNo Android devices connected.'));
        console.log('Please connect an Android device with USB debugging enabled and try again.');
        return;
      }
      
      // Select device if multiple are connected
      let deviceId;
      if (devices.length === 1) {
        deviceId = devices[0].id;
        console.log(chalk.green(`\nUsing connected device: ${deviceId}`));
      } else {
        const { selectedDevice } = await inquirer.prompt([
          {
            type: 'list',
            name: 'selectedDevice',
            message: 'Select Android device:',
            choices: devices.map(device => ({
              name: `${device.id} (${device.status})`,
              value: device.id
            }))
          }
        ]);
        deviceId = selectedDevice;
      }
      
      // Get device information
      const deviceInfo = await getAndroidDeviceInfo(deviceId);
      
      console.log(chalk.green('\nDevice Information:'));
      console.log(`Model: ${deviceInfo.model}`);
      console.log(`Android Version: ${deviceInfo.version}`);
      console.log(`Security Patch Level: ${deviceInfo.securityPatch}`);
      console.log(`Rooted: ${deviceInfo.rooted ? chalk.red('Yes (Security Risk)') : chalk.green('No')}`);
      console.log(`Installed Apps: ${deviceInfo.apps.length} third-party apps`);
      
      // Check for security issues
      const securityIssues = await checkAndroidSecurityIssues(deviceId);
      
      console.log(chalk.yellow('\nSecurity Issues:'));
      console.log(`USB Debugging: ${securityIssues.usbDebugging ? chalk.red('Enabled (Security Risk)') : chalk.green('Disabled')}`);
      console.log(`Device Encryption: ${securityIssues.encryption ? chalk.green('Enabled') : chalk.red('Disabled (Security Risk)')}`);
      console.log(`Unknown Sources: ${securityIssues.unknownSources ? chalk.red('Enabled (Security Risk)') : chalk.green('Disabled')}`);
      console.log(`Developer Options: ${securityIssues.developerOptions ? chalk.yellow('Enabled (Potential Risk)') : chalk.green('Disabled')}`);
      
      // Generate recommendations
      const recommendations = generateAndroidRecommendations(deviceInfo, securityIssues);
      
      console.log(chalk.green('\nSecurity Recommendations:'));
      recommendations.forEach((recommendation, index) => {
        console.log(`${index + 1}. ${recommendation}`);
      });
      
      // Option to analyze app permissions
      const { analyzeApp } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'analyzeApp',
          message: 'Would you like to analyze permissions for a specific app?',
          default: false
        }
      ]);
      
      if (analyzeApp && deviceInfo.apps.length > 0) {
        const { selectedApp } = await inquirer.prompt([
          {
            type: 'list',
            name: 'selectedApp',
            message: 'Select app to analyze:',
            choices: deviceInfo.apps
          }
        ]);
        
        try {
          const permissions = await analyzeAppPermissions(deviceId, selectedApp);
          
          console.log(chalk.green(`\nPermissions for ${selectedApp}:`));
          if (permissions.length === 0) {
            console.log('No specific permissions found or app does not request permissions.');
          } else {
            permissions.forEach(permission => {
              console.log(`- ${permission}`);
            });
            
            // Categorize dangerous permissions
            const dangerousPermissions = permissions.filter(p => 
              p.includes('CAMERA') || 
              p.includes('RECORD_AUDIO') || 
              p.includes('ACCESS_FINE_LOCATION') ||
              p.includes('READ_CONTACTS') ||
              p.includes('READ_SMS') ||
              p.includes('SEND_SMS') ||
              p.includes('READ_CALL_LOG')
            );
            
            if (dangerousPermissions.length > 0) {
              console.log(chalk.red('\nPotentially sensitive permissions:'));
              dangerousPermissions.forEach(permission => {
                console.log(`- ${permission}`);
              });
              console.log(chalk.yellow('\nReview these permissions carefully and consider if the app really needs them.'));
            }
          }
        } catch (error) {
          console.error(chalk.red('Error analyzing app permissions:'), error);
        }
      }
      
    } catch (error) {
      console.error(chalk.red('Error analyzing Android device:'), error);
    }
  } else if (action === 'Analyze iOS device (simulation)') {
    try {
      const iosAnalysis = await simulateIosDeviceAnalysis();
      
      console.log(chalk.green('\niOS Device Analysis (Simulation):'));
      console.log(`Model: ${iosAnalysis.model}`);
      console.log(`Version: ${iosAnalysis.version}`);
      
      console.log(chalk.yellow('\nSimulated Security Analysis:'));
      iosAnalysis.securityIssues.forEach(issue => {
        console.log(`- ${issue}`);
      });
      
      console.log(chalk.green('\nSecurity Recommendations for iOS:'));
      iosAnalysis.recommendations.forEach((recommendation, index) => {
        console.log(`${index + 1}. ${recommendation}`);
      });
      
      console.log(chalk.yellow('\nNote: This is a simulation. For actual iOS device analysis, specialized tools are required.'));
      
    } catch (error) {
      console.error(chalk.red('Error in iOS analysis simulation:'), error);
    }
  }
  
  // Ask if the user wants to perform another mobile analysis
  const { anotherAnalysis } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAnalysis',
      message: 'Would you like to perform another mobile device analysis?',
      default: true
    }
  ]);
  
  if (anotherAnalysis) {
    await menu();
  }
}

module.exports = {
  menu,
  checkAdbInstalled,
  getConnectedAndroidDevices,
  getAndroidDeviceInfo,
  analyzeAppPermissions,
  checkAndroidSecurityIssues,
  simulateIosDeviceAnalysis
};
