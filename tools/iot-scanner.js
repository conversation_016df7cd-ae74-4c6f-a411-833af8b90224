/**
 * IoT Security Scanner
 * 
 * This module provides tools for analyzing and testing the security of IoT devices
 * for educational purposes ONLY.
 * 
 * IMPORTANT: Only use on devices you own or have explicit permission to test.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const dns = require('dns');
const net = require('net');

// Common IoT device ports
const commonIoTPorts = [
  { port: 21, service: 'FTP', description: 'File Transfer Protocol' },
  { port: 22, service: 'SSH', description: 'Secure Shell' },
  { port: 23, service: 'Telnet', description: 'Unencrypted terminal access (insecure)' },
  { port: 25, service: 'SMTP', description: 'Simple Mail Transfer Protocol' },
  { port: 80, service: 'HTTP', description: 'Web server (unencrypted)' },
  { port: 443, service: 'HTTPS', description: 'Web server (encrypted)' },
  { port: 554, service: 'RTSP', description: 'Real Time Streaming Protocol' },
  { port: 1883, service: 'MQTT', description: 'Message Queuing Telemetry Transport' },
  { port: 5683, service: 'CoAP', description: 'Constrained Application Protocol' },
  { port: 8080, service: 'HTTP Alt', description: 'Alternative HTTP port' },
  { port: 8443, service: 'HTTPS Alt', description: 'Alternative HTTPS port' },
  { port: 8883, service: 'MQTT-SSL', description: 'MQTT over SSL' },
  { port: 9000, service: 'Management', description: 'Common management interface port' }
];

// Common IoT device default credentials
const defaultCredentials = [
  { username: 'admin', password: 'admin' },
  { username: 'admin', password: 'password' },
  { username: 'admin', password: '1234' },
  { username: 'admin', password: 'admin123' },
  { username: 'root', password: 'root' },
  { username: 'root', password: 'password' },
  { username: 'root', password: '1234' },
  { username: 'user', password: 'user' },
  { username: 'guest', password: 'guest' }
];

// Common IoT device vulnerabilities
const commonVulnerabilities = [
  { id: 'IOTV-001', name: 'Default credentials', description: 'Device uses default or weak credentials', severity: 'Critical' },
  { id: 'IOTV-002', name: 'Unencrypted services', description: 'Device offers services without encryption', severity: 'High' },
  { id: 'IOTV-003', name: 'Outdated firmware', description: 'Device runs outdated firmware with known vulnerabilities', severity: 'High' },
  { id: 'IOTV-004', name: 'Open ports', description: 'Device has unnecessary open ports', severity: 'Medium' },
  { id: 'IOTV-005', name: 'Insecure web interface', description: 'Web interface lacks security controls', severity: 'High' },
  { id: 'IOTV-006', name: 'No firmware update mechanism', description: 'Device lacks secure update mechanism', severity: 'Medium' },
  { id: 'IOTV-007', name: 'Insecure communication', description: 'Device uses insecure communication protocols', severity: 'High' },
  { id: 'IOTV-008', name: 'Hardcoded credentials', description: 'Device has hardcoded credentials', severity: 'Critical' },
  { id: 'IOTV-009', name: 'Lack of encryption', description: 'Device does not encrypt stored data', severity: 'High' },
  { id: 'IOTV-010', name: 'Excessive permissions', description: 'Device requests excessive permissions', severity: 'Medium' }
];

// Resolve hostname to IP
async function resolveHostname(hostname) {
  return new Promise((resolve, reject) => {
    dns.lookup(hostname, (err, address) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(address);
    });
  });
}

// Check if a port is open
async function checkPort(host, port, timeout = 3000) {
  return new Promise(resolve => {
    const socket = new net.Socket();
    let status = false;
    let error = null;
    
    // Set timeout
    socket.setTimeout(timeout);
    
    // Handle connection
    socket.on('connect', () => {
      status = true;
      socket.destroy();
    });
    
    // Handle errors
    socket.on('error', err => {
      error = err;
    });
    
    // Handle timeout
    socket.on('timeout', () => {
      socket.destroy();
    });
    
    // Handle close
    socket.on('close', () => {
      resolve({ port, open: status, error });
    });
    
    // Connect
    socket.connect(port, host);
  });
}

// Scan IoT device for open ports
async function scanPorts(host, ports = commonIoTPorts.map(p => p.port)) {
  const spinner = ora(`Scanning ${host} for open ports...`).start();
  
  try {
    // Resolve hostname if needed
    let ipAddress = host;
    if (!net.isIP(host)) {
      ipAddress = await resolveHostname(host);
    }
    
    // Check each port
    const portPromises = ports.map(port => checkPort(ipAddress, port));
    const results = await Promise.all(portPromises);
    
    // Filter open ports
    const openPorts = results.filter(result => result.open);
    
    spinner.succeed(`Found ${openPorts.length} open ports on ${host}`);
    
    // Add service information to open ports
    return openPorts.map(result => {
      const portInfo = commonIoTPorts.find(p => p.port === result.port) || 
                      { service: 'Unknown', description: 'Unknown service' };
      
      return {
        port: result.port,
        service: portInfo.service,
        description: portInfo.description
      };
    });
  } catch (error) {
    spinner.fail(`Failed to scan ports on ${host}`);
    throw error;
  }
}

// Check for default credentials on web interface
async function checkDefaultWebCredentials(host, port = 80, isHttps = false) {
  const spinner = ora('Checking for default web credentials...').start();
  
  try {
    const protocol = isHttps ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}:${port}`;
    
    // Try to access the web interface
    try {
      await axios.get(baseUrl, { timeout: 5000 });
    } catch (error) {
      // Ignore errors, we just want to check if the server responds
    }
    
    // This is a simulation - in a real tool, you would attempt to authenticate
    // with default credentials, but we'll simulate the results
    
    // Simulate finding default credentials (random result for demonstration)
    const vulnerableCredentials = [];
    
    if (Math.random() > 0.7) {
      // Simulate finding 1-3 default credentials
      const numFound = Math.floor(Math.random() * 3) + 1;
      for (let i = 0; i < numFound; i++) {
        if (i < defaultCredentials.length) {
          vulnerableCredentials.push(defaultCredentials[i]);
        }
      }
    }
    
    if (vulnerableCredentials.length > 0) {
      spinner.succeed(`Found ${vulnerableCredentials.length} default credentials`);
    } else {
      spinner.succeed('No default credentials found');
    }
    
    return vulnerableCredentials;
  } catch (error) {
    spinner.fail('Failed to check for default credentials');
    throw error;
  }
}

// Check web interface security
async function checkWebSecurity(host, port = 80, isHttps = false) {
  const spinner = ora('Analyzing web interface security...').start();
  
  try {
    const protocol = isHttps ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}:${port}`;
    
    // Security issues to check
    const securityIssues = [];
    
    // Check for HTTPS
    if (!isHttps) {
      securityIssues.push({
        issue: 'Unencrypted HTTP',
        description: 'Web interface uses unencrypted HTTP instead of HTTPS',
        severity: 'High'
      });
    }
    
    // Make a request to the web interface
    let response;
    try {
      response = await axios.get(baseUrl, { 
        timeout: 5000,
        validateStatus: () => true // Accept any status code
      });
    } catch (error) {
      // If we can't connect, return what we have so far
      spinner.warn('Could not connect to web interface');
      return securityIssues;
    }
    
    // Check security headers
    const headers = response.headers;
    const securityHeaders = [
      { name: 'strict-transport-security', description: 'HTTP Strict Transport Security' },
      { name: 'x-content-type-options', description: 'X-Content-Type-Options' },
      { name: 'x-frame-options', description: 'X-Frame-Options' },
      { name: 'content-security-policy', description: 'Content Security Policy' }
    ];
    
    securityHeaders.forEach(header => {
      if (!headers[header.name]) {
        securityIssues.push({
          issue: `Missing ${header.description}`,
          description: `Web interface does not set the ${header.description} header`,
          severity: 'Medium'
        });
      }
    });
    
    // Check for server information disclosure
    if (headers['server']) {
      securityIssues.push({
        issue: 'Server information disclosure',
        description: `Web server reveals version information: ${headers['server']}`,
        severity: 'Low'
      });
    }
    
    spinner.succeed('Web interface security analysis complete');
    return securityIssues;
  } catch (error) {
    spinner.fail('Failed to analyze web interface security');
    throw error;
  }
}

// Analyze IoT device security
async function analyzeIoTSecurity(host, openPorts, webCredentials, webSecurityIssues) {
  // Detected vulnerabilities
  const vulnerabilities = [];
  
  // Check for open insecure ports
  const insecurePorts = openPorts.filter(port => 
    ['Telnet', 'FTP', 'HTTP'].includes(port.service)
  );
  
  if (insecurePorts.length > 0) {
    vulnerabilities.push({
      id: 'IOTV-002',
      name: 'Unencrypted services',
      description: `Device offers unencrypted services: ${insecurePorts.map(p => p.service).join(', ')}`,
      severity: 'High',
      evidence: `Open ports: ${insecurePorts.map(p => p.port).join(', ')}`
    });
  }
  
  // Check for default credentials
  if (webCredentials && webCredentials.length > 0) {
    vulnerabilities.push({
      id: 'IOTV-001',
      name: 'Default credentials',
      description: 'Device uses default or weak credentials',
      severity: 'Critical',
      evidence: `Found ${webCredentials.length} default credential sets`
    });
  }
  
  // Add web security issues
  if (webSecurityIssues && webSecurityIssues.length > 0) {
    webSecurityIssues.forEach(issue => {
      const matchingVuln = commonVulnerabilities.find(v => 
        v.name.toLowerCase().includes(issue.issue.toLowerCase())
      );
      
      if (matchingVuln) {
        vulnerabilities.push({
          id: matchingVuln.id,
          name: matchingVuln.name,
          description: issue.description,
          severity: issue.severity,
          evidence: `Detected in web interface`
        });
      } else {
        // If no matching vulnerability, create a custom one
        vulnerabilities.push({
          id: 'IOTV-CUSTOM',
          name: issue.issue,
          description: issue.description,
          severity: issue.severity,
          evidence: `Detected in web interface`
        });
      }
    });
  }
  
  // Calculate security score
  let securityScore = 100;
  
  // Deduct points based on vulnerability severity
  vulnerabilities.forEach(vuln => {
    switch (vuln.severity) {
      case 'Critical':
        securityScore -= 25;
        break;
      case 'High':
        securityScore -= 15;
        break;
      case 'Medium':
        securityScore -= 10;
        break;
      case 'Low':
        securityScore -= 5;
        break;
    }
  });
  
  // Ensure score doesn't go below 0
  securityScore = Math.max(0, securityScore);
  
  // Generate recommendations
  const recommendations = [];
  
  if (vulnerabilities.some(v => v.id === 'IOTV-001')) {
    recommendations.push('Change default credentials to strong, unique passwords');
  }
  
  if (vulnerabilities.some(v => v.id === 'IOTV-002')) {
    recommendations.push('Disable unnecessary services or replace with encrypted alternatives');
    recommendations.push('Use HTTPS instead of HTTP, SFTP instead of FTP, SSH instead of Telnet');
  }
  
  if (webSecurityIssues && webSecurityIssues.length > 0) {
    recommendations.push('Update device firmware to the latest version');
    recommendations.push('Configure security headers in the web interface');
  }
  
  // Add general recommendations
  recommendations.push('Place IoT devices on a separate network segment');
  recommendations.push('Regularly check for and apply firmware updates');
  recommendations.push('Disable remote access when not needed');
  recommendations.push('Monitor device behavior for unusual activity');
  
  return {
    host,
    securityScore,
    vulnerabilities,
    recommendations
  };
}

// Generate a comprehensive security report
function generateSecurityReport(host, scanResults, securityAnalysis) {
  let report = `# IoT Device Security Report
Generated: ${new Date().toLocaleString()}

## Device Information
- Host: ${host}
- IP Address: ${scanResults.ipAddress || 'Unknown'}

## Security Score
- Score: ${securityAnalysis.securityScore}/100
- Rating: ${getSecurityRating(securityAnalysis.securityScore)}

## Open Ports
`;

  if (scanResults.openPorts.length === 0) {
    report += 'No open ports detected.\n';
  } else {
    scanResults.openPorts.forEach(port => {
      report += `- Port ${port.port}: ${port.service} (${port.description})\n`;
    });
  }

  report += `
## Vulnerabilities
`;

  if (securityAnalysis.vulnerabilities.length === 0) {
    report += 'No vulnerabilities detected.\n';
  } else {
    securityAnalysis.vulnerabilities.forEach(vuln => {
      report += `### ${vuln.name} (${vuln.severity})
- ID: ${vuln.id}
- Description: ${vuln.description}
- Evidence: ${vuln.evidence}

`;
    });
  }

  report += `
## Recommendations
`;

  securityAnalysis.recommendations.forEach((recommendation, index) => {
    report += `${index + 1}. ${recommendation}\n`;
  });

  report += `
## Disclaimer
This scan is for educational purposes only and may not detect all vulnerabilities.
A comprehensive security assessment should include manual testing by security professionals.
`;

  return report;
}

// Get security rating based on score
function getSecurityRating(score) {
  if (score >= 90) return 'Excellent';
  if (score >= 70) return 'Good';
  if (score >= 50) return 'Fair';
  if (score >= 30) return 'Poor';
  return 'Critical';
}

// Display the menu for IoT security scanner
async function menu() {
  console.log(chalk.cyan('\n=== IoT Security Scanner ===\n'));
  console.log(chalk.yellow('This tool provides IoT device security analysis capabilities.'));
  console.log(chalk.red('IMPORTANT: Only use on devices you own or have explicit permission to test.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an IoT security action:',
      choices: [
        'Scan IoT device',
        'Back to main menu'
      ]
    }
  ]);
  
  if (action === 'Back to main menu') {
    return;
  }
  
  // Get target device
  const { targetHost } = await inquirer.prompt([
    {
      type: 'input',
      name: 'targetHost',
      message: 'Enter IoT device IP address or hostname:',
      validate: value => value ? true : 'Please enter a valid IP address or hostname'
    }
  ]);
  
  // Confirm scan
  const { confirmScan } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirmScan',
      message: `This will scan ${targetHost} for security vulnerabilities. Do you have permission to scan this device?`,
      default: false
    }
  ]);
  
  if (!confirmScan) {
    console.log(chalk.yellow('Scan cancelled.'));
    return;
  }
  
  try {
    // Resolve hostname to IP if needed
    let ipAddress = targetHost;
    if (!net.isIP(targetHost)) {
      try {
        ipAddress = await resolveHostname(targetHost);
        console.log(chalk.green(`Resolved ${targetHost} to ${ipAddress}`));
      } catch (error) {
        console.error(chalk.red(`Could not resolve hostname ${targetHost}`));
        return;
      }
    }
    
    // Scan for open ports
    console.log(chalk.yellow(`\nScanning ${targetHost} for open ports...`));
    const openPorts = await scanPorts(targetHost);
    
    console.log(chalk.green('\nOpen Ports:'));
    if (openPorts.length === 0) {
      console.log('No open ports detected');
    } else {
      openPorts.forEach(port => {
        console.log(`Port ${port.port}: ${port.service} (${port.description})`);
      });
    }
    
    // Check for web interfaces
    const webPorts = openPorts.filter(port => ['HTTP', 'HTTPS', 'HTTP Alt', 'HTTPS Alt'].includes(port.service));
    
    let webCredentials = [];
    let webSecurityIssues = [];
    
    if (webPorts.length > 0) {
      console.log(chalk.yellow('\nAnalyzing web interfaces...'));
      
      for (const port of webPorts) {
        const isHttps = port.service.includes('HTTPS');
        
        // Check for default credentials
        try {
          const credentials = await checkDefaultWebCredentials(targetHost, port.port, isHttps);
          webCredentials = [...webCredentials, ...credentials];
        } catch (error) {
          console.error(chalk.red(`Error checking credentials on port ${port.port}:`, error.message));
        }
        
        // Check web security
        try {
          const securityIssues = await checkWebSecurity(targetHost, port.port, isHttps);
          webSecurityIssues = [...webSecurityIssues, ...securityIssues];
        } catch (error) {
          console.error(chalk.red(`Error checking web security on port ${port.port}:`, error.message));
        }
      }
      
      // Display web security findings
      if (webCredentials.length > 0) {
        console.log(chalk.red('\nDefault Credentials Found:'));
        webCredentials.forEach(cred => {
          console.log(`Username: ${cred.username}, Password: ${cred.password}`);
        });
      }
      
      if (webSecurityIssues.length > 0) {
        console.log(chalk.red('\nWeb Security Issues:'));
        webSecurityIssues.forEach(issue => {
          console.log(`- ${issue.issue} (${issue.severity}): ${issue.description}`);
        });
      }
    }
    
    // Analyze overall security
    console.log(chalk.yellow('\nAnalyzing overall device security...'));
    const securityAnalysis = await analyzeIoTSecurity(targetHost, openPorts, webCredentials, webSecurityIssues);
    
    console.log(chalk.green('\nSecurity Analysis:'));
    console.log(`Security Score: ${securityAnalysis.securityScore}/100`);
    console.log(`Rating: ${getSecurityRatingColor(securityAnalysis.securityScore)(getSecurityRating(securityAnalysis.securityScore))}`);
    
    console.log(chalk.red('\nVulnerabilities:'));
    if (securityAnalysis.vulnerabilities.length === 0) {
      console.log('No vulnerabilities detected');
    } else {
      securityAnalysis.vulnerabilities.forEach(vuln => {
        console.log(`- ${vuln.name} (${vuln.severity}): ${vuln.description}`);
      });
    }
    
    console.log(chalk.green('\nRecommendations:'));
    securityAnalysis.recommendations.forEach((recommendation, index) => {
      console.log(`${index + 1}. ${recommendation}`);
    });
    
    // Save report option
    const { saveReport } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'saveReport',
        message: 'Save security analysis report to file?',
        default: true
      }
    ]);
    
    if (saveReport) {
      const reportPath = './iot_security_report.md';
      const scanResults = {
        ipAddress,
        openPorts
      };
      
      const report = generateSecurityReport(targetHost, scanResults, securityAnalysis);
      fs.writeFileSync(reportPath, report);
      console.log(chalk.green(`\nReport saved to ${reportPath}`));
    }
    
  } catch (error) {
    console.error(chalk.red('Error scanning IoT device:'), error.message);
  }
  
  // Ask if the user wants to scan another device
  const { anotherScan } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherScan',
      message: 'Would you like to scan another IoT device?',
      default: true
    }
  ]);
  
  if (anotherScan) {
    await menu();
  }
}

// Helper function to get color based on security score
function getSecurityRatingColor(score) {
  const rating = getSecurityRating(score);
  switch (rating) {
    case 'Excellent':
      return chalk.green;
    case 'Good':
      return chalk.green;
    case 'Fair':
      return chalk.yellow;
    case 'Poor':
      return chalk.red;
    case 'Critical':
      return chalk.red.bold;
    default:
      return chalk.white;
  }
}

module.exports = {
  menu,
  scanPorts,
  checkDefaultWebCredentials,
  checkWebSecurity,
  analyzeIoTSecurity,
  generateSecurityReport
};
