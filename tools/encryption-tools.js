/**
 * Encryption/Decryption Tools
 * 
 * This module provides encryption and decryption capabilities
 * for educational purposes.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const crypto = require('crypto');
const CryptoJS = require('crypto-js');
const fs = require('fs');
const path = require('path');

// Encrypt text using various algorithms
function encryptText(text, algorithm, key) {
  try {
    switch (algorithm) {
      case 'aes-256-cbc':
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key.padEnd(32).slice(0, 32)), iv);
        let encrypted = cipher.update(text);
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        return {
          iv: iv.toString('hex'),
          encryptedData: encrypted.toString('hex')
        };
        
      case 'aes':
        return {
          encryptedData: CryptoJS.AES.encrypt(text, key).toString()
        };
        
      case 'des':
        return {
          encryptedData: CryptoJS.DES.encrypt(text, key).toString()
        };
        
      case 'tripledes':
        return {
          encryptedData: CryptoJS.TripleDES.encrypt(text, key).toString()
        };
        
      case 'rabbit':
        return {
          encryptedData: CryptoJS.Rabbit.encrypt(text, key).toString()
        };
        
      case 'rc4':
        return {
          encryptedData: CryptoJS.RC4.encrypt(text, key).toString()
        };
        
      default:
        throw new Error('Unsupported algorithm');
    }
  } catch (error) {
    throw new Error(`Encryption failed: ${error.message}`);
  }
}

// Decrypt text using various algorithms
function decryptText(encryptedData, algorithm, key, iv) {
  try {
    switch (algorithm) {
      case 'aes-256-cbc':
        const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key.padEnd(32).slice(0, 32)), Buffer.from(iv, 'hex'));
        let decrypted = decipher.update(Buffer.from(encryptedData, 'hex'));
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        return decrypted.toString();
        
      case 'aes':
        const bytes = CryptoJS.AES.decrypt(encryptedData, key);
        return bytes.toString(CryptoJS.enc.Utf8);
        
      case 'des':
        const bytesD = CryptoJS.DES.decrypt(encryptedData, key);
        return bytesD.toString(CryptoJS.enc.Utf8);
        
      case 'tripledes':
        const bytes3D = CryptoJS.TripleDES.decrypt(encryptedData, key);
        return bytes3D.toString(CryptoJS.enc.Utf8);
        
      case 'rabbit':
        const bytesR = CryptoJS.Rabbit.decrypt(encryptedData, key);
        return bytesR.toString(CryptoJS.enc.Utf8);
        
      case 'rc4':
        const bytesRC4 = CryptoJS.RC4.decrypt(encryptedData, key);
        return bytesRC4.toString(CryptoJS.enc.Utf8);
        
      default:
        throw new Error('Unsupported algorithm');
    }
  } catch (error) {
    throw new Error(`Decryption failed: ${error.message}`);
  }
}

// Generate a cryptographic key
function generateKey(type, length) {
  try {
    switch (type) {
      case 'random':
        return crypto.randomBytes(length / 8).toString('hex');
        
      case 'rsa':
        const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
          modulusLength: length,
          publicKeyEncoding: {
            type: 'spki',
            format: 'pem'
          },
          privateKeyEncoding: {
            type: 'pkcs8',
            format: 'pem'
          }
        });
        
        return {
          publicKey,
          privateKey
        };
        
      default:
        throw new Error('Unsupported key type');
    }
  } catch (error) {
    throw new Error(`Key generation failed: ${error.message}`);
  }
}

// Encrypt a file
function encryptFile(filePath, outputPath, algorithm, key) {
  try {
    const data = fs.readFileSync(filePath);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipheriv(algorithm, Buffer.from(key.padEnd(32).slice(0, 32)), iv);
    const encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
    
    const result = Buffer.concat([iv, encrypted]);
    fs.writeFileSync(outputPath, result);
    
    return {
      success: true,
      message: `File encrypted successfully: ${outputPath}`
    };
  } catch (error) {
    throw new Error(`File encryption failed: ${error.message}`);
  }
}

// Decrypt a file
function decryptFile(filePath, outputPath, algorithm, key) {
  try {
    const data = fs.readFileSync(filePath);
    const iv = data.slice(0, 16);
    const encryptedData = data.slice(16);
    
    const decipher = crypto.createDecipheriv(algorithm, Buffer.from(key.padEnd(32).slice(0, 32)), iv);
    const decrypted = Buffer.concat([decipher.update(encryptedData), decipher.final()]);
    
    fs.writeFileSync(outputPath, decrypted);
    
    return {
      success: true,
      message: `File decrypted successfully: ${outputPath}`
    };
  } catch (error) {
    throw new Error(`File decryption failed: ${error.message}`);
  }
}

// Display the menu for encryption tools
async function menu() {
  console.log(chalk.cyan('\n=== Encryption/Decryption Tools ===\n'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an encryption/decryption tool:',
      choices: [
        'Encrypt text',
        'Decrypt text',
        'Generate cryptographic key',
        'Encrypt file',
        'Decrypt file',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Encrypt text':
      const { text, encAlgorithm, encKey } = await inquirer.prompt([
        {
          type: 'input',
          name: 'text',
          message: 'Enter text to encrypt:',
          validate: value => value ? true : 'Please enter text to encrypt'
        },
        {
          type: 'list',
          name: 'encAlgorithm',
          message: 'Select encryption algorithm:',
          choices: ['aes-256-cbc', 'aes', 'des', 'tripledes', 'rabbit', 'rc4']
        },
        {
          type: 'password',
          name: 'encKey',
          message: 'Enter encryption key:',
          mask: '*',
          validate: value => value ? true : 'Please enter an encryption key'
        }
      ]);
      
      try {
        const result = encryptText(text, encAlgorithm, encKey);
        
        console.log(chalk.green('\nEncryption Result:'));
        console.log(`Algorithm: ${encAlgorithm}`);
        
        if (result.iv) {
          console.log(`IV: ${result.iv}`);
        }
        
        console.log(`Encrypted Data: ${result.encryptedData}`);
        
        console.log(chalk.yellow('\nNote: Save this information if you want to decrypt it later.'));
      } catch (error) {
        console.error(chalk.red('Encryption error:'), error.message);
      }
      break;
      
    case 'Decrypt text':
      const { encryptedText, decAlgorithm, decKey, decIv } = await inquirer.prompt([
        {
          type: 'input',
          name: 'encryptedText',
          message: 'Enter encrypted text:',
          validate: value => value ? true : 'Please enter encrypted text'
        },
        {
          type: 'list',
          name: 'decAlgorithm',
          message: 'Select decryption algorithm:',
          choices: ['aes-256-cbc', 'aes', 'des', 'tripledes', 'rabbit', 'rc4']
        },
        {
          type: 'password',
          name: 'decKey',
          message: 'Enter decryption key:',
          mask: '*',
          validate: value => value ? true : 'Please enter a decryption key'
        },
        {
          type: 'input',
          name: 'decIv',
          message: 'Enter IV (only required for aes-256-cbc):',
          when: answers => answers.decAlgorithm === 'aes-256-cbc'
        }
      ]);
      
      try {
        const decrypted = decryptText(encryptedText, decAlgorithm, decKey, decIv);
        
        console.log(chalk.green('\nDecryption Result:'));
        console.log(`Decrypted Text: ${decrypted}`);
      } catch (error) {
        console.error(chalk.red('Decryption error:'), error.message);
      }
      break;
      
    case 'Generate cryptographic key':
      const { keyType, keyLength } = await inquirer.prompt([
        {
          type: 'list',
          name: 'keyType',
          message: 'Select key type:',
          choices: ['random', 'rsa']
        },
        {
          type: 'list',
          name: 'keyLength',
          message: 'Select key length:',
          choices: keyType => keyType === 'random' ? [128, 256, 512, 1024] : [1024, 2048, 4096]
        }
      ]);
      
      try {
        const key = generateKey(keyType, keyLength);
        
        console.log(chalk.green('\nGenerated Key:'));
        
        if (typeof key === 'string') {
          console.log(key);
        } else {
          console.log(chalk.yellow('Public Key:'));
          console.log(key.publicKey);
          console.log(chalk.yellow('\nPrivate Key:'));
          console.log(key.privateKey);
        }
        
        console.log(chalk.yellow('\nNote: Save this key securely if you plan to use it.'));
      } catch (error) {
        console.error(chalk.red('Key generation error:'), error.message);
      }
      break;
      
    case 'Encrypt file':
      console.log(chalk.yellow('Note: This feature requires access to the file system.'));
      console.log(chalk.yellow('For security reasons, only use this on files you own.'));
      
      const { encFilePath, encOutputPath, fileEncAlgorithm, fileEncKey } = await inquirer.prompt([
        {
          type: 'input',
          name: 'encFilePath',
          message: 'Enter path to file to encrypt:',
          validate: value => value ? true : 'Please enter a file path'
        },
        {
          type: 'input',
          name: 'encOutputPath',
          message: 'Enter path for encrypted output file:',
          default: answers => `${answers.encFilePath}.enc`,
          validate: value => value ? true : 'Please enter an output path'
        },
        {
          type: 'list',
          name: 'fileEncAlgorithm',
          message: 'Select encryption algorithm:',
          choices: ['aes-256-cbc', 'aes-192-cbc', 'aes-128-cbc']
        },
        {
          type: 'password',
          name: 'fileEncKey',
          message: 'Enter encryption key:',
          mask: '*',
          validate: value => value ? true : 'Please enter an encryption key'
        }
      ]);
      
      try {
        if (!fs.existsSync(encFilePath)) {
          console.error(chalk.red('File not found:'), encFilePath);
          break;
        }
        
        const result = encryptFile(encFilePath, encOutputPath, fileEncAlgorithm, fileEncKey);
        console.log(chalk.green(result.message));
      } catch (error) {
        console.error(chalk.red('File encryption error:'), error.message);
      }
      break;
      
    case 'Decrypt file':
      console.log(chalk.yellow('Note: This feature requires access to the file system.'));
      console.log(chalk.yellow('For security reasons, only use this on files you own.'));
      
      const { decFilePath, decOutputPath, fileDecAlgorithm, fileDecKey } = await inquirer.prompt([
        {
          type: 'input',
          name: 'decFilePath',
          message: 'Enter path to encrypted file:',
          validate: value => value ? true : 'Please enter a file path'
        },
        {
          type: 'input',
          name: 'decOutputPath',
          message: 'Enter path for decrypted output file:',
          default: answers => answers.decFilePath.replace('.enc', '.dec'),
          validate: value => value ? true : 'Please enter an output path'
        },
        {
          type: 'list',
          name: 'fileDecAlgorithm',
          message: 'Select decryption algorithm:',
          choices: ['aes-256-cbc', 'aes-192-cbc', 'aes-128-cbc']
        },
        {
          type: 'password',
          name: 'fileDecKey',
          message: 'Enter decryption key:',
          mask: '*',
          validate: value => value ? true : 'Please enter a decryption key'
        }
      ]);
      
      try {
        if (!fs.existsSync(decFilePath)) {
          console.error(chalk.red('File not found:'), decFilePath);
          break;
        }
        
        const result = decryptFile(decFilePath, decOutputPath, fileDecAlgorithm, fileDecKey);
        console.log(chalk.green(result.message));
      } catch (error) {
        console.error(chalk.red('File decryption error:'), error.message);
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another encryption action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another encryption/decryption action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

module.exports = {
  menu,
  encryptText,
  decryptText,
  generateKey,
  encryptFile,
  decryptFile
};
