/**
 * Bluetooth Security Toolkit
 * 
 * This module provides tools for analyzing and testing Bluetooth security
 * for educational purposes ONLY.
 * 
 * IMPORTANT: Only use on devices you own or have explicit permission to test.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Check if required tools are installed
async function checkDependencies() {
  const dependencies = {
    hcitool: false,
    bluetoothctl: false,
    l2ping: false,
    gatttool: false
  };
  
  // Check each dependency
  for (const tool of Object.keys(dependencies)) {
    try {
      await new Promise((resolve, reject) => {
        exec(`which ${tool}`, (error, stdout) => {
          if (!error && stdout) {
            dependencies[tool] = true;
          }
          resolve();
        });
      });
    } catch (error) {
      // Ignore errors
    }
  }
  
  return dependencies;
}

// Scan for Bluetooth devices
async function scanForDevices(duration = 10) {
  const spinner = ora('Scanning for Bluetooth devices...').start();
  
  return new Promise((resolve, reject) => {
    // Check if we're on Linux (where hcitool is available)
    if (process.platform === 'linux') {
      exec(`hcitool scan --length=${duration}`, (error, stdout) => {
        if (error) {
          spinner.fail('Failed to scan for Bluetooth devices');
          reject(error);
          return;
        }
        
        const lines = stdout.split('\n').filter(line => line.includes('\t'));
        const devices = [];
        
        lines.forEach(line => {
          const parts = line.trim().split('\t');
          if (parts.length >= 2) {
            devices.push({
              address: parts[0],
              name: parts[1]
            });
          }
        });
        
        spinner.succeed(`Found ${devices.length} Bluetooth devices`);
        resolve(devices);
      });
    } else {
      // Simulate device discovery on non-Linux platforms
      setTimeout(() => {
        spinner.succeed('Bluetooth scan simulation complete');
        
        // Generate some simulated devices
        const simulatedDevices = [
          { address: '00:11:22:33:44:55', name: 'Simulated Headphones' },
          { address: 'AA:BB:CC:DD:EE:FF', name: 'Simulated Speaker' },
          { address: '11:22:33:44:55:66', name: 'Simulated Car Audio' }
        ];
        
        resolve(simulatedDevices);
      }, duration * 1000);
    }
  });
}

// Scan for Bluetooth Low Energy (BLE) devices
async function scanForBLEDevices(duration = 10) {
  const spinner = ora('Scanning for BLE devices...').start();
  
  return new Promise((resolve, reject) => {
    // Check if we're on Linux (where hcitool is available)
    if (process.platform === 'linux') {
      exec(`timeout ${duration} hcitool lescan`, (error, stdout) => {
        if (error && error.code !== 124) { // 124 is the exit code for timeout
          spinner.fail('Failed to scan for BLE devices');
          reject(error);
          return;
        }
        
        const lines = stdout.split('\n').filter(line => line.includes(' '));
        const devices = [];
        
        lines.forEach(line => {
          const parts = line.trim().split(' ');
          if (parts.length >= 2) {
            const address = parts[0];
            const name = parts.slice(1).join(' ');
            
            // Check if device already exists in the array
            const existingDevice = devices.find(d => d.address === address);
            if (!existingDevice) {
              devices.push({
                address,
                name
              });
            }
          }
        });
        
        spinner.succeed(`Found ${devices.length} BLE devices`);
        resolve(devices);
      });
    } else {
      // Simulate BLE device discovery on non-Linux platforms
      setTimeout(() => {
        spinner.succeed('BLE scan simulation complete');
        
        // Generate some simulated devices
        const simulatedDevices = [
          { address: '00:11:22:33:44:55', name: 'Simulated BLE Device' },
          { address: 'AA:BB:CC:DD:EE:FF', name: 'Simulated Fitness Tracker' },
          { address: '11:22:33:44:55:66', name: 'Simulated Smart Lock' }
        ];
        
        resolve(simulatedDevices);
      }, duration * 1000);
    }
  });
}

// Get device information
async function getDeviceInfo(deviceAddress) {
  const spinner = ora(`Getting information for device ${deviceAddress}...`).start();
  
  return new Promise((resolve, reject) => {
    // Check if we're on Linux
    if (process.platform === 'linux') {
      // Use multiple commands to gather information
      const commands = [
        `hcitool info ${deviceAddress}`,
        `hcitool name ${deviceAddress}`,
        `hcitool class ${deviceAddress}`
      ];
      
      const results = {};
      
      // Execute each command
      Promise.all(commands.map(cmd => {
        return new Promise(resolve => {
          exec(cmd, (error, stdout) => {
            resolve({ cmd, output: error ? '' : stdout });
          });
        });
      })).then(outputs => {
        outputs.forEach(({ cmd, output }) => {
          if (cmd.includes('info')) {
            results.info = output;
            
            // Extract BD Address
            const bdAddressMatch = output.match(/BD Address: ([0-9A-F:]+)/);
            if (bdAddressMatch) {
              results.address = bdAddressMatch[1];
            }
            
            // Extract device features
            results.features = [];
            const featuresMatch = output.match(/Features:([^]*?)$/);
            if (featuresMatch) {
              const featuresLines = featuresMatch[1].trim().split('\n');
              featuresLines.forEach(line => {
                if (line.includes('0x')) {
                  results.features.push(line.trim());
                }
              });
            }
          } else if (cmd.includes('name')) {
            results.name = output.trim();
          } else if (cmd.includes('class')) {
            results.deviceClass = output.trim();
          }
        });
        
        spinner.succeed('Device information retrieved');
        resolve(results);
      }).catch(error => {
        spinner.fail('Failed to get device information');
        reject(error);
      });
    } else {
      // Simulate device info on non-Linux platforms
      setTimeout(() => {
        spinner.succeed('Device information retrieved (simulation)');
        
        // Generate simulated device info
        const simulatedInfo = {
          address: deviceAddress,
          name: 'Simulated Device',
          deviceClass: '0x240404',
          features: [
            '0x01 3-slot packets',
            '0x02 5-slot packets',
            '0x04 Encryption',
            '0x08 Slot offset'
          ],
          info: 'Simulated device information'
        };
        
        resolve(simulatedInfo);
      }, 2000);
    }
  });
}

// Test Bluetooth connectivity (ping)
async function testBluetoothConnectivity(deviceAddress, count = 4) {
  const spinner = ora(`Testing connectivity to ${deviceAddress}...`).start();
  
  return new Promise((resolve, reject) => {
    // Check if we're on Linux
    if (process.platform === 'linux') {
      exec(`l2ping -c ${count} ${deviceAddress}`, (error, stdout) => {
        if (error) {
          spinner.fail(`Failed to connect to ${deviceAddress}`);
          reject(error);
          return;
        }
        
        // Parse ping results
        const results = {
          sent: 0,
          received: 0,
          loss: 100,
          time: 0,
          min: 0,
          max: 0,
          avg: 0
        };
        
        // Extract sent/received packets
        const sentReceivedMatch = stdout.match(/(\d+) sent, (\d+) received/);
        if (sentReceivedMatch) {
          results.sent = parseInt(sentReceivedMatch[1]);
          results.received = parseInt(sentReceivedMatch[2]);
          results.loss = 100 - (results.received / results.sent * 100);
        }
        
        // Extract timing information
        const timingMatch = stdout.match(/min\/avg\/max = ([\d.]+)\/([\d.]+)\/([\d.]+)/);
        if (timingMatch) {
          results.min = parseFloat(timingMatch[1]);
          results.avg = parseFloat(timingMatch[2]);
          results.max = parseFloat(timingMatch[3]);
        }
        
        spinner.succeed(`Connectivity test to ${deviceAddress} complete`);
        resolve(results);
      });
    } else {
      // Simulate ping on non-Linux platforms
      setTimeout(() => {
        spinner.succeed('Connectivity test complete (simulation)');
        
        // Generate simulated ping results
        const simulatedResults = {
          sent: count,
          received: Math.floor(count * 0.75), // 75% success rate
          loss: 25,
          time: 1000,
          min: 10.5,
          max: 45.2,
          avg: 22.7
        };
        
        resolve(simulatedResults);
      }, 2000);
    }
  });
}

// Scan for BLE services and characteristics
async function scanBLEServices(deviceAddress) {
  const spinner = ora(`Scanning BLE services for ${deviceAddress}...`).start();
  
  return new Promise((resolve, reject) => {
    // Check if we're on Linux
    if (process.platform === 'linux') {
      // Use gatttool to discover services
      exec(`gatttool -b ${deviceAddress} --primary`, (error, stdout) => {
        if (error) {
          spinner.warn(`Could not discover services for ${deviceAddress}`);
          // Don't reject, just return empty results
          resolve({ services: [] });
          return;
        }
        
        const services = [];
        const lines = stdout.split('\n').filter(line => line.includes('attr handle'));
        
        lines.forEach(line => {
          const match = line.match(/attr handle: (0x[0-9a-f]+), end grp handle: (0x[0-9a-f]+) uuid: ([0-9a-f-]+)/);
          if (match) {
            services.push({
              startHandle: match[1],
              endHandle: match[2],
              uuid: match[3]
            });
          }
        });
        
        spinner.succeed(`Found ${services.length} BLE services`);
        resolve({ services });
      });
    } else {
      // Simulate BLE service discovery on non-Linux platforms
      setTimeout(() => {
        spinner.succeed('BLE service scan complete (simulation)');
        
        // Generate simulated BLE services
        const simulatedServices = [
          { startHandle: '0x0001', endHandle: '0x0005', uuid: '1800', name: 'Generic Access' },
          { startHandle: '0x0006', endHandle: '0x0009', uuid: '1801', name: 'Generic Attribute' },
          { startHandle: '0x000a', endHandle: '0x000e', uuid: '180f', name: 'Battery Service' },
          { startHandle: '0x000f', endHandle: '0x0014', uuid: '1812', name: 'Human Interface Device' }
        ];
        
        resolve({ services: simulatedServices });
      }, 2000);
    }
  });
}

// Analyze Bluetooth security
function analyzeBluetoothSecurity(deviceInfo, connectivityResults, bleServices) {
  const securityAnalysis = {
    deviceAddress: deviceInfo.address,
    deviceName: deviceInfo.name,
    securityIssues: [],
    securityScore: 100, // Start with perfect score and deduct points
    recommendations: []
  };
  
  // Check if device name is default or easily guessable
  const commonNames = ['bluetooth', 'headset', 'speaker', 'car', 'audio'];
  if (deviceInfo.name) {
    const lowerName = deviceInfo.name.toLowerCase();
    if (commonNames.some(name => lowerName.includes(name))) {
      securityAnalysis.securityIssues.push('Device uses a common or default name');
      securityAnalysis.securityScore -= 10;
      securityAnalysis.recommendations.push('Change the device name to something unique');
    }
  }
  
  // Check for encryption support
  let hasEncryption = false;
  if (deviceInfo.features) {
    hasEncryption = deviceInfo.features.some(feature => feature.includes('Encryption'));
    if (!hasEncryption) {
      securityAnalysis.securityIssues.push('Device does not support encryption');
      securityAnalysis.securityScore -= 30;
      securityAnalysis.recommendations.push('Use only devices that support Bluetooth encryption');
    }
  }
  
  // Check connectivity results
  if (connectivityResults) {
    if (connectivityResults.loss < 25) {
      securityAnalysis.securityIssues.push('Device is highly responsive to connection attempts');
      securityAnalysis.securityScore -= 5;
    }
  }
  
  // Check BLE services
  if (bleServices && bleServices.services) {
    // Check for sensitive services
    const sensitiveServiceUUIDs = ['1800', '1801', '180f', '1812'];
    const hasSensitiveServices = bleServices.services.some(service => 
      sensitiveServiceUUIDs.includes(service.uuid)
    );
    
    if (hasSensitiveServices) {
      securityAnalysis.securityIssues.push('Device exposes sensitive BLE services');
      securityAnalysis.securityScore -= 15;
      securityAnalysis.recommendations.push('Configure BLE services to require authentication');
    }
  }
  
  // Add general recommendations
  securityAnalysis.recommendations.push('Keep device firmware updated');
  securityAnalysis.recommendations.push('Pair devices in private locations');
  securityAnalysis.recommendations.push('Use complex PIN codes when pairing');
  securityAnalysis.recommendations.push('Disable Bluetooth when not in use');
  
  // Determine overall security rating
  if (securityAnalysis.securityScore >= 80) {
    securityAnalysis.overallRating = 'Good';
  } else if (securityAnalysis.securityScore >= 60) {
    securityAnalysis.overallRating = 'Moderate';
  } else {
    securityAnalysis.overallRating = 'Poor';
  }
  
  return securityAnalysis;
}

// Display the menu for Bluetooth security toolkit
async function menu() {
  console.log(chalk.cyan('\n=== Bluetooth Security Toolkit ===\n'));
  console.log(chalk.yellow('This tool provides Bluetooth security analysis capabilities.'));
  console.log(chalk.red('IMPORTANT: Only use on devices you own or have explicit permission to test.'));
  
  // Check dependencies
  const dependencies = await checkDependencies();
  const missingDependencies = Object.keys(dependencies).filter(tool => !dependencies[tool]);
  
  if (missingDependencies.length > 0 && process.platform === 'linux') {
    console.log(chalk.yellow('\nWarning: Some Bluetooth tools are not installed:'));
    missingDependencies.forEach(tool => {
      console.log(`- ${tool}`);
    });
    console.log(chalk.yellow('\nSome features may be limited or simulated.'));
    console.log(chalk.yellow('On Ubuntu/Debian, install missing tools with:'));
    console.log('sudo apt install bluez bluez-tools');
  }
  
  if (process.platform !== 'linux') {
    console.log(chalk.yellow('\nNote: Full Bluetooth analysis requires Linux.'));
    console.log(chalk.yellow('Running in simulation mode on this platform.'));
  }
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a Bluetooth security action:',
      choices: [
        'Scan for Bluetooth devices',
        'Scan for BLE devices',
        'Analyze Bluetooth device security',
        'Back to main menu'
      ]
    }
  ]);
  
  if (action === 'Back to main menu') {
    return;
  }
  
  try {
    switch (action) {
      case 'Scan for Bluetooth devices':
        const { scanDuration } = await inquirer.prompt([
          {
            type: 'number',
            name: 'scanDuration',
            message: 'Enter scan duration in seconds:',
            default: 10,
            validate: value => value > 0 ? true : 'Duration must be greater than 0'
          }
        ]);
        
        const devices = await scanForDevices(scanDuration);
        
        console.log(chalk.green('\nDiscovered Bluetooth Devices:'));
        if (devices.length === 0) {
          console.log('No devices found');
        } else {
          devices.forEach((device, index) => {
            console.log(`${index + 1}. ${device.name || 'Unknown'} (${device.address})`);
          });
        }
        break;
        
      case 'Scan for BLE devices':
        const { bleScanDuration } = await inquirer.prompt([
          {
            type: 'number',
            name: 'bleScanDuration',
            message: 'Enter scan duration in seconds:',
            default: 10,
            validate: value => value > 0 ? true : 'Duration must be greater than 0'
          }
        ]);
        
        const bleDevices = await scanForBLEDevices(bleScanDuration);
        
        console.log(chalk.green('\nDiscovered BLE Devices:'));
        if (bleDevices.length === 0) {
          console.log('No BLE devices found');
        } else {
          bleDevices.forEach((device, index) => {
            console.log(`${index + 1}. ${device.name || 'Unknown'} (${device.address})`);
          });
        }
        break;
        
      case 'Analyze Bluetooth device security':
        // First scan for devices
        console.log(chalk.yellow('Scanning for nearby Bluetooth devices...'));
        const nearbyDevices = await scanForDevices(5);
        const nearbyBLEDevices = await scanForBLEDevices(5);
        
        // Combine both lists
        const allDevices = [...nearbyDevices];
        nearbyBLEDevices.forEach(bleDevice => {
          if (!allDevices.some(device => device.address === bleDevice.address)) {
            allDevices.push(bleDevice);
          }
        });
        
        if (allDevices.length === 0) {
          console.log(chalk.yellow('No Bluetooth devices found to analyze.'));
          break;
        }
        
        // Let user select a device
        const { selectedDeviceIndex } = await inquirer.prompt([
          {
            type: 'list',
            name: 'selectedDeviceIndex',
            message: 'Select a device to analyze:',
            choices: allDevices.map((device, index) => ({
              name: `${device.name || 'Unknown'} (${device.address})`,
              value: index
            }))
          }
        ]);
        
        const selectedDevice = allDevices[selectedDeviceIndex];
        
        // Get device information
        const deviceInfo = await getDeviceInfo(selectedDevice.address);
        
        // Test connectivity
        const connectivityResults = await testBluetoothConnectivity(selectedDevice.address);
        
        // Scan for BLE services
        const bleServices = await scanBLEServices(selectedDevice.address);
        
        // Analyze security
        const securityAnalysis = analyzeBluetoothSecurity(deviceInfo, connectivityResults, bleServices);
        
        // Display results
        console.log(chalk.green('\nBluetooth Security Analysis:'));
        console.log(`Device: ${securityAnalysis.deviceName || 'Unknown'} (${securityAnalysis.deviceAddress})`);
        console.log(`Security Score: ${securityAnalysis.securityScore}/100`);
        console.log(`Overall Rating: ${getSecurityRatingColor(securityAnalysis.overallRating)(securityAnalysis.overallRating)}`);
        
        console.log(chalk.yellow('\nSecurity Issues:'));
        if (securityAnalysis.securityIssues.length === 0) {
          console.log('No specific security issues detected');
        } else {
          securityAnalysis.securityIssues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
          });
        }
        
        console.log(chalk.green('\nRecommendations:'));
        securityAnalysis.recommendations.forEach((recommendation, index) => {
          console.log(`${index + 1}. ${recommendation}`);
        });
        
        // Save report option
        const { saveReport } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'saveReport',
            message: 'Save security analysis report to file?',
            default: false
          }
        ]);
        
        if (saveReport) {
          const reportPath = './bluetooth_security_report.txt';
          
          let report = `Bluetooth Security Analysis Report\n`;
          report += `Generated: ${new Date().toLocaleString()}\n\n`;
          report += `Device: ${securityAnalysis.deviceName || 'Unknown'} (${securityAnalysis.deviceAddress})\n`;
          report += `Security Score: ${securityAnalysis.securityScore}/100\n`;
          report += `Overall Rating: ${securityAnalysis.overallRating}\n\n`;
          
          report += `Security Issues:\n`;
          if (securityAnalysis.securityIssues.length === 0) {
            report += `- No specific security issues detected\n`;
          } else {
            securityAnalysis.securityIssues.forEach((issue, index) => {
              report += `${index + 1}. ${issue}\n`;
            });
          }
          
          report += `\nRecommendations:\n`;
          securityAnalysis.recommendations.forEach((recommendation, index) => {
            report += `${index + 1}. ${recommendation}\n`;
          });
          
          fs.writeFileSync(reportPath, report);
          console.log(chalk.green(`\nReport saved to ${reportPath}`));
        }
        
        break;
    }
  } catch (error) {
    console.error(chalk.red('Error:'), error.message);
  }
  
  // Ask if the user wants to perform another Bluetooth security action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another Bluetooth security action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

// Helper function to get color based on security rating
function getSecurityRatingColor(rating) {
  switch (rating) {
    case 'Good':
      return chalk.green;
    case 'Moderate':
      return chalk.yellow;
    case 'Poor':
      return chalk.red;
    default:
      return chalk.white;
  }
}

module.exports = {
  menu,
  scanForDevices,
  scanForBLEDevices,
  getDeviceInfo,
  testBluetoothConnectivity,
  scanBLEServices,
  analyzeBluetoothSecurity
};
