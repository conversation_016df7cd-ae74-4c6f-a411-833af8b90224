/**
 * Wireless Network Analyzer Tool
 * 
 * This module provides wireless network analysis capabilities
 * for educational purposes. ONLY use on networks you own or
 * have explicit permission to analyze.
 */

const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const { exec } = require('child_process');
const wifi = require('wifi-scanner');
const os = require('os');

// Scan for wireless networks
async function scanWirelessNetworks() {
  const spinner = ora('Scanning for wireless networks...').start();
  
  return new Promise((resolve, reject) => {
    wifi.scan((err, networks) => {
      if (err) {
        spinner.fail('Failed to scan wireless networks');
        reject(err);
        return;
      }
      
      spinner.succeed('Wireless network scan complete');
      resolve(networks);
    });
  });
}

// Get detailed information about a specific wireless network
function getWirelessNetworkDetails(ssid) {
  const spinner = ora(`Getting details for network: ${ssid}...`).start();
  
  return new Promise((resolve, reject) => {
    if (process.platform === 'darwin') {
      exec(`/System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport -I`, (error, stdout) => {
        if (error) {
          spinner.fail(`Failed to get details for network: ${ssid}`);
          reject(error);
          return;
        }
        
        const lines = stdout.split('\n');
        const details = {};
        
        lines.forEach(line => {
          const parts = line.split(':');
          if (parts.length === 2) {
            details[parts[0].trim()] = parts[1].trim();
          }
        });
        
        spinner.succeed(`Details for network: ${ssid} retrieved`);
        resolve(details);
      });
    } else if (process.platform === 'win32') {
      exec(`netsh wlan show network name="${ssid}" mode=Bssid`, (error, stdout) => {
        if (error) {
          spinner.fail(`Failed to get details for network: ${ssid}`);
          reject(error);
          return;
        }
        
        spinner.succeed(`Details for network: ${ssid} retrieved`);
        resolve(stdout);
      });
    } else {
      exec(`iwconfig`, (error, stdout) => {
        if (error) {
          spinner.fail(`Failed to get details for network: ${ssid}`);
          reject(error);
          return;
        }
        
        spinner.succeed(`Details for network: ${ssid} retrieved`);
        resolve(stdout);
      });
    }
  });
}

// Analyze wireless network security
function analyzeWirelessSecurity(network) {
  let securityRating = 'Unknown';
  let vulnerabilities = [];
  let recommendations = [];
  
  // Check encryption type
  if (!network.security || network.security === 'OPEN') {
    securityRating = 'Very Poor';
    vulnerabilities.push('Network has no encryption');
    recommendations.push('Enable WPA2 or WPA3 encryption');
  } else if (network.security.includes('WEP')) {
    securityRating = 'Poor';
    vulnerabilities.push('WEP encryption is easily crackable');
    recommendations.push('Upgrade to WPA2 or WPA3 encryption');
  } else if (network.security.includes('WPA-PSK')) {
    securityRating = 'Moderate';
    vulnerabilities.push('WPA1 is vulnerable to TKIP attacks');
    recommendations.push('Upgrade to WPA2 or WPA3 encryption');
  } else if (network.security.includes('WPA2-PSK')) {
    securityRating = 'Good';
    vulnerabilities.push('Potentially vulnerable to KRACK attacks if not patched');
    recommendations.push('Ensure router firmware is updated');
    recommendations.push('Consider upgrading to WPA3 if available');
  } else if (network.security.includes('WPA3')) {
    securityRating = 'Excellent';
    recommendations.push('Continue to keep router firmware updated');
  }
  
  // Check signal strength for potential distance-based attacks
  if (network.signal && network.signal > -50) {
    vulnerabilities.push('Strong signal may extend beyond premises');
    recommendations.push('Consider reducing transmit power if possible');
  }
  
  return {
    securityRating,
    vulnerabilities,
    recommendations
  };
}

// Display the menu for wireless analysis tools
async function menu() {
  console.log(chalk.cyan('\n=== Wireless Network Analysis Tools ===\n'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a wireless analysis tool:',
      choices: [
        'Scan for wireless networks',
        'Get current wireless network details',
        'Analyze wireless network security',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Scan for wireless networks':
      const { confirmScan } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmScan',
          message: 'This will scan for nearby wireless networks. Do you have permission to perform this scan?',
          default: false
        }
      ]);
      
      if (confirmScan) {
        try {
          const networks = await scanWirelessNetworks();
          
          console.log(chalk.green('\nWireless Networks:'));
          console.log(chalk.yellow('SSID\t\t\tBSSID\t\t\tChannel\tSignal\tSecurity'));
          
          networks.forEach(network => {
            console.log(`${(network.ssid || 'Hidden').padEnd(15)}\t${network.mac || 'N/A'}\t${network.channel || 'N/A'}\t${network.signal || 'N/A'}\t${network.security || 'OPEN'}`);
          });
          
          console.log(chalk.yellow(`\nTotal networks: ${networks.length}`));
        } catch (error) {
          console.error(chalk.red('Error scanning wireless networks:'), error);
        }
      } else {
        console.log(chalk.yellow('Wireless network scan cancelled.'));
      }
      break;
      
    case 'Get current wireless network details':
      try {
        const interfaces = os.networkInterfaces();
        let currentWifi = null;
        
        // Find the active wireless interface
        for (const name of Object.keys(interfaces)) {
          if ((name.includes('wlan') || name.includes('wifi') || name.includes('en0')) && 
              interfaces[name].some(iface => iface.family === 'IPv4' && !iface.internal)) {
            currentWifi = name;
            break;
          }
        }
        
        if (!currentWifi) {
          console.log(chalk.yellow('No active wireless connection detected.'));
          break;
        }
        
        const details = await getWirelessNetworkDetails('current');
        
        console.log(chalk.green('\nCurrent Wireless Network Details:'));
        
        if (typeof details === 'string') {
          console.log(details);
        } else {
          Object.keys(details).forEach(key => {
            console.log(`${key}: ${details[key]}`);
          });
        }
      } catch (error) {
        console.error(chalk.red('Error getting wireless network details:'), error);
      }
      break;
      
    case 'Analyze wireless network security':
      try {
        const networks = await scanWirelessNetworks();
        
        if (networks.length === 0) {
          console.log(chalk.yellow('No wireless networks found to analyze.'));
          break;
        }
        
        const { selectedNetwork } = await inquirer.prompt([
          {
            type: 'list',
            name: 'selectedNetwork',
            message: 'Select a network to analyze:',
            choices: networks.map(network => ({
              name: network.ssid || 'Hidden Network',
              value: network
            }))
          }
        ]);
        
        const analysis = analyzeWirelessSecurity(selectedNetwork);
        
        console.log(chalk.green('\nWireless Security Analysis:'));
        console.log(`Network: ${selectedNetwork.ssid || 'Hidden Network'}`);
        console.log(`Security Type: ${selectedNetwork.security || 'OPEN'}`);
        console.log(`Security Rating: ${getSecurityRatingColor(analysis.securityRating)(analysis.securityRating)}`);
        
        console.log(chalk.yellow('\nPotential Vulnerabilities:'));
        if (analysis.vulnerabilities.length > 0) {
          analysis.vulnerabilities.forEach(vuln => console.log(`- ${vuln}`));
        } else {
          console.log('No obvious vulnerabilities detected');
        }
        
        console.log(chalk.green('\nRecommendations:'));
        analysis.recommendations.forEach(rec => console.log(`- ${rec}`));
        
      } catch (error) {
        console.error(chalk.red('Error analyzing wireless network:'), error);
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another wireless analysis action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another wireless analysis action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

// Helper function to get color based on security rating
function getSecurityRatingColor(rating) {
  switch (rating) {
    case 'Very Poor':
      return chalk.red.bold;
    case 'Poor':
      return chalk.red;
    case 'Moderate':
      return chalk.yellow;
    case 'Good':
      return chalk.green;
    case 'Excellent':
      return chalk.green.bold;
    default:
      return chalk.white;
  }
}

module.exports = {
  menu,
  scanWirelessNetworks,
  getWirelessNetworkDetails,
  analyzeWirelessSecurity
};
