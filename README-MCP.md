# MCP Server Ecosystem

A comprehensive Model Context Protocol (MCP) server ecosystem providing specialized tools for development, security, data processing, and system management.

## 🌟 Overview

This project implements a complete MCP server ecosystem with 8+ specialized servers, each providing distinct functionality through the Model Context Protocol. The ecosystem is designed for scalability, security, and ease of integration with AI applications.

## 🏗️ Architecture

```
MCP Ecosystem
├── Core Infrastructure
│   ├── Base Server Framework
│   ├── Server Registry
│   └── Type Definitions
├── Specialized Servers (8+)
│   ├── Code Analysis Server
│   ├── Git Operations Server
│   ├── System Monitoring Server
│   ├── File Manipulation Server
│   ├── Vulnerability Scanning Server
│   ├── Data Validation Server
│   ├── Log Analysis Server
│   └── Project Management Server
└── Integration Layer
    ├── CLI Interface
    ├── Configuration Management
    └── Orchestration Tools
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- TypeScript 5.0+
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd hack-tools

# Install dependencies
npm install

# Build the project
npm run build

# Start the MCP ecosystem
npm run mcp:start
```

### Basic Usage

```bash
# List all available servers
node dist/mcp/index.js list

# Start all servers
node dist/mcp/index.js start

# Start a specific server
node dist/mcp/index.js start "Code Analysis"

# Get server information
node dist/mcp/index.js info "Code Analysis"

# Run a server in standalone mode
node dist/mcp/index.js run "System Monitoring"
```

## 🛠️ Available Servers

### 1. Code Analysis Server
**Purpose**: Static code analysis, security scanning, and quality metrics

**Key Features**:
- Multi-language code analysis (JS, TS, Python, Java, C++, etc.)
- Security vulnerability detection
- Code quality metrics and complexity analysis
- Dependency analysis
- Comprehensive reporting

**Tools**:
- `analyze-code`: Perform comprehensive static code analysis
- `scan-security`: Scan for security vulnerabilities
- `calculate-metrics`: Calculate code quality metrics
- `analyze-dependencies`: Analyze project dependencies
- `generate-report`: Generate analysis reports

### 2. Git Operations Server
**Purpose**: Comprehensive Git repository management and analysis

**Key Features**:
- Repository status and history analysis
- Branch management operations
- Commit analysis and search
- Diff analysis and visualization
- Repository statistics

**Tools**:
- `git-status`: Get repository status
- `git-history`: Retrieve commit history
- `git-branches`: List and manage branches
- `git-diff`: Generate diffs between commits/branches
- `git-stats`: Repository statistics and analytics

### 3. System Monitoring Server
**Purpose**: Real-time system performance monitoring and alerts

**Key Features**:
- CPU, memory, and disk monitoring
- Process management
- Network statistics
- Performance metrics collection
- Alert generation and thresholds

**Tools**:
- `get-system-info`: System information
- `get-metrics`: Current performance metrics
- `get-processes`: Running processes
- `start-monitoring`: Continuous monitoring
- `get-health-check`: System health assessment

### 4. File Manipulation Server
**Purpose**: Secure file and directory operations with access controls

**Key Features**:
- CRUD operations for files and directories
- Advanced search and filtering
- Batch operations
- Security controls and path restrictions
- Format conversion

**Tools**:
- `read-file`: Read file contents
- `write-file`: Write to files
- `list-directory`: Directory listings
- `search-files`: Advanced file search
- `batch-operation`: Bulk file operations

### 5. Vulnerability Scanning Server
**Purpose**: Comprehensive vulnerability scanning and security assessment

**Key Features**:
- Network vulnerability scanning
- Web application security testing
- Dependency vulnerability analysis
- Configuration security assessment
- Compliance checking

**Tools**:
- `network-scan`: Network vulnerability scan
- `webapp-scan`: Web application security scan
- `dependency-scan`: Dependency vulnerability analysis
- `config-scan`: Configuration security assessment
- `compliance-check`: Compliance framework checking

### 6. Data Validation Server
**Purpose**: Data validation, quality assessment, and profiling

**Key Features**:
- Schema validation (JSON, XML, CSV)
- Data quality metrics
- Data profiling and analysis
- Format conversion
- Custom validation rules

**Tools**:
- `validate-json`: JSON data validation
- `validate-csv`: CSV data validation
- `profile-data`: Data profiling
- `create-schema`: Schema generation
- `data-quality`: Quality assessment

### 7. Log Analysis Server
**Purpose**: Log parsing, pattern recognition, and security monitoring

**Key Features**:
- Multi-format log parsing
- Pattern recognition and anomaly detection
- Security event analysis
- Performance metrics extraction
- Real-time monitoring

**Tools**:
- `analyze-logs`: Comprehensive log analysis
- `parse-logs`: Structure log data
- `detect-anomalies`: Anomaly detection
- `security-analysis`: Security event analysis
- `search-logs`: Advanced log search

### 8. Project Management Server
**Purpose**: Project planning, task management, and team collaboration

**Key Features**:
- Task and milestone tracking
- Team collaboration tools
- Progress monitoring and reporting
- Resource allocation
- Risk assessment

**Tools**:
- `create-project`: Create new projects
- `create-task`: Task management
- `create-milestone`: Milestone tracking
- `generate-report`: Project reporting
- `project-analytics`: Analytics and insights

## 🔧 Configuration

### Environment Variables

```bash
# MCP Server Configuration
MCP_ALLOWED_PATHS=/path1,/path2,/path3  # Allowed file system paths
MCP_LOG_LEVEL=info                       # Logging level
MCP_PORT=3000                           # Server port (if applicable)

# Security Configuration
MCP_API_KEY=your-api-key                # API key for authentication
MCP_ENABLE_AUTH=true                    # Enable authentication

# Performance Configuration
MCP_MAX_CONCURRENT_REQUESTS=10          # Max concurrent requests
MCP_REQUEST_TIMEOUT=30000               # Request timeout (ms)
```

### Server-Specific Configuration

Each server can be configured individually through configuration files or environment variables. See individual server documentation for details.

## 🔒 Security Features

- **Access Control**: Path-based restrictions for file operations
- **Authentication**: API key and token-based authentication
- **Authorization**: Role-based access control
- **Rate Limiting**: Configurable rate limits per tool
- **Audit Logging**: Comprehensive audit trails
- **Input Validation**: Strict input validation and sanitization

## 📊 Monitoring and Observability

- **Health Checks**: Built-in health monitoring for all servers
- **Metrics Collection**: Performance and usage metrics
- **Logging**: Structured logging with multiple levels
- **Event Tracking**: Server lifecycle and operation events
- **Status Dashboard**: Real-time server status display

## 🧪 Testing

```bash
# Run all tests
npm test

# Run MCP-specific tests
npm run test:mcp

# Run tests for a specific server
npm test -- --testPathPattern=code-analysis
```

## 📚 API Documentation

### Tool Schema

All tools follow the MCP specification with Zod schema validation:

```typescript
interface MCPTool {
  name: string;
  description: string;
  inputSchema: z.ZodSchema;
  handler: ToolHandler;
  category?: string;
  tags?: string[];
  security?: ToolSecurity;
}
```

### Resource Access

Resources are accessible via URI patterns:

```
file://{filePath}              # File content access
project://{projectId}          # Project information
logs://analysis/{analysisId}   # Log analysis results
system://metrics/current       # Current system metrics
```

### Prompt Templates

Servers provide prompt templates for common use cases:

```typescript
// Code review prompt
{
  name: 'code-review',
  arguments: [
    { name: 'directory', required: true },
    { name: 'focus', required: false }
  ]
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

### Adding New Servers

1. Create server class extending `BaseMCPServer`
2. Implement required tools, resources, and prompts
3. Add server to the registry in `src/mcp/index.ts`
4. Add tests and documentation
5. Update this README

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: See individual server README files
- **Issues**: Report bugs and feature requests via GitHub issues
- **Discussions**: Join community discussions for questions and ideas

## 🗺️ Roadmap

### Phase 1 (Current)
- ✅ Core infrastructure and 8 specialized servers
- ✅ CLI interface and orchestration
- ✅ Basic security and monitoring

### Phase 2 (Planned)
- 🔄 Additional specialized servers (Network Analysis, AI/ML Tools)
- 🔄 Web dashboard for server management
- 🔄 Advanced analytics and reporting
- 🔄 Integration with existing MCP servers

### Phase 3 (Future)
- 📋 Plugin system for custom servers
- 📋 Distributed server deployment
- 📋 Advanced AI integration features
- 📋 Enterprise security features

## 🏆 Acknowledgments

- Model Context Protocol specification and community
- Open source libraries and tools used in this project
- Contributors and maintainers

---

**Built with ❤️ for the MCP community**
