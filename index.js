#!/usr/bin/env node

const { program } = require("commander");
const chalk = require("chalk");
const inquirer = require("inquirer");
const fs = require("fs");
const path = require("path");

// Banner display
console.log(
  chalk.cyan(`
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ███████╗███████╗ ██████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██╔════╝██╔════╝██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝███████╗█████╗  ██║
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗╚════██║██╔══╝  ██║
╚██████╗   ██║   ██████╔╝███████╗██║  ██║███████║███████╗╚██████╗
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝╚══════╝╚══════╝ ╚═════╝

████████╗ ██████╗  ██████╗ ██╗     ██╗  ██╗██╗████████╗
╚══██╔══╝██╔═══██╗██╔═══██╗██║     ██║ ██╔╝██║╚══██╔══╝
   ██║   ██║   ██║██║   ██║██║     █████╔╝ ██║   ██║
   ██║   ██║   ██║██║   ██║██║     ██╔═██╗ ██║   ██║
   ██║   ╚██████╔╝╚██████╔╝███████╗██║  ██╗██║   ██║
   ╚═╝    ╚═════╝  ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝   ╚═╝
`)
);

console.log(
  chalk.yellow("Educational Cybersecurity Toolkit - For authorized use only\n")
);

// Legal disclaimer
console.log(chalk.red.bold("LEGAL DISCLAIMER:"));
console.log(chalk.red("This toolkit is for EDUCATIONAL PURPOSES ONLY."));
console.log(
  chalk.red(
    "Only use these tools on systems you own or have explicit permission to test."
  )
);
console.log(
  chalk.red("Unauthorized use may be illegal and result in criminal charges.\n")
);

// Version
program.version("1.0.0");

// Import tool modules
const networkScanner = require("./tools/network-scanner");
const passwordAnalyzer = require("./tools/password-analyzer");
const systemInfo = require("./tools/system-info");
const wirelessAnalyzer = require("./tools/wireless-analyzer");
const encryptionTools = require("./tools/encryption-tools");
const packetAnalyzer = require("./tools/packet-analyzer");
const socialEngineering = require("./tools/social-engineering");
const aiTools = require("./tools/ai-tools");
const mobileAnalyzer = require("./tools/mobile-analyzer");
const webAppScanner = require("./tools/web-app-scanner");
const steganographyTools = require("./tools/steganography-tools");
const bluetoothToolkit = require("./tools/bluetooth-toolkit");
const iotScanner = require("./tools/iot-scanner");

// Main menu function
async function mainMenu() {
  const { category } = await inquirer.prompt([
    {
      type: "list",
      name: "category",
      message: "Select a tool category:",
      choices: [
        "Network Scanning",
        "Password Analysis",
        "System Information",
        "Wireless Analysis",
        "Encryption/Decryption",
        "Packet Analysis",
        "Social Engineering Education",
        "AI-Powered Tools",
        "Mobile Device Analyzer",
        "Web Application Scanner",
        "Steganography Tools",
        "Bluetooth Security Toolkit",
        "IoT Security Scanner",
        "Exit",
      ],
    },
  ]);

  switch (category) {
    case "Network Scanning":
      await networkScanner.menu();
      break;
    case "Password Analysis":
      await passwordAnalyzer.menu();
      break;
    case "System Information":
      await systemInfo.menu();
      break;
    case "Wireless Analysis":
      await wirelessAnalyzer.menu();
      break;
    case "Encryption/Decryption":
      await encryptionTools.menu();
      break;
    case "Packet Analysis":
      await packetAnalyzer.menu();
      break;
    case "Social Engineering Education":
      await socialEngineering.menu();
      break;
    case "AI-Powered Tools":
      await aiTools.menu();
      break;
    case "Mobile Device Analyzer":
      await mobileAnalyzer.menu();
      break;
    case "Web Application Scanner":
      await webAppScanner.menu();
      break;
    case "Steganography Tools":
      await steganographyTools.menu();
      break;
    case "Bluetooth Security Toolkit":
      await bluetoothToolkit.menu();
      break;
    case "IoT Security Scanner":
      await iotScanner.menu();
      break;
    case "Exit":
      console.log(chalk.green("Thank you for using CyberSec Toolkit!"));
      process.exit(0);
  }

  // Return to main menu after tool execution
  await mainMenu();
}

// Check if tools directory exists, if not create it
const toolsDir = path.join(__dirname, "tools");
if (!fs.existsSync(toolsDir)) {
  fs.mkdirSync(toolsDir);
}

// Start the application
mainMenu().catch((err) => {
  console.error(chalk.red("An error occurred:"), err);
  process.exit(1);
});
