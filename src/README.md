# CyberSec Toolkit - TypeScript Version

This directory contains the TypeScript implementation of the CyberSec Toolkit, with a comprehensive collection of cybersecurity and hacking tools for educational purposes.

## Setup Instructions

1. Install TypeScript and required dependencies:

```bash
npm install --save-dev typescript @types/node @types/inquirer @types/chalk @types/crypto-js
```

2. Install runtime dependencies:

```bash
npm install inquirer chalk ora crypto-js express socket.io axios systeminformation
```

3. Add TypeScript compilation scripts to your package.json:

```json
"scripts": {
  "start": "node index.js",
  "test": "jest",
  "build": "tsc",
  "dev": "tsc --watch"
}
```

4. Compile the TypeScript code:

```bash
npm run build
```

5. Run the application:

```bash
node dist/index.js
```

## Comprehensive Hacking Tools Collection

This toolkit includes a comprehensive collection of hacking tools for educational purposes:

### Network & Web Tools

- **Network Scanner** - Discover and analyze devices on a network
- **Web Application Scanner** - Identify vulnerabilities in web applications
- **Packet Analyzer** - Capture and analyze network traffic
- **IoT Scanner** - Discover and analyze IoT devices

### Wireless Tools

- **Wireless Analyzer** - Analyze WiFi networks and security
- **Bluetooth Toolkit** - Analyze Bluetooth devices and security

### System & Mobile Tools

- **System Information** - Gather detailed system information
- **Mobile Device Analyzer** - Analyze mobile device security
- **Privilege Escalation** - Identify privilege escalation vulnerabilities

### Cryptography & Password Tools

- **Password Analyzer** - Analyze password strength and security
- **Encryption/Decryption Tools** - Basic encryption and decryption utilities
- **Advanced Cryptography** - Advanced cryptographic tools and cryptanalysis

### Exploitation & Reverse Engineering

- **Exploit Framework** - Framework for managing and executing exploits
- **Reverse Engineering** - Tools for analyzing binaries and applications
- **Digital Forensics** - Tools for digital forensic investigation

### Stealth & Social Engineering

- **Steganography Tools** - Hide and extract hidden data
- **Social Engineering** - Educational tools about social engineering techniques

### AI-Powered Tools

- **AI Password Cracking** - Simulate AI-powered password cracking
- **AI Network Anomaly Detection** - Detect unusual network behavior
- **AI Phishing Email Generator & Detector** - Generate and detect phishing emails
- **AI Vulnerability Scanner** - Scan for vulnerabilities using AI
- **AI Malware Behavior Analyzer** - Analyze malware behavior using AI
- **AI Traffic Pattern Analyzer** - Analyze network traffic patterns
- **AI Social Engineering Detector** - Detect social engineering attempts
- **AI Code Vulnerability Scanner** - Scan code for vulnerabilities
- **AI OSINT Analyzer** - Gather and analyze open-source intelligence
- **AI Deepfake Detector** - Detect potentially manipulated media
- **AI Threat Intelligence Analyzer** - Analyze threat intelligence feeds

## Project Structure

```
src/
├── index.ts                  # Main application entry point
├── tools/                    # Tools directory
│   ├── ai-tools/             # AI-powered tools
│   │   ├── index.ts          # AI tools index
│   │   ├── password-cracker.ts
│   │   ├── anomaly-detector.ts
│   │   ├── phishing-analyzer.ts
│   │   ├── vulnerability-scanner.ts
│   │   ├── malware-analyzer.ts
│   │   ├── traffic-analyzer.ts
│   │   ├── social-engineering-detector.ts
│   │   ├── code-vulnerability-scanner.ts
│   │   ├── osint-analyzer.ts
│   │   ├── deepfake-detector.ts
│   │   └── threat-intelligence-analyzer.ts
│   ├── network-scanner.ts
│   ├── password-analyzer.ts
│   ├── system-info.ts
│   ├── wireless-analyzer.ts
│   ├── encryption-tools.ts
│   ├── packet-analyzer.ts
│   ├── social-engineering.ts
│   ├── mobile-analyzer.ts
│   ├── web-app-scanner.ts
│   ├── steganography-tools.ts
│   ├── bluetooth-toolkit.ts
│   ├── iot-scanner.ts
│   ├── exploit-framework.ts
│   ├── reverse-engineering.ts
│   ├── digital-forensics.ts
│   ├── advanced-cryptography.ts
│   └── privilege-escalation.ts
```

## New Advanced Hacking Tools

### Exploit Framework

- Manage and execute exploits against known vulnerabilities
- Search for exploits by CVE, target system, or keyword
- Simulate exploit execution with detailed results
- View mitigation strategies for each vulnerability

### Reverse Engineering

- Analyze binary files for security vulnerabilities
- Decompile binaries to source code for analysis
- Identify potential security issues in code
- Support for multiple file formats and programming languages

### Digital Forensics

- Analyze files for forensic evidence
- Perform memory dump analysis to identify malicious activity
- Analyze disk images to recover deleted files and identify suspicious activity
- Generate detailed forensic reports

### Advanced Cryptography

- Advanced encryption and decryption with multiple algorithms
- Hash calculation with various algorithms
- Cryptanalysis simulation for educational purposes
- Detailed information about encryption algorithms and attack methods

### Privilege Escalation

- Scan systems for privilege escalation vulnerabilities
- Identify misconfigured permissions and vulnerable services
- Detailed information about privilege escalation techniques
- Mitigation strategies for each vulnerability

## Converting the Rest of the Project

To convert the entire project to TypeScript:

1. Rename all `.js` files to `.ts`
2. Add type annotations to functions and variables
3. Create interfaces for data structures
4. Update imports/exports to use ES modules syntax
5. Compile with TypeScript compiler

## Educational Purpose

All tools in this toolkit are for educational purposes only. They are designed to demonstrate cybersecurity concepts and techniques, not to perform actual attacks or security breaches. Always use these tools responsibly and only on systems you own or have explicit permission to test.

## Legal Disclaimer

This toolkit is for EDUCATIONAL PURPOSES ONLY. Only use these tools on systems you own or have explicit permission to test. Unauthorized use may be illegal and result in criminal charges.
