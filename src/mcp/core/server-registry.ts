/**
 * MCP Server Registry
 * 
 * This module manages the registration and lifecycle of MCP servers
 */

import { EventEmitter } from 'events';
import {
  ServerRegistry,
  MCPServerInstance,
  ServerStatus,
  LogLevel,
  MCPError
} from '../types/index.js';

export class MCPServerRegistry extends EventEmitter implements ServerRegistry {
  public servers: Map<string, MCPServerInstance> = new Map();
  private startupOrder: string[] = [];
  private shutdownOrder: string[] = [];

  constructor() {
    super();
    this.setupGracefulShutdown();
  }

  /**
   * Register a new MCP server
   */
  register(server: MCPServerInstance): void {
    try {
      if (this.servers.has(server.config.name)) {
        throw new MCPError(
          `Server with name '${server.config.name}' is already registered`,
          'DUPLICATE_SERVER'
        );
      }

      this.servers.set(server.config.name, server);
      this.startupOrder.push(server.config.name);
      this.shutdownOrder.unshift(server.config.name); // Reverse order for shutdown

      // Setup server event forwarding
      this.setupServerEventForwarding(server);

      this.emit('server:registered', {
        serverName: server.config.name,
        config: server.config
      });

      console.log(`✅ Registered MCP server: ${server.config.name}`);
    } catch (error) {
      console.error(`❌ Failed to register server ${server.config.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Unregister an MCP server
   */
  unregister(name: string): void {
    try {
      const server = this.servers.get(name);
      if (!server) {
        throw new MCPError(`Server '${name}' not found`, 'SERVER_NOT_FOUND');
      }

      // Stop the server if it's running
      if (server.status === ServerStatus.RUNNING) {
        server.stop().catch(error => {
          console.error(`Error stopping server ${name} during unregistration:`, error);
        });
      }

      this.servers.delete(name);
      
      // Remove from startup/shutdown orders
      const startupIndex = this.startupOrder.indexOf(name);
      if (startupIndex > -1) {
        this.startupOrder.splice(startupIndex, 1);
      }
      
      const shutdownIndex = this.shutdownOrder.indexOf(name);
      if (shutdownIndex > -1) {
        this.shutdownOrder.splice(shutdownIndex, 1);
      }

      this.emit('server:unregistered', { serverName: name });
      console.log(`✅ Unregistered MCP server: ${name}`);
    } catch (error) {
      console.error(`❌ Failed to unregister server ${name}:`, error.message);
      throw error;
    }
  }

  /**
   * Get a specific server by name
   */
  get(name: string): MCPServerInstance | undefined {
    return this.servers.get(name);
  }

  /**
   * List all registered servers
   */
  list(): MCPServerInstance[] {
    return Array.from(this.servers.values());
  }

  /**
   * Start all registered servers
   */
  async startAll(): Promise<void> {
    console.log(`🚀 Starting ${this.servers.size} MCP servers...`);
    
    const results = await Promise.allSettled(
      this.startupOrder.map(async (name) => {
        const server = this.servers.get(name);
        if (server && server.status === ServerStatus.STOPPED) {
          try {
            await server.start();
            return { name, success: true };
          } catch (error) {
            console.error(`❌ Failed to start server ${name}:`, error.message);
            return { name, success: false, error };
          }
        }
        return { name, success: true, skipped: true };
      })
    );

    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.filter(r => r.status === 'rejected' || !r.value.success).length;

    console.log(`✅ Started ${successful} servers successfully`);
    if (failed > 0) {
      console.log(`❌ Failed to start ${failed} servers`);
    }

    this.emit('registry:startup-complete', {
      total: this.servers.size,
      successful,
      failed
    });
  }

  /**
   * Stop all registered servers
   */
  async stopAll(): Promise<void> {
    console.log(`🛑 Stopping ${this.servers.size} MCP servers...`);
    
    const results = await Promise.allSettled(
      this.shutdownOrder.map(async (name) => {
        const server = this.servers.get(name);
        if (server && server.status === ServerStatus.RUNNING) {
          try {
            await server.stop();
            return { name, success: true };
          } catch (error) {
            console.error(`❌ Failed to stop server ${name}:`, error.message);
            return { name, success: false, error };
          }
        }
        return { name, success: true, skipped: true };
      })
    );

    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.filter(r => r.status === 'rejected' || !r.value.success).length;

    console.log(`✅ Stopped ${successful} servers successfully`);
    if (failed > 0) {
      console.log(`❌ Failed to stop ${failed} servers`);
    }

    this.emit('registry:shutdown-complete', {
      total: this.servers.size,
      successful,
      failed
    });
  }

  /**
   * Restart all registered servers
   */
  async restartAll(): Promise<void> {
    await this.stopAll();
    await this.startAll();
  }

  /**
   * Start a specific server
   */
  async startServer(name: string): Promise<void> {
    const server = this.get(name);
    if (!server) {
      throw new MCPError(`Server '${name}' not found`, 'SERVER_NOT_FOUND');
    }

    if (server.status === ServerStatus.RUNNING) {
      console.log(`⚠️  Server ${name} is already running`);
      return;
    }

    try {
      await server.start();
      console.log(`✅ Started server: ${name}`);
    } catch (error) {
      console.error(`❌ Failed to start server ${name}:`, error.message);
      throw error;
    }
  }

  /**
   * Stop a specific server
   */
  async stopServer(name: string): Promise<void> {
    const server = this.get(name);
    if (!server) {
      throw new MCPError(`Server '${name}' not found`, 'SERVER_NOT_FOUND');
    }

    if (server.status === ServerStatus.STOPPED) {
      console.log(`⚠️  Server ${name} is already stopped`);
      return;
    }

    try {
      await server.stop();
      console.log(`✅ Stopped server: ${name}`);
    } catch (error) {
      console.error(`❌ Failed to stop server ${name}:`, error.message);
      throw error;
    }
  }

  /**
   * Restart a specific server
   */
  async restartServer(name: string): Promise<void> {
    await this.stopServer(name);
    await this.startServer(name);
  }

  /**
   * Get registry status
   */
  getStatus(): {
    total: number;
    running: number;
    stopped: number;
    error: number;
    servers: Array<{ name: string; status: ServerStatus; description: string }>;
  } {
    const servers = this.list();
    const status = {
      total: servers.length,
      running: 0,
      stopped: 0,
      error: 0,
      servers: [] as Array<{ name: string; status: ServerStatus; description: string }>
    };

    servers.forEach(server => {
      status.servers.push({
        name: server.config.name,
        status: server.status,
        description: server.config.description
      });

      switch (server.status) {
        case ServerStatus.RUNNING:
          status.running++;
          break;
        case ServerStatus.STOPPED:
          status.stopped++;
          break;
        case ServerStatus.ERROR:
          status.error++;
          break;
      }
    });

    return status;
  }

  /**
   * Setup event forwarding from individual servers
   */
  private setupServerEventForwarding(server: MCPServerInstance): void {
    // Forward all server events with server name prefix
    server.on('*', (event) => {
      this.emit(`server:${server.config.name}:${event.type}`, {
        serverName: server.config.name,
        ...event
      });
    });

    // Forward specific events
    const eventsToForward = [
      'server:started',
      'server:stopped',
      'status:changed',
      'tool:registered',
      'resource:registered',
      'prompt:registered',
      'log:entry'
    ];

    eventsToForward.forEach(eventType => {
      server.on(eventType, (event) => {
        this.emit(`registry:${eventType}`, {
          serverName: server.config.name,
          ...event
        });
      });
    });
  }

  /**
   * Setup graceful shutdown handling
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`\n🔄 Received ${signal}, shutting down gracefully...`);
      
      try {
        await this.stopAll();
        console.log('✅ All servers stopped successfully');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error);
      shutdown('uncaughtException').catch(() => process.exit(1));
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection').catch(() => process.exit(1));
    });
  }

  /**
   * Display registry status in a formatted way
   */
  displayStatus(): void {
    const status = this.getStatus();
    
    console.log('\n📊 MCP Server Registry Status');
    console.log('═'.repeat(50));
    console.log(`Total Servers: ${status.total}`);
    console.log(`Running: ${status.running} | Stopped: ${status.stopped} | Error: ${status.error}`);
    console.log('═'.repeat(50));
    
    if (status.servers.length > 0) {
      status.servers.forEach(server => {
        const statusIcon = this.getStatusIcon(server.status);
        console.log(`${statusIcon} ${server.name.padEnd(20)} | ${server.status.padEnd(10)} | ${server.description}`);
      });
    } else {
      console.log('No servers registered');
    }
    
    console.log('═'.repeat(50));
  }

  /**
   * Get status icon for display
   */
  private getStatusIcon(status: ServerStatus): string {
    switch (status) {
      case ServerStatus.RUNNING:
        return '🟢';
      case ServerStatus.STOPPED:
        return '🔴';
      case ServerStatus.STARTING:
        return '🟡';
      case ServerStatus.STOPPING:
        return '🟠';
      case ServerStatus.ERROR:
        return '❌';
      default:
        return '⚪';
    }
  }
}
