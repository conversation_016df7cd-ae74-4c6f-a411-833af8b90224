/**
 * Base MCP Server Implementation
 * 
 * This module provides the base implementation for all MCP servers in the ecosystem
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { EventEmitter } from 'events';
import {
  MCPServerConfig,
  MCPTool,
  MCPResource,
  MCPPrompt,
  MCPServerInstance,
  ServerStatus,
  LogEntry,
  LogLevel,
  ServerEvent,
  EventHandler,
  ExecutionContext,
  MCPError,
  ValidationError
} from '../types/index.js';

export abstract class BaseMCPServer extends EventEmitter implements MCPServerInstance {
  public config: MCPServerConfig;
  public tools: Map<string, MCPTool> = new Map();
  public resources: Map<string, MCPResource> = new Map();
  public prompts: Map<string, MCPPrompt> = new Map();
  public status: ServerStatus = ServerStatus.STOPPED;

  protected server: McpServer;
  protected transport: StdioServerTransport | null = null;
  protected logs: LogEntry[] = [];
  protected eventHandlers: Map<string, EventHandler[]> = new Map();

  constructor(config: MCPServerConfig) {
    super();
    this.config = config;
    this.server = new McpServer({
      name: config.name,
      version: config.version,
      capabilities: {
        tools: config.capabilities.tools ? {} : undefined,
        resources: config.capabilities.resources ? {} : undefined,
        prompts: config.capabilities.prompts ? {} : undefined,
        logging: config.capabilities.logging ? {} : undefined
      }
    });

    this.setupEventHandlers();
  }

  /**
   * Abstract method to be implemented by concrete servers
   */
  protected abstract initializeServer(): Promise<void>;

  /**
   * Start the MCP server
   */
  async start(): Promise<void> {
    try {
      this.setStatus(ServerStatus.STARTING);
      this.log(LogLevel.INFO, `Starting MCP server: ${this.config.name}`);

      // Initialize the concrete server implementation
      await this.initializeServer();

      // Setup transport
      this.transport = new StdioServerTransport(
        process.stdin,
        process.stdout
      );

      // Connect server to transport
      await this.server.connect(this.transport);

      this.setStatus(ServerStatus.RUNNING);
      this.log(LogLevel.INFO, `MCP server ${this.config.name} started successfully`);
      
      this.emitEvent('server:started', { config: this.config });
    } catch (error) {
      this.setStatus(ServerStatus.ERROR);
      this.log(LogLevel.ERROR, `Failed to start MCP server: ${error.message}`, { error });
      throw new MCPError(`Failed to start server: ${error.message}`, 'START_ERROR');
    }
  }

  /**
   * Stop the MCP server
   */
  async stop(): Promise<void> {
    try {
      this.setStatus(ServerStatus.STOPPING);
      this.log(LogLevel.INFO, `Stopping MCP server: ${this.config.name}`);

      if (this.transport) {
        await this.transport.close();
        this.transport = null;
      }

      this.setStatus(ServerStatus.STOPPED);
      this.log(LogLevel.INFO, `MCP server ${this.config.name} stopped successfully`);
      
      this.emitEvent('server:stopped', { config: this.config });
    } catch (error) {
      this.setStatus(ServerStatus.ERROR);
      this.log(LogLevel.ERROR, `Failed to stop MCP server: ${error.message}`, { error });
      throw new MCPError(`Failed to stop server: ${error.message}`, 'STOP_ERROR');
    }
  }

  /**
   * Restart the MCP server
   */
  async restart(): Promise<void> {
    await this.stop();
    await this.start();
  }

  /**
   * Register a tool with the server
   */
  protected registerTool(tool: MCPTool): void {
    try {
      this.validateTool(tool);
      
      this.server.tool(
        tool.name,
        tool.description,
        tool.inputSchema,
        async (params) => {
          const context = this.createExecutionContext();
          try {
            this.log(LogLevel.DEBUG, `Executing tool: ${tool.name}`, { params, context });
            const result = await tool.handler(params, context);
            this.log(LogLevel.DEBUG, `Tool execution completed: ${tool.name}`, { result });
            return result;
          } catch (error) {
            this.log(LogLevel.ERROR, `Tool execution failed: ${tool.name}`, { error, params });
            throw error;
          }
        }
      );

      this.tools.set(tool.name, tool);
      this.log(LogLevel.INFO, `Tool registered: ${tool.name}`);
      this.emitEvent('tool:registered', { tool });
    } catch (error) {
      this.log(LogLevel.ERROR, `Failed to register tool: ${tool.name}`, { error });
      throw new ValidationError(`Failed to register tool: ${error.message}`);
    }
  }

  /**
   * Register a resource with the server
   */
  protected registerResource(resource: MCPResource): void {
    try {
      this.validateResource(resource);
      
      this.server.resource(
        resource.uri,
        resource.name,
        resource.description,
        resource.mimeType,
        async (uri) => {
          const context = this.createExecutionContext();
          try {
            this.log(LogLevel.DEBUG, `Accessing resource: ${resource.name}`, { uri, context });
            const result = await resource.handler(uri, context);
            this.log(LogLevel.DEBUG, `Resource access completed: ${resource.name}`, { result });
            return result;
          } catch (error) {
            this.log(LogLevel.ERROR, `Resource access failed: ${resource.name}`, { error, uri });
            throw error;
          }
        }
      );

      this.resources.set(resource.uri, resource);
      this.log(LogLevel.INFO, `Resource registered: ${resource.name}`);
      this.emitEvent('resource:registered', { resource });
    } catch (error) {
      this.log(LogLevel.ERROR, `Failed to register resource: ${resource.name}`, { error });
      throw new ValidationError(`Failed to register resource: ${error.message}`);
    }
  }

  /**
   * Register a prompt with the server
   */
  protected registerPrompt(prompt: MCPPrompt): void {
    try {
      this.validatePrompt(prompt);
      
      this.server.prompt(
        prompt.name,
        prompt.description,
        prompt.arguments,
        async (args) => {
          const context = this.createExecutionContext();
          try {
            this.log(LogLevel.DEBUG, `Executing prompt: ${prompt.name}`, { args, context });
            const result = await prompt.handler(args, context);
            this.log(LogLevel.DEBUG, `Prompt execution completed: ${prompt.name}`, { result });
            return result;
          } catch (error) {
            this.log(LogLevel.ERROR, `Prompt execution failed: ${prompt.name}`, { error, args });
            throw error;
          }
        }
      );

      this.prompts.set(prompt.name, prompt);
      this.log(LogLevel.INFO, `Prompt registered: ${prompt.name}`);
      this.emitEvent('prompt:registered', { prompt });
    } catch (error) {
      this.log(LogLevel.ERROR, `Failed to register prompt: ${prompt.name}`, { error });
      throw new ValidationError(`Failed to register prompt: ${error.message}`);
    }
  }

  /**
   * Set server status and emit event
   */
  protected setStatus(status: ServerStatus): void {
    const previousStatus = this.status;
    this.status = status;
    this.emitEvent('status:changed', { previousStatus, currentStatus: status });
  }

  /**
   * Log a message
   */
  protected log(level: LogLevel, message: string, context?: Record<string, any>): void {
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      context
    };

    this.logs.push(entry);
    
    // Keep only last 1000 log entries
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-1000);
    }

    this.emitEvent('log:entry', entry);

    // Also emit to console for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${entry.timestamp.toISOString()}] ${level.toUpperCase()}: ${message}`, context || '');
    }
  }

  /**
   * Emit a server event
   */
  protected emitEvent(type: string, data?: any): void {
    const event: ServerEvent = {
      type,
      timestamp: new Date(),
      data
    };

    this.emit(type, event);
    
    const handlers = this.eventHandlers.get(type) || [];
    handlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        this.log(LogLevel.ERROR, `Event handler error for ${type}`, { error });
      }
    });
  }

  /**
   * Create execution context for tool/resource/prompt execution
   */
  protected createExecutionContext(): ExecutionContext {
    return {
      sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      metadata: {
        serverName: this.config.name,
        serverVersion: this.config.version
      }
    };
  }

  /**
   * Setup base event handlers
   */
  private setupEventHandlers(): void {
    this.on('error', (error) => {
      this.log(LogLevel.ERROR, `Server error: ${error.message}`, { error });
      this.setStatus(ServerStatus.ERROR);
    });
  }

  /**
   * Validate tool configuration
   */
  private validateTool(tool: MCPTool): void {
    if (!tool.name || typeof tool.name !== 'string') {
      throw new ValidationError('Tool name is required and must be a string');
    }
    if (!tool.description || typeof tool.description !== 'string') {
      throw new ValidationError('Tool description is required and must be a string');
    }
    if (!tool.handler || typeof tool.handler !== 'function') {
      throw new ValidationError('Tool handler is required and must be a function');
    }
    if (!tool.inputSchema) {
      throw new ValidationError('Tool input schema is required');
    }
  }

  /**
   * Validate resource configuration
   */
  private validateResource(resource: MCPResource): void {
    if (!resource.uri || typeof resource.uri !== 'string') {
      throw new ValidationError('Resource URI is required and must be a string');
    }
    if (!resource.name || typeof resource.name !== 'string') {
      throw new ValidationError('Resource name is required and must be a string');
    }
    if (!resource.handler || typeof resource.handler !== 'function') {
      throw new ValidationError('Resource handler is required and must be a function');
    }
  }

  /**
   * Validate prompt configuration
   */
  private validatePrompt(prompt: MCPPrompt): void {
    if (!prompt.name || typeof prompt.name !== 'string') {
      throw new ValidationError('Prompt name is required and must be a string');
    }
    if (!prompt.handler || typeof prompt.handler !== 'function') {
      throw new ValidationError('Prompt handler is required and must be a function');
    }
  }

  /**
   * Get server logs
   */
  public getLogs(level?: LogLevel, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;
    
    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }
    
    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }
    
    return filteredLogs;
  }

  /**
   * Add event handler
   */
  public addEventHandler(type: string, handler: EventHandler): void {
    if (!this.eventHandlers.has(type)) {
      this.eventHandlers.set(type, []);
    }
    this.eventHandlers.get(type)!.push(handler);
  }

  /**
   * Remove event handler
   */
  public removeEventHandler(type: string, handler: EventHandler): void {
    const handlers = this.eventHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }
}
