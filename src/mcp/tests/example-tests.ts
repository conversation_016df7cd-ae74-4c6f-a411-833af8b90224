#!/usr/bin/env node

/**
 * Example MCP Server Tests
 * 
 * This file demonstrates how to use the MCP testing framework
 * to test all servers in the ecosystem.
 */

import { MCPTestFramework } from './mcp-test-framework.js';
import { CodeAnalysisServer } from '../servers/code-analysis-server.js';
import { GitOperationsServer } from '../servers/git-operations-server.js';
import { SystemMonitoringServer } from '../servers/system-monitoring-server.js';
import { FileManipulationServer } from '../servers/file-manipulation-server.js';
import { VulnerabilityScanningServer } from '../servers/vulnerability-scanning-server.js';
import { DataValidationServer } from '../servers/data-validation-server.js';
import { LogAnalysisServer } from '../servers/log-analysis-server.js';
import { ProjectManagementServer } from '../servers/project-management-server.js';

async function runAllTests(): Promise<void> {
  console.log('🧪 Starting MCP Server Ecosystem Tests');
  console.log('=' .repeat(50));

  const testFramework = new MCPTestFramework();
  
  // Setup event listeners for real-time feedback
  testFramework.on('suite:started', (event) => {
    console.log(`\n🚀 Starting: ${event.suiteName}`);
  });
  
  testFramework.on('test:passed', (event) => {
    console.log(`   ✅ ${event.testName} (${event.duration}ms)`);
  });
  
  testFramework.on('test:failed', (event) => {
    console.log(`   ❌ ${event.testName} (${event.duration}ms)`);
    console.log(`      Error: ${event.error.message}`);
  });
  
  testFramework.on('suite:completed', (suite) => {
    console.log(`\n📊 ${suite.suiteName} Complete:`);
    console.log(`   Passed: ${suite.passed}, Failed: ${suite.failed}, Duration: ${suite.totalDuration}ms`);
  });

  // Test configuration
  const testConfig = {
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  };

  try {
    // Test Code Analysis Server
    console.log('\n🔍 Testing Code Analysis Server...');
    const codeAnalysisServer = new CodeAnalysisServer();
    await testFramework.testServer({
      ...testConfig,
      server: codeAnalysisServer
    });

    // Test Git Operations Server
    console.log('\n📚 Testing Git Operations Server...');
    const gitOpsServer = new GitOperationsServer();
    await testFramework.testServer({
      ...testConfig,
      server: gitOpsServer
    });

    // Test System Monitoring Server
    console.log('\n📊 Testing System Monitoring Server...');
    const systemMonitorServer = new SystemMonitoringServer();
    await testFramework.testServer({
      ...testConfig,
      server: systemMonitorServer
    });

    // Test File Manipulation Server
    console.log('\n📁 Testing File Manipulation Server...');
    const fileManipServer = new FileManipulationServer(['/tmp', '/var/log']);
    await testFramework.testServer({
      ...testConfig,
      server: fileManipServer
    });

    // Test Vulnerability Scanning Server
    console.log('\n🔒 Testing Vulnerability Scanning Server...');
    const vulnScanServer = new VulnerabilityScanningServer();
    await testFramework.testServer({
      ...testConfig,
      server: vulnScanServer
    });

    // Test Data Validation Server
    console.log('\n✅ Testing Data Validation Server...');
    const dataValidationServer = new DataValidationServer();
    await testFramework.testServer({
      ...testConfig,
      server: dataValidationServer
    });

    // Test Log Analysis Server
    console.log('\n📋 Testing Log Analysis Server...');
    const logAnalysisServer = new LogAnalysisServer();
    await testFramework.testServer({
      ...testConfig,
      server: logAnalysisServer
    });

    // Test Project Management Server
    console.log('\n📈 Testing Project Management Server...');
    const projectMgmtServer = new ProjectManagementServer();
    await testFramework.testServer({
      ...testConfig,
      server: projectMgmtServer
    });

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
    process.exit(1);
  }

  // Generate and display final report
  console.log('\n' + testFramework.generateReport());
  
  // Determine exit code based on results
  const results = Array.from(testFramework.getResults().values());
  const totalFailed = results.reduce((sum, suite) => sum + suite.failed, 0);
  
  if (totalFailed > 0) {
    console.log('❌ Some tests failed. Please review the results above.');
    process.exit(1);
  } else {
    console.log('🎉 All tests passed successfully!');
    process.exit(0);
  }
}

// Individual server test functions for more granular testing

export async function testCodeAnalysisServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new CodeAnalysisServer();
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

export async function testGitOperationsServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new GitOperationsServer();
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

export async function testSystemMonitoringServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new SystemMonitoringServer();
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

export async function testFileManipulationServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new FileManipulationServer(['/tmp']);
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

export async function testVulnerabilityScanningServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new VulnerabilityScanningServer();
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

export async function testDataValidationServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new DataValidationServer();
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

export async function testLogAnalysisServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new LogAnalysisServer();
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

export async function testProjectManagementServer(): Promise<void> {
  const testFramework = new MCPTestFramework();
  const server = new ProjectManagementServer();
  
  const result = await testFramework.testServer({
    server,
    testTimeout: 30000,
    skipSlowTests: false,
    enablePerformanceTests: true,
    enableSecurityTests: true
  });
  
  console.log(testFramework.generateReport());
  return;
}

// CLI interface for running tests
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];
  
  switch (command) {
    case 'all':
      runAllTests();
      break;
    case 'code-analysis':
      testCodeAnalysisServer();
      break;
    case 'git-operations':
      testGitOperationsServer();
      break;
    case 'system-monitoring':
      testSystemMonitoringServer();
      break;
    case 'file-manipulation':
      testFileManipulationServer();
      break;
    case 'vulnerability-scanning':
      testVulnerabilityScanningServer();
      break;
    case 'data-validation':
      testDataValidationServer();
      break;
    case 'log-analysis':
      testLogAnalysisServer();
      break;
    case 'project-management':
      testProjectManagementServer();
      break;
    default:
      console.log('Usage: node example-tests.js <command>');
      console.log('Commands:');
      console.log('  all                    - Run all server tests');
      console.log('  code-analysis          - Test Code Analysis Server');
      console.log('  git-operations         - Test Git Operations Server');
      console.log('  system-monitoring      - Test System Monitoring Server');
      console.log('  file-manipulation      - Test File Manipulation Server');
      console.log('  vulnerability-scanning - Test Vulnerability Scanning Server');
      console.log('  data-validation        - Test Data Validation Server');
      console.log('  log-analysis           - Test Log Analysis Server');
      console.log('  project-management     - Test Project Management Server');
      process.exit(1);
  }
}
