/**
 * MCP Server Testing Framework
 * 
 * Comprehensive testing utilities for MCP servers including:
 * - Server lifecycle testing
 * - Tool execution testing
 * - Resource access testing
 * - Prompt generation testing
 * - Performance testing
 * - Security testing
 */

import { EventEmitter } from 'events';
import {
  MCPServerInstance,
  MCPTool,
  MCPResource,
  MCPPrompt,
  ExecutionContext,
  ServerStatus,
  LogLevel
} from '../types/index.js';

interface TestResult {
  testName: string;
  passed: boolean;
  duration: number;
  error?: Error;
  details?: any;
}

interface TestSuite {
  suiteName: string;
  tests: TestResult[];
  passed: number;
  failed: number;
  totalDuration: number;
}

interface ServerTestConfig {
  server: MCPServerInstance;
  testTimeout: number;
  skipSlowTests: boolean;
  enablePerformanceTests: boolean;
  enableSecurityTests: boolean;
}

export class MCPTestFramework extends EventEmitter {
  private testResults: Map<string, TestSuite> = new Map();
  private currentSuite: string | null = null;

  constructor() {
    super();
  }

  /**
   * Run comprehensive tests for an MCP server
   */
  async testServer(config: ServerTestConfig): Promise<TestSuite> {
    const { server, testTimeout = 30000 } = config;
    const suiteName = `${server.config.name} Server Tests`;
    
    this.startTestSuite(suiteName);
    
    try {
      // Lifecycle tests
      await this.testServerLifecycle(server, testTimeout);
      
      // Configuration tests
      await this.testServerConfiguration(server);
      
      // Tool tests
      await this.testServerTools(server, testTimeout);
      
      // Resource tests
      await this.testServerResources(server, testTimeout);
      
      // Prompt tests
      await this.testServerPrompts(server, testTimeout);
      
      // Performance tests (if enabled)
      if (config.enablePerformanceTests) {
        await this.testServerPerformance(server, testTimeout);
      }
      
      // Security tests (if enabled)
      if (config.enableSecurityTests) {
        await this.testServerSecurity(server, testTimeout);
      }
      
    } catch (error) {
      this.addTestResult('Server Test Suite', false, 0, error);
    }
    
    return this.endTestSuite();
  }

  /**
   * Test server lifecycle (start, stop, restart)
   */
  private async testServerLifecycle(server: MCPServerInstance, timeout: number): Promise<void> {
    // Test initial state
    await this.runTest('Initial State Check', async () => {
      if (server.status !== ServerStatus.STOPPED) {
        throw new Error(`Expected server to be stopped, but status is ${server.status}`);
      }
    });

    // Test server start
    await this.runTest('Server Start', async () => {
      await Promise.race([
        server.start(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Server start timeout')), timeout)
        )
      ]);
      
      if (server.status !== ServerStatus.RUNNING) {
        throw new Error(`Expected server to be running, but status is ${server.status}`);
      }
    });

    // Test server restart
    await this.runTest('Server Restart', async () => {
      await Promise.race([
        server.restart(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Server restart timeout')), timeout)
        )
      ]);
      
      if (server.status !== ServerStatus.RUNNING) {
        throw new Error(`Expected server to be running after restart, but status is ${server.status}`);
      }
    });

    // Test server stop
    await this.runTest('Server Stop', async () => {
      await Promise.race([
        server.stop(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Server stop timeout')), timeout)
        )
      ]);
      
      if (server.status !== ServerStatus.STOPPED) {
        throw new Error(`Expected server to be stopped, but status is ${server.status}`);
      }
    });
  }

  /**
   * Test server configuration
   */
  private async testServerConfiguration(server: MCPServerInstance): Promise<void> {
    await this.runTest('Configuration Validation', async () => {
      const config = server.config;
      
      if (!config.name || typeof config.name !== 'string') {
        throw new Error('Server name is required and must be a string');
      }
      
      if (!config.version || typeof config.version !== 'string') {
        throw new Error('Server version is required and must be a string');
      }
      
      if (!config.description || typeof config.description !== 'string') {
        throw new Error('Server description is required and must be a string');
      }
      
      if (!config.capabilities || typeof config.capabilities !== 'object') {
        throw new Error('Server capabilities are required and must be an object');
      }
    });

    await this.runTest('Capabilities Check', async () => {
      const capabilities = server.config.capabilities;
      const hasTools = server.tools.size > 0;
      const hasResources = server.resources.size > 0;
      const hasPrompts = server.prompts.size > 0;
      
      if (capabilities.tools && !hasTools) {
        throw new Error('Server claims to have tools capability but no tools are registered');
      }
      
      if (capabilities.resources && !hasResources) {
        throw new Error('Server claims to have resources capability but no resources are registered');
      }
      
      if (capabilities.prompts && !hasPrompts) {
        throw new Error('Server claims to have prompts capability but no prompts are registered');
      }
    });
  }

  /**
   * Test server tools
   */
  private async testServerTools(server: MCPServerInstance, timeout: number): Promise<void> {
    if (server.tools.size === 0) {
      await this.runTest('No Tools Check', async () => {
        // This is fine if the server doesn't claim to have tools
        if (server.config.capabilities.tools) {
          throw new Error('Server claims to have tools but none are registered');
        }
      });
      return;
    }

    for (const [toolName, tool] of server.tools) {
      await this.testTool(toolName, tool, timeout);
    }
  }

  /**
   * Test individual tool
   */
  private async testTool(toolName: string, tool: MCPTool, timeout: number): Promise<void> {
    await this.runTest(`Tool: ${toolName} - Structure`, async () => {
      if (!tool.name || typeof tool.name !== 'string') {
        throw new Error('Tool name is required and must be a string');
      }
      
      if (!tool.description || typeof tool.description !== 'string') {
        throw new Error('Tool description is required and must be a string');
      }
      
      if (!tool.handler || typeof tool.handler !== 'function') {
        throw new Error('Tool handler is required and must be a function');
      }
      
      if (!tool.inputSchema) {
        throw new Error('Tool input schema is required');
      }
    });

    // Test tool execution with minimal valid input
    await this.runTest(`Tool: ${toolName} - Execution`, async () => {
      const context: ExecutionContext = {
        sessionId: `test_${Date.now()}`,
        timestamp: new Date(),
        metadata: { test: true }
      };

      try {
        // Try to generate minimal valid input for the tool
        const testInput = this.generateTestInput(tool);
        
        const result = await Promise.race([
          tool.handler(testInput, context),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Tool execution timeout')), timeout)
          )
        ]);

        if (!result || !result.content || !Array.isArray(result.content)) {
          throw new Error('Tool must return a result with content array');
        }
      } catch (error) {
        // Some tools might fail with minimal input, which is acceptable
        // as long as they fail gracefully
        if (error.message.includes('timeout')) {
          throw error;
        }
        // Other errors are acceptable for this basic test
      }
    });
  }

  /**
   * Test server resources
   */
  private async testServerResources(server: MCPServerInstance, timeout: number): Promise<void> {
    if (server.resources.size === 0) {
      await this.runTest('No Resources Check', async () => {
        if (server.config.capabilities.resources) {
          throw new Error('Server claims to have resources but none are registered');
        }
      });
      return;
    }

    for (const [resourceUri, resource] of server.resources) {
      await this.testResource(resourceUri, resource, timeout);
    }
  }

  /**
   * Test individual resource
   */
  private async testResource(resourceUri: string, resource: MCPResource, timeout: number): Promise<void> {
    await this.runTest(`Resource: ${resource.name} - Structure`, async () => {
      if (!resource.uri || typeof resource.uri !== 'string') {
        throw new Error('Resource URI is required and must be a string');
      }
      
      if (!resource.name || typeof resource.name !== 'string') {
        throw new Error('Resource name is required and must be a string');
      }
      
      if (!resource.handler || typeof resource.handler !== 'function') {
        throw new Error('Resource handler is required and must be a function');
      }
    });
  }

  /**
   * Test server prompts
   */
  private async testServerPrompts(server: MCPServerInstance, timeout: number): Promise<void> {
    if (server.prompts.size === 0) {
      await this.runTest('No Prompts Check', async () => {
        if (server.config.capabilities.prompts) {
          throw new Error('Server claims to have prompts but none are registered');
        }
      });
      return;
    }

    for (const [promptName, prompt] of server.prompts) {
      await this.testPrompt(promptName, prompt, timeout);
    }
  }

  /**
   * Test individual prompt
   */
  private async testPrompt(promptName: string, prompt: MCPPrompt, timeout: number): Promise<void> {
    await this.runTest(`Prompt: ${promptName} - Structure`, async () => {
      if (!prompt.name || typeof prompt.name !== 'string') {
        throw new Error('Prompt name is required and must be a string');
      }
      
      if (!prompt.handler || typeof prompt.handler !== 'function') {
        throw new Error('Prompt handler is required and must be a function');
      }
    });
  }

  /**
   * Test server performance
   */
  private async testServerPerformance(server: MCPServerInstance, timeout: number): Promise<void> {
    await this.runTest('Memory Usage', async () => {
      const initialMemory = process.memoryUsage();
      
      // Perform some operations
      await server.start();
      await server.stop();
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // Check for memory leaks (arbitrary threshold)
      if (memoryIncrease > 50 * 1024 * 1024) { // 50MB
        throw new Error(`Potential memory leak detected: ${memoryIncrease} bytes increase`);
      }
    });
  }

  /**
   * Test server security
   */
  private async testServerSecurity(server: MCPServerInstance, timeout: number): Promise<void> {
    await this.runTest('Security Configuration', async () => {
      const security = server.config.security;
      
      if (security?.authentication?.type === 'none') {
        console.warn('Warning: Server has no authentication configured');
      }
      
      if (!security?.authorization?.enabled) {
        console.warn('Warning: Server has no authorization configured');
      }
    });
  }

  /**
   * Generate test input for a tool
   */
  private generateTestInput(tool: MCPTool): any {
    // This is a simplified test input generator
    // In a real implementation, you'd want more sophisticated input generation
    return {};
  }

  /**
   * Run a single test
   */
  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.addTestResult(testName, true, duration);
      this.emit('test:passed', { testName, duration });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.addTestResult(testName, false, duration, error);
      this.emit('test:failed', { testName, duration, error });
    }
  }

  /**
   * Start a test suite
   */
  private startTestSuite(suiteName: string): void {
    this.currentSuite = suiteName;
    this.testResults.set(suiteName, {
      suiteName,
      tests: [],
      passed: 0,
      failed: 0,
      totalDuration: 0
    });
    this.emit('suite:started', { suiteName });
  }

  /**
   * End the current test suite
   */
  private endTestSuite(): TestSuite {
    if (!this.currentSuite) {
      throw new Error('No active test suite');
    }
    
    const suite = this.testResults.get(this.currentSuite)!;
    this.emit('suite:completed', suite);
    this.currentSuite = null;
    
    return suite;
  }

  /**
   * Add a test result to the current suite
   */
  private addTestResult(testName: string, passed: boolean, duration: number, error?: Error): void {
    if (!this.currentSuite) {
      throw new Error('No active test suite');
    }
    
    const suite = this.testResults.get(this.currentSuite)!;
    const result: TestResult = {
      testName,
      passed,
      duration,
      error,
      details: error ? { message: error.message, stack: error.stack } : undefined
    };
    
    suite.tests.push(result);
    suite.totalDuration += duration;
    
    if (passed) {
      suite.passed++;
    } else {
      suite.failed++;
    }
  }

  /**
   * Get all test results
   */
  getResults(): Map<string, TestSuite> {
    return new Map(this.testResults);
  }

  /**
   * Generate a test report
   */
  generateReport(): string {
    const results = Array.from(this.testResults.values());
    let report = '\n📊 MCP Server Test Report\n';
    report += '='.repeat(50) + '\n\n';
    
    for (const suite of results) {
      report += `📋 ${suite.suiteName}\n`;
      report += `-`.repeat(30) + '\n';
      report += `✅ Passed: ${suite.passed}\n`;
      report += `❌ Failed: ${suite.failed}\n`;
      report += `⏱️  Total Duration: ${suite.totalDuration}ms\n\n`;
      
      // Show failed tests
      const failedTests = suite.tests.filter(t => !t.passed);
      if (failedTests.length > 0) {
        report += '❌ Failed Tests:\n';
        for (const test of failedTests) {
          report += `   • ${test.testName}: ${test.error?.message || 'Unknown error'}\n`;
        }
        report += '\n';
      }
    }
    
    const totalPassed = results.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = results.reduce((sum, suite) => sum + suite.failed, 0);
    const totalDuration = results.reduce((sum, suite) => sum + suite.totalDuration, 0);
    
    report += '📈 Overall Summary\n';
    report += '='.repeat(20) + '\n';
    report += `Total Tests: ${totalPassed + totalFailed}\n`;
    report += `Passed: ${totalPassed}\n`;
    report += `Failed: ${totalFailed}\n`;
    report += `Success Rate: ${totalPassed + totalFailed > 0 ? ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1) : 0}%\n`;
    report += `Total Duration: ${totalDuration}ms\n`;
    
    return report;
  }
}
