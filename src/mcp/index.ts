#!/usr/bin/env node

/**
 * MCP Server Ecosystem Main Entry Point
 * 
 * This is the main orchestrator for the comprehensive MCP server ecosystem.
 * It manages multiple specialized MCP servers and provides a unified interface.
 */

import { program } from 'commander';
import chalk from 'chalk';
import * as dotenv from 'dotenv';
import { MCPServerRegistry } from './core/server-registry.js';
import { CodeAnalysisServer } from './servers/code-analysis-server.js';
import { GitOperationsServer } from './servers/git-operations-server.js';
import { SystemMonitoringServer } from './servers/system-monitoring-server.js';
import { FileManipulationServer } from './servers/file-manipulation-server.js';
import { LogLevel } from './types/index.js';

// Load environment variables
dotenv.config();

// ASCII Art Banner
const banner = `
███╗   ███╗ ██████╗██████╗     ███████╗███████╗██████╗ ██╗   ██╗███████╗██████╗ 
████╗ ████║██╔════╝██╔══██╗    ██╔════╝██╔════╝██╔══██╗██║   ██║██╔════╝██╔══██╗
██╔████╔██║██║     ██████╔╝    ███████╗█████╗  ██████╔╝██║   ██║█████╗  ██████╔╝
██║╚██╔╝██║██║     ██╔═══╝     ╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══╝  ██╔══██╗
██║ ╚═╝ ██║╚██████╗██║         ███████║███████╗██║  ██║ ╚████╔╝ ███████╗██║  ██║
╚═╝     ╚═╝ ╚═════╝╚═╝         ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝

███████╗ ██████╗ ██████╗ ███████╗██╗   ██╗███████╗████████╗███████╗███╗   ███╗
██╔════╝██╔════╝██╔═══██╗██╔════╝╚██╗ ██╔╝██╔════╝╚══██╔══╝██╔════╝████╗ ████║
█████╗  ██║     ██║   ██║███████╗ ╚████╔╝ ███████╗   ██║   █████╗  ██╔████╔██║
██╔══╝  ██║     ██║   ██║╚════██║  ╚██╔╝  ╚════██║   ██║   ██╔══╝  ██║╚██╔╝██║
███████╗╚██████╗╚██████╔╝███████║   ██║   ███████║   ██║   ███████╗██║ ╚═╝ ██║
╚══════╝ ╚═════╝ ╚═════╝ ╚══════╝   ╚═╝   ╚══════╝   ╚═╝   ╚══════╝╚═╝     ╚═╝
`;

class MCPEcosystem {
  private registry: MCPServerRegistry;
  private selectedServer: string | null = null;

  constructor() {
    this.registry = new MCPServerRegistry();
    this.setupEventHandlers();
  }

  /**
   * Initialize and register all MCP servers
   */
  async initialize(): Promise<void> {
    console.log(chalk.cyan(banner));
    console.log(chalk.yellow('🚀 Initializing MCP Server Ecosystem...'));
    console.log(chalk.gray('═'.repeat(80)));

    try {
      // Register all available servers
      await this.registerServers();
      
      console.log(chalk.green('✅ MCP Server Ecosystem initialized successfully!'));
      this.registry.displayStatus();
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize MCP ecosystem:'), error.message);
      throw error;
    }
  }

  /**
   * Register all available MCP servers
   */
  private async registerServers(): Promise<void> {
    const servers = [
      {
        name: 'Code Analysis',
        factory: () => new CodeAnalysisServer(),
        description: 'Static code analysis, security scanning, and quality metrics'
      },
      {
        name: 'Git Operations',
        factory: () => new GitOperationsServer(),
        description: 'Comprehensive Git repository management and analysis'
      },
      {
        name: 'System Monitoring',
        factory: () => new SystemMonitoringServer(),
        description: 'Real-time system performance monitoring and alerts'
      },
      {
        name: 'File Manipulation',
        factory: () => new FileManipulationServer(this.getAllowedPaths()),
        description: 'Secure file and directory operations with access controls'
      }
    ];

    for (const serverConfig of servers) {
      try {
        console.log(chalk.blue(`📦 Registering ${serverConfig.name} Server...`));
        const server = serverConfig.factory();
        this.registry.register(server);
        console.log(chalk.green(`   ✅ ${serverConfig.description}`));
      } catch (error) {
        console.error(chalk.red(`   ❌ Failed to register ${serverConfig.name}:`), error.message);
      }
    }
  }

  /**
   * Start a specific server or all servers
   */
  async startServer(serverName?: string): Promise<void> {
    try {
      if (serverName) {
        await this.registry.startServer(serverName);
        this.selectedServer = serverName;
      } else {
        await this.registry.startAll();
      }
    } catch (error) {
      console.error(chalk.red('Failed to start server(s):'), error.message);
      throw error;
    }
  }

  /**
   * Stop a specific server or all servers
   */
  async stopServer(serverName?: string): Promise<void> {
    try {
      if (serverName) {
        await this.registry.stopServer(serverName);
        if (this.selectedServer === serverName) {
          this.selectedServer = null;
        }
      } else {
        await this.registry.stopAll();
        this.selectedServer = null;
      }
    } catch (error) {
      console.error(chalk.red('Failed to stop server(s):'), error.message);
      throw error;
    }
  }

  /**
   * List all available servers
   */
  listServers(): void {
    this.registry.displayStatus();
  }

  /**
   * Get server information
   */
  getServerInfo(serverName: string): any {
    const server = this.registry.get(serverName);
    if (!server) {
      throw new Error(`Server '${serverName}' not found`);
    }

    return {
      name: server.config.name,
      version: server.config.version,
      description: server.config.description,
      status: server.status,
      capabilities: server.config.capabilities,
      tools: Array.from(server.tools.keys()),
      resources: Array.from(server.resources.keys()),
      prompts: Array.from(server.prompts.keys())
    };
  }

  /**
   * Setup event handlers for the registry
   */
  private setupEventHandlers(): void {
    this.registry.on('server:registered', (event) => {
      console.log(chalk.green(`📝 Server registered: ${event.serverName}`));
    });

    this.registry.on('server:unregistered', (event) => {
      console.log(chalk.yellow(`📝 Server unregistered: ${event.serverName}`));
    });

    this.registry.on('registry:startup-complete', (event) => {
      console.log(chalk.green(`🎉 Startup complete: ${event.successful}/${event.total} servers started`));
    });

    this.registry.on('registry:shutdown-complete', (event) => {
      console.log(chalk.blue(`🛑 Shutdown complete: ${event.successful}/${event.total} servers stopped`));
    });
  }

  /**
   * Get allowed paths for file operations
   */
  private getAllowedPaths(): string[] {
    const allowedPaths = process.env.MCP_ALLOWED_PATHS?.split(',') || [];
    return allowedPaths.length > 0 ? allowedPaths : [process.cwd()];
  }

  /**
   * Run a specific server in standalone mode
   */
  async runStandalone(serverName: string): Promise<void> {
    console.log(chalk.cyan(`🚀 Starting ${serverName} server in standalone mode...`));
    
    const server = this.registry.get(serverName);
    if (!server) {
      throw new Error(`Server '${serverName}' not found`);
    }

    await server.start();
    
    console.log(chalk.green(`✅ ${serverName} server is running`));
    console.log(chalk.gray('Press Ctrl+C to stop the server'));

    // Keep the process alive
    return new Promise((resolve) => {
      process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n🛑 Stopping server...'));
        await server.stop();
        console.log(chalk.green('✅ Server stopped'));
        resolve();
      });
    });
  }
}

// CLI Setup
program
  .name('mcp-ecosystem')
  .description('Comprehensive MCP Server Ecosystem')
  .version('1.0.0');

program
  .command('list')
  .description('List all available MCP servers')
  .action(async () => {
    const ecosystem = new MCPEcosystem();
    await ecosystem.initialize();
    ecosystem.listServers();
  });

program
  .command('start [server]')
  .description('Start a specific server or all servers')
  .action(async (server) => {
    const ecosystem = new MCPEcosystem();
    await ecosystem.initialize();
    await ecosystem.startServer(server);
  });

program
  .command('stop [server]')
  .description('Stop a specific server or all servers')
  .action(async (server) => {
    const ecosystem = new MCPEcosystem();
    await ecosystem.initialize();
    await ecosystem.stopServer(server);
  });

program
  .command('info <server>')
  .description('Get detailed information about a server')
  .action(async (server) => {
    const ecosystem = new MCPEcosystem();
    await ecosystem.initialize();
    
    try {
      const info = ecosystem.getServerInfo(server);
      console.log(chalk.cyan(`\n📊 Server Information: ${server}`));
      console.log(chalk.gray('═'.repeat(50)));
      console.log(JSON.stringify(info, null, 2));
    } catch (error) {
      console.error(chalk.red(error.message));
    }
  });

program
  .command('run <server>')
  .description('Run a specific server in standalone mode')
  .action(async (server) => {
    const ecosystem = new MCPEcosystem();
    await ecosystem.initialize();
    await ecosystem.runStandalone(server);
  });

program
  .command('interactive')
  .description('Start interactive mode for server management')
  .action(async () => {
    console.log(chalk.yellow('🔧 Interactive mode not yet implemented'));
    console.log(chalk.gray('Use individual commands for now: list, start, stop, info, run'));
  });

// Default action - show help
program.action(() => {
  program.help();
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error(chalk.red('💥 Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('💥 Unhandled Rejection at:'), promise, 'reason:', reason);
  process.exit(1);
});

// Parse command line arguments
if (import.meta.url === `file://${process.argv[1]}`) {
  program.parse();
}

export { MCPEcosystem };
