/**
 * MCP Server Types and Interfaces
 * 
 * This module defines the core types and interfaces for the MCP server ecosystem
 */

import { z } from 'zod';

// Base MCP Server Configuration
export interface MCPServerConfig {
  name: string;
  version: string;
  description: string;
  capabilities: ServerCapabilities;
  transport?: TransportConfig;
  security?: SecurityConfig;
}

export interface ServerCapabilities {
  tools?: boolean;
  resources?: boolean;
  prompts?: boolean;
  logging?: boolean;
}

export interface TransportConfig {
  type: 'stdio' | 'sse' | 'websocket';
  options?: Record<string, any>;
}

export interface SecurityConfig {
  authentication?: AuthConfig;
  authorization?: AuthzConfig;
  encryption?: EncryptionConfig;
}

export interface AuthConfig {
  type: 'none' | 'api-key' | 'oauth' | 'jwt';
  options?: Record<string, any>;
}

export interface AuthzConfig {
  enabled: boolean;
  policies?: string[];
}

export interface EncryptionConfig {
  enabled: boolean;
  algorithm?: string;
}

// Tool Definitions
export interface MCPTool {
  name: string;
  description: string;
  inputSchema: z.ZodSchema;
  handler: Tool<PERSON>and<PERSON>;
  category?: string;
  tags?: string[];
  security?: ToolSecurity;
}

export interface ToolSecurity {
  requiresAuth?: boolean;
  permissions?: string[];
  rateLimit?: RateLimit;
}

export interface RateLimit {
  requests: number;
  window: number; // in seconds
}

export type ToolHandler = (params: any, context: ExecutionContext) => Promise<ToolResult>;

export interface ToolResult {
  content: Array<{
    type: 'text' | 'image' | 'resource';
    text?: string;
    data?: string;
    mimeType?: string;
  }>;
  isError?: boolean;
}

export interface ExecutionContext {
  userId?: string;
  sessionId: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Resource Definitions
export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
  handler: ResourceHandler;
}

export type ResourceHandler = (uri: string, context: ExecutionContext) => Promise<ResourceContent>;

export interface ResourceContent {
  contents: Array<{
    uri: string;
    mimeType: string;
    text?: string;
    blob?: Uint8Array;
  }>;
}

// Prompt Definitions
export interface MCPPrompt {
  name: string;
  description?: string;
  arguments?: PromptArgument[];
  handler: PromptHandler;
}

export interface PromptArgument {
  name: string;
  description?: string;
  required?: boolean;
}

export type PromptHandler = (args: Record<string, string>, context: ExecutionContext) => Promise<PromptResult>;

export interface PromptResult {
  description?: string;
  messages: Array<{
    role: 'user' | 'assistant';
    content: {
      type: 'text';
      text: string;
    };
  }>;
}

// Server Registry
export interface ServerRegistry {
  servers: Map<string, MCPServerInstance>;
  register(server: MCPServerInstance): void;
  unregister(name: string): void;
  get(name: string): MCPServerInstance | undefined;
  list(): MCPServerInstance[];
}

export interface MCPServerInstance {
  config: MCPServerConfig;
  tools: Map<string, MCPTool>;
  resources: Map<string, MCPResource>;
  prompts: Map<string, MCPPrompt>;
  status: ServerStatus;
  start(): Promise<void>;
  stop(): Promise<void>;
  restart(): Promise<void>;
}

export enum ServerStatus {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  ERROR = 'error'
}

// Logging
export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// Events
export interface ServerEvent {
  type: string;
  timestamp: Date;
  data?: any;
}

export interface EventHandler {
  (event: ServerEvent): void | Promise<void>;
}

// Validation Schemas
export const MCPServerConfigSchema = z.object({
  name: z.string().min(1),
  version: z.string().min(1),
  description: z.string(),
  capabilities: z.object({
    tools: z.boolean().optional(),
    resources: z.boolean().optional(),
    prompts: z.boolean().optional(),
    logging: z.boolean().optional()
  }),
  transport: z.object({
    type: z.enum(['stdio', 'sse', 'websocket']),
    options: z.record(z.any()).optional()
  }).optional(),
  security: z.object({
    authentication: z.object({
      type: z.enum(['none', 'api-key', 'oauth', 'jwt']),
      options: z.record(z.any()).optional()
    }).optional(),
    authorization: z.object({
      enabled: z.boolean(),
      policies: z.array(z.string()).optional()
    }).optional(),
    encryption: z.object({
      enabled: z.boolean(),
      algorithm: z.string().optional()
    }).optional()
  }).optional()
});

export const ToolInputSchema = z.object({
  name: z.string().min(1),
  description: z.string(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  security: z.object({
    requiresAuth: z.boolean().optional(),
    permissions: z.array(z.string()).optional(),
    rateLimit: z.object({
      requests: z.number().positive(),
      window: z.number().positive()
    }).optional()
  }).optional()
});

// Error Types
export class MCPError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'MCPError';
  }
}

export class ValidationError extends MCPError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends MCPError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'AUTHENTICATION_ERROR', details);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends MCPError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'AUTHORIZATION_ERROR', details);
    this.name = 'AuthorizationError';
  }
}

export class RateLimitError extends MCPError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, 'RATE_LIMIT_ERROR', details);
    this.name = 'RateLimitError';
  }
}
