/**
 * Log Analysis MCP Server
 * 
 * This server provides comprehensive log analysis capabilities including:
 * - Log parsing and pattern recognition
 * - Error detection and classification
 * - Performance metrics extraction
 * - Security event analysis
 * - Anomaly detection
 * - Log aggregation and reporting
 */

import { z } from 'zod';
import * as fs from 'fs/promises';
import { createReadStream } from 'fs';
import { createInterface } from 'readline';
import { BaseMCPServer } from '../core/base-server.js';
import {
  MCPServerConfig,
  MCPTool,
  ToolResult,
  ExecutionContext,
  LogLevel
} from '../types/index.js';

interface LogEntry {
  timestamp: Date;
  level: string;
  message: string;
  source?: string;
  metadata?: Record<string, any>;
  raw: string;
}

interface LogPattern {
  name: string;
  regex: RegExp;
  fields: string[];
  category: 'error' | 'warning' | 'info' | 'security' | 'performance';
}

interface AnalysisResult {
  summary: {
    totalLines: number;
    parsedLines: number;
    errorCount: number;
    warningCount: number;
    timeRange: { start: Date; end: Date };
  };
  patterns: Array<{
    pattern: string;
    count: number;
    category: string;
    examples: string[];
  }>;
  anomalies: Array<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
    occurrences: number;
    examples: string[];
  }>;
  metrics: {
    errorRate: number;
    averageResponseTime?: number;
    throughput?: number;
  };
}

interface SecurityEvent {
  timestamp: Date;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  description: string;
  indicators: string[];
}

export class LogAnalysisServer extends BaseMCPServer {
  private logPatterns: LogPattern[] = [];
  private securityPatterns: Map<string, RegExp[]> = new Map();
  private analysisHistory: Map<string, AnalysisResult> = new Map();

  constructor() {
    const config: MCPServerConfig = {
      name: 'log-analysis',
      version: '1.0.0',
      description: 'Comprehensive log analysis, pattern recognition, and security monitoring server',
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      }
    };

    super(config);
    this.initializePatterns();
  }

  protected async initializeServer(): Promise<void> {
    this.log(LogLevel.INFO, 'Initializing Log Analysis Server');

    // Register tools
    this.registerTool(this.createAnalyzeLogsTool());
    this.registerTool(this.createParseLogsTool());
    this.registerTool(this.createDetectAnomaliesTools());
    this.registerTool(this.createSecurityAnalysisTool());
    this.registerTool(this.createPerformanceAnalysisTool());
    this.registerTool(this.createSearchLogsTool());
    this.registerTool(this.createAggregateLogsTool());
    this.registerTool(this.createGenerateReportTool());
    this.registerTool(this.createRealTimeMonitorTool());
    this.registerTool(this.createCustomPatternTool());

    // Register resources
    this.registerResource({
      uri: 'logs://analysis/{analysisId}',
      name: 'Log Analysis Results',
      description: 'Access to log analysis results and reports',
      mimeType: 'application/json',
      handler: this.handleAnalysisResource.bind(this)
    });

    // Register prompts
    this.registerPrompt({
      name: 'log-investigation',
      description: 'Generate log investigation prompt for specific issues',
      arguments: [
        { name: 'logFile', description: 'Log file to investigate', required: true },
        { name: 'issue', description: 'Specific issue to investigate', required: true },
        { name: 'timeRange', description: 'Time range for investigation', required: false }
      ],
      handler: this.handleLogInvestigationPrompt.bind(this)
    });

    this.log(LogLevel.INFO, 'Log Analysis Server initialized successfully');
  }

  private createAnalyzeLogsTool(): MCPTool {
    return {
      name: 'analyze-logs',
      description: 'Perform comprehensive log analysis',
      category: 'analysis',
      tags: ['logs', 'analysis', 'patterns'],
      inputSchema: z.object({
        logFile: z.string().describe('Path to log file'),
        logFormat: z.enum(['apache', 'nginx', 'syslog', 'json', 'custom']).optional().describe('Log format type'),
        timeRange: z.object({
          start: z.string().optional(),
          end: z.string().optional()
        }).optional().describe('Time range for analysis'),
        includePatterns: z.array(z.string()).optional().describe('Specific patterns to look for'),
        maxLines: z.number().optional().describe('Maximum lines to analyze')
      }),
      handler: this.handleAnalyzeLogs.bind(this)
    };
  }

  private createParseLogsTool(): MCPTool {
    return {
      name: 'parse-logs',
      description: 'Parse log files into structured format',
      category: 'parsing',
      tags: ['logs', 'parsing', 'structure'],
      inputSchema: z.object({
        logFile: z.string().describe('Path to log file'),
        format: z.enum(['apache', 'nginx', 'syslog', 'json', 'custom']).describe('Log format'),
        customPattern: z.string().optional().describe('Custom regex pattern for parsing'),
        outputFormat: z.enum(['json', 'csv']).default('json').describe('Output format'),
        includeRaw: z.boolean().default(false).describe('Include raw log lines')
      }),
      handler: this.handleParseLogs.bind(this)
    };
  }

  private createDetectAnomaliesTools(): MCPTool {
    return {
      name: 'detect-anomalies',
      description: 'Detect anomalies and unusual patterns in logs',
      category: 'analysis',
      tags: ['anomalies', 'detection', 'patterns'],
      inputSchema: z.object({
        logFile: z.string().describe('Path to log file'),
        sensitivity: z.enum(['low', 'medium', 'high']).default('medium').describe('Anomaly detection sensitivity'),
        baselineHours: z.number().default(24).describe('Hours of baseline data to use'),
        anomalyTypes: z.array(z.enum(['volume', 'pattern', 'timing', 'error-rate'])).optional().describe('Types of anomalies to detect')
      }),
      handler: this.handleDetectAnomalies.bind(this)
    };
  }

  private createSecurityAnalysisTool(): MCPTool {
    return {
      name: 'security-analysis',
      description: 'Analyze logs for security events and threats',
      category: 'security',
      tags: ['security', 'threats', 'events'],
      inputSchema: z.object({
        logFile: z.string().describe('Path to log file'),
        threatTypes: z.array(z.enum(['brute-force', 'injection', 'xss', 'ddos', 'malware'])).optional().describe('Specific threat types to look for'),
        severity: z.enum(['low', 'medium', 'high', 'critical']).optional().describe('Minimum severity level'),
        includeIPs: z.boolean().default(true).describe('Include IP address analysis')
      }),
      handler: this.handleSecurityAnalysis.bind(this)
    };
  }

  private createPerformanceAnalysisTool(): MCPTool {
    return {
      name: 'performance-analysis',
      description: 'Extract and analyze performance metrics from logs',
      category: 'performance',
      tags: ['performance', 'metrics', 'response-time'],
      inputSchema: z.object({
        logFile: z.string().describe('Path to log file'),
        metrics: z.array(z.enum(['response-time', 'throughput', 'error-rate', 'cpu-usage', 'memory-usage'])).optional().describe('Metrics to extract'),
        aggregation: z.enum(['minute', 'hour', 'day']).default('hour').describe('Time aggregation level'),
        percentiles: z.array(z.number()).default([50, 90, 95, 99]).describe('Percentiles to calculate')
      }),
      handler: this.handlePerformanceAnalysis.bind(this)
    };
  }

  private createSearchLogsTool(): MCPTool {
    return {
      name: 'search-logs',
      description: 'Search logs with advanced filtering and pattern matching',
      category: 'search',
      tags: ['search', 'filter', 'query'],
      inputSchema: z.object({
        logFile: z.string().describe('Path to log file'),
        query: z.string().describe('Search query or regex pattern'),
        timeRange: z.object({
          start: z.string().optional(),
          end: z.string().optional()
        }).optional().describe('Time range for search'),
        logLevel: z.enum(['debug', 'info', 'warn', 'error', 'fatal']).optional().describe('Filter by log level'),
        context: z.number().default(0).describe('Number of context lines to include'),
        maxResults: z.number().default(100).describe('Maximum results to return')
      }),
      handler: this.handleSearchLogs.bind(this)
    };
  }

  private createAggregateLogsTool(): MCPTool {
    return {
      name: 'aggregate-logs',
      description: 'Aggregate log data for reporting and visualization',
      category: 'aggregation',
      tags: ['aggregation', 'statistics', 'summary'],
      inputSchema: z.object({
        logFiles: z.array(z.string()).describe('Paths to log files'),
        groupBy: z.enum(['hour', 'day', 'week', 'month', 'level', 'source']).describe('Aggregation criteria'),
        metrics: z.array(z.enum(['count', 'error-rate', 'avg-response-time'])).describe('Metrics to calculate'),
        filters: z.object({}).optional().describe('Additional filters to apply')
      }),
      handler: this.handleAggregateLogs.bind(this)
    };
  }

  private createGenerateReportTool(): MCPTool {
    return {
      name: 'generate-report',
      description: 'Generate comprehensive log analysis report',
      category: 'reporting',
      tags: ['report', 'summary', 'analysis'],
      inputSchema: z.object({
        analysisId: z.string().describe('Analysis ID to generate report for'),
        format: z.enum(['json', 'html', 'pdf']).default('json').describe('Report format'),
        sections: z.array(z.enum(['summary', 'patterns', 'anomalies', 'security', 'performance'])).optional().describe('Report sections to include'),
        includeCharts: z.boolean().default(true).describe('Include charts and visualizations')
      }),
      handler: this.handleGenerateReport.bind(this)
    };
  }

  private createRealTimeMonitorTool(): MCPTool {
    return {
      name: 'realtime-monitor',
      description: 'Start real-time log monitoring with alerts',
      category: 'monitoring',
      tags: ['realtime', 'monitoring', 'alerts'],
      inputSchema: z.object({
        logFile: z.string().describe('Path to log file to monitor'),
        alertRules: z.array(z.object({
          name: z.string(),
          pattern: z.string(),
          severity: z.enum(['low', 'medium', 'high', 'critical']),
          threshold: z.number().optional()
        })).describe('Alert rules to apply'),
        interval: z.number().default(5).describe('Monitoring interval in seconds')
      }),
      handler: this.handleRealTimeMonitor.bind(this)
    };
  }

  private createCustomPatternTool(): MCPTool {
    return {
      name: 'custom-pattern',
      description: 'Define and apply custom log patterns',
      category: 'patterns',
      tags: ['custom', 'patterns', 'regex'],
      inputSchema: z.object({
        name: z.string().describe('Pattern name'),
        regex: z.string().describe('Regular expression pattern'),
        fields: z.array(z.string()).describe('Field names for captured groups'),
        category: z.enum(['error', 'warning', 'info', 'security', 'performance']).describe('Pattern category'),
        testLog: z.string().optional().describe('Test log line to validate pattern')
      }),
      handler: this.handleCustomPattern.bind(this)
    };
  }

  private async handleAnalyzeLogs(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { logFile, logFormat, timeRange, includePatterns, maxLines } = params;
      
      this.log(LogLevel.INFO, `Analyzing log file: ${logFile}`, { context });

      const analysisResult = await this.analyzeLogs(logFile, logFormat, timeRange, includePatterns, maxLines);
      
      // Store analysis result
      const analysisId = `analysis_${Date.now()}`;
      this.analysisHistory.set(analysisId, analysisResult);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            analysisId,
            ...analysisResult
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Log analysis failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error analyzing logs: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleParseLogs(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { logFile, format, customPattern, outputFormat = 'json', includeRaw = false } = params;
      
      this.log(LogLevel.INFO, `Parsing log file: ${logFile}`, { context });

      const parsedLogs = await this.parseLogs(logFile, format, customPattern, includeRaw);

      let output: string;
      if (outputFormat === 'csv') {
        output = this.convertToCSV(parsedLogs);
      } else {
        output = JSON.stringify(parsedLogs, null, 2);
      }

      return {
        content: [{
          type: 'text',
          text: output
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Log parsing failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error parsing logs: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleSearchLogs(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { logFile, query, timeRange, logLevel, context: contextLines = 0, maxResults = 100 } = params;
      
      this.log(LogLevel.INFO, `Searching logs: ${query}`, { context });

      const searchResults = await this.searchLogs(logFile, query, timeRange, logLevel, contextLines, maxResults);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(searchResults, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Log search failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error searching logs: ${error.message}`
        }],
        isError: true
      };
    }
  }

  // Helper methods for log analysis
  private async analyzeLogs(
    logFile: string, 
    logFormat?: string, 
    timeRange?: any, 
    includePatterns?: string[], 
    maxLines?: number
  ): Promise<AnalysisResult> {
    const logEntries = await this.readLogFile(logFile, maxLines);
    const parsedEntries = this.parseLogEntries(logEntries, logFormat);
    
    // Filter by time range if specified
    let filteredEntries = parsedEntries;
    if (timeRange) {
      filteredEntries = this.filterByTimeRange(parsedEntries, timeRange);
    }

    // Analyze patterns
    const patterns = this.analyzePatterns(filteredEntries, includePatterns);
    
    // Detect anomalies
    const anomalies = this.detectAnomalies(filteredEntries);
    
    // Calculate metrics
    const metrics = this.calculateMetrics(filteredEntries);
    
    // Generate summary
    const summary = this.generateSummary(filteredEntries);

    return {
      summary,
      patterns,
      anomalies,
      metrics
    };
  }

  private async readLogFile(logFile: string, maxLines?: number): Promise<string[]> {
    const lines: string[] = [];
    const fileStream = createReadStream(logFile);
    const rl = createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    let lineCount = 0;
    for await (const line of rl) {
      if (maxLines && lineCount >= maxLines) {
        break;
      }
      lines.push(line);
      lineCount++;
    }

    return lines;
  }

  private parseLogEntries(lines: string[], format?: string): LogEntry[] {
    const entries: LogEntry[] = [];
    
    for (const line of lines) {
      try {
        const entry = this.parseLogLine(line, format);
        if (entry) {
          entries.push(entry);
        }
      } catch (error) {
        // Skip unparseable lines
        continue;
      }
    }

    return entries;
  }

  private parseLogLine(line: string, format?: string): LogEntry | null {
    // Simplified log parsing - in real implementation, use proper log parsers
    const timestamp = new Date();
    let level = 'info';
    let message = line;

    // Try to extract timestamp and level from common formats
    const timestampMatch = line.match(/(\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2})/);
    if (timestampMatch) {
      timestamp.setTime(Date.parse(timestampMatch[1]));
    }

    const levelMatch = line.match(/\b(DEBUG|INFO|WARN|ERROR|FATAL)\b/i);
    if (levelMatch) {
      level = levelMatch[1].toLowerCase();
    }

    return {
      timestamp,
      level,
      message,
      raw: line
    };
  }

  private filterByTimeRange(entries: LogEntry[], timeRange: any): LogEntry[] {
    const start = timeRange.start ? new Date(timeRange.start) : new Date(0);
    const end = timeRange.end ? new Date(timeRange.end) : new Date();

    return entries.filter(entry => 
      entry.timestamp >= start && entry.timestamp <= end
    );
  }

  private analyzePatterns(entries: LogEntry[], includePatterns?: string[]): any[] {
    const patternCounts = new Map<string, number>();
    const patternExamples = new Map<string, string[]>();

    // Apply built-in patterns
    for (const pattern of this.logPatterns) {
      let count = 0;
      const examples: string[] = [];

      for (const entry of entries) {
        if (pattern.regex.test(entry.message)) {
          count++;
          if (examples.length < 3) {
            examples.push(entry.message);
          }
        }
      }

      if (count > 0) {
        patternCounts.set(pattern.name, count);
        patternExamples.set(pattern.name, examples);
      }
    }

    // Convert to result format
    const patterns = [];
    for (const [patternName, count] of patternCounts) {
      const pattern = this.logPatterns.find(p => p.name === patternName);
      patterns.push({
        pattern: patternName,
        count,
        category: pattern?.category || 'info',
        examples: patternExamples.get(patternName) || []
      });
    }

    return patterns;
  }

  private detectAnomalies(entries: LogEntry[]): any[] {
    const anomalies = [];
    
    // Simple anomaly detection - high error rate
    const errorEntries = entries.filter(e => e.level === 'error');
    const errorRate = entries.length > 0 ? (errorEntries.length / entries.length) * 100 : 0;
    
    if (errorRate > 10) { // More than 10% errors
      anomalies.push({
        type: 'high-error-rate',
        description: `High error rate detected: ${errorRate.toFixed(2)}%`,
        severity: errorRate > 25 ? 'high' : 'medium',
        occurrences: errorEntries.length,
        examples: errorEntries.slice(0, 3).map(e => e.message)
      });
    }

    return anomalies;
  }

  private calculateMetrics(entries: LogEntry[]): any {
    const errorCount = entries.filter(e => e.level === 'error').length;
    const errorRate = entries.length > 0 ? (errorCount / entries.length) * 100 : 0;

    return {
      errorRate,
      // Additional metrics would be calculated here
    };
  }

  private generateSummary(entries: LogEntry[]): any {
    const errorCount = entries.filter(e => e.level === 'error').length;
    const warningCount = entries.filter(e => e.level === 'warn').length;
    
    const timestamps = entries.map(e => e.timestamp).filter(t => t);
    const timeRange = timestamps.length > 0 ? {
      start: new Date(Math.min(...timestamps.map(t => t.getTime()))),
      end: new Date(Math.max(...timestamps.map(t => t.getTime())))
    } : { start: new Date(), end: new Date() };

    return {
      totalLines: entries.length,
      parsedLines: entries.length,
      errorCount,
      warningCount,
      timeRange
    };
  }

  private async parseLogs(logFile: string, format: string, customPattern?: string, includeRaw: boolean = false): Promise<LogEntry[]> {
    const lines = await this.readLogFile(logFile);
    return this.parseLogEntries(lines, format);
  }

  private async searchLogs(
    logFile: string, 
    query: string, 
    timeRange?: any, 
    logLevel?: string, 
    contextLines: number = 0, 
    maxResults: number = 100
  ): Promise<any> {
    const lines = await this.readLogFile(logFile);
    const regex = new RegExp(query, 'i');
    const results = [];

    for (let i = 0; i < lines.length && results.length < maxResults; i++) {
      const line = lines[i];
      if (regex.test(line)) {
        const result: any = {
          lineNumber: i + 1,
          content: line,
          match: line.match(regex)?.[0]
        };

        // Add context lines if requested
        if (contextLines > 0) {
          result.context = {
            before: lines.slice(Math.max(0, i - contextLines), i),
            after: lines.slice(i + 1, Math.min(lines.length, i + 1 + contextLines))
          };
        }

        results.push(result);
      }
    }

    return {
      query,
      totalMatches: results.length,
      results
    };
  }

  private convertToCSV(data: any[]): string {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvLines = [headers.join(',')];
    
    for (const item of data) {
      const values = headers.map(header => {
        const value = item[header];
        return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
      });
      csvLines.push(values.join(','));
    }
    
    return csvLines.join('\n');
  }

  private initializePatterns(): void {
    // Initialize common log patterns
    this.logPatterns = [
      {
        name: 'http-error',
        regex: /HTTP\/\d\.\d"\s+[45]\d{2}/,
        fields: ['status_code'],
        category: 'error'
      },
      {
        name: 'database-error',
        regex: /database|sql|mysql|postgres|connection.*error/i,
        fields: ['error_type'],
        category: 'error'
      },
      {
        name: 'authentication-failure',
        regex: /auth.*fail|login.*fail|invalid.*credential/i,
        fields: ['auth_type'],
        category: 'security'
      },
      {
        name: 'slow-query',
        regex: /slow.*query|query.*time.*\d+ms/i,
        fields: ['duration'],
        category: 'performance'
      }
    ];

    // Initialize security patterns
    this.securityPatterns.set('brute-force', [
      /failed.*login.*attempt/i,
      /authentication.*failed/i,
      /invalid.*password/i
    ]);

    this.securityPatterns.set('injection', [
      /union.*select/i,
      /script.*alert/i,
      /\<script\>/i
    ]);
  }

  // Additional handler methods would be implemented here...
  private async handleDetectAnomalies(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Anomaly detection not fully implemented' }] };
  }

  private async handleSecurityAnalysis(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Security analysis not fully implemented' }] };
  }

  private async handlePerformanceAnalysis(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Performance analysis not fully implemented' }] };
  }

  private async handleAggregateLogs(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Log aggregation not fully implemented' }] };
  }

  private async handleGenerateReport(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Report generation not fully implemented' }] };
  }

  private async handleRealTimeMonitor(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Real-time monitoring not fully implemented' }] };
  }

  private async handleCustomPattern(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Custom pattern not fully implemented' }] };
  }

  private async handleAnalysisResource(uri: string, context: ExecutionContext): Promise<any> {
    return { contents: [{ uri, mimeType: 'application/json', text: '{}' }] };
  }

  private async handleLogInvestigationPrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    const { logFile, issue, timeRange } = args;
    
    return {
      description: `Log investigation for ${issue}`,
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: `Investigate ${issue} in log file ${logFile}${timeRange ? ` during ${timeRange}` : ''}. Analyze patterns, identify root causes, and provide recommendations.`
        }
      }]
    };
  }
}
