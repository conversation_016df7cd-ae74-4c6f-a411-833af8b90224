/**
 * System Monitoring MCP Server
 * 
 * This server provides comprehensive system monitoring capabilities including:
 * - CPU, Memory, and Disk usage monitoring
 * - Process management and monitoring
 * - Network statistics
 * - System health checks
 * - Performance metrics collection
 * - Alert generation
 */

import { z } from 'zod';
import * as si from 'systeminformation';
import * as os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';
import { BaseMCPServer } from '../core/base-server.js';
import {
  MCPServerConfig,
  MCPTool,
  ToolResult,
  ExecutionContext,
  LogLevel
} from '../types/index.js';

const execAsync = promisify(exec);

interface SystemMetrics {
  timestamp: Date;
  cpu: {
    usage: number;
    cores: number;
    temperature?: number;
    speed: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    bytesReceived: number;
    bytesSent: number;
    packetsReceived: number;
    packetsSent: number;
  };
  uptime: number;
  loadAverage: number[];
}

interface ProcessInfo {
  pid: number;
  name: string;
  cpu: number;
  memory: number;
  command: string;
  user: string;
  state: string;
  startTime: Date;
}

interface SystemAlert {
  id: string;
  type: 'cpu' | 'memory' | 'disk' | 'process' | 'network';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  value: number;
  threshold: number;
}

export class SystemMonitoringServer extends BaseMCPServer {
  private metricsHistory: SystemMetrics[] = [];
  private alerts: SystemAlert[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private alertThresholds = {
    cpu: 80,
    memory: 85,
    disk: 90,
    temperature: 70
  };

  constructor() {
    const config: MCPServerConfig = {
      name: 'system-monitoring',
      version: '1.0.0',
      description: 'Comprehensive system monitoring and performance analysis server',
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      }
    };

    super(config);
  }

  protected async initializeServer(): Promise<void> {
    this.log(LogLevel.INFO, 'Initializing System Monitoring Server');

    // Register tools
    this.registerTool(this.createGetSystemInfoTool());
    this.registerTool(this.createGetMetricsTool());
    this.registerTool(this.createGetProcessesTool());
    this.registerTool(this.createKillProcessTool());
    this.registerTool(this.createGetNetworkStatsTool());
    this.registerTool(this.createGetDiskUsageTool());
    this.registerTool(this.createStartMonitoringTool());
    this.registerTool(this.createStopMonitoringTool());
    this.registerTool(this.createGetAlertsTool());
    this.registerTool(this.createSetThresholdsTool());
    this.registerTool(this.createGetHealthCheckTool());
    this.registerTool(this.createGetPerformanceReportTool());

    // Register resources
    this.registerResource({
      uri: 'system://metrics/current',
      name: 'Current System Metrics',
      description: 'Real-time system performance metrics',
      mimeType: 'application/json',
      handler: this.handleCurrentMetricsResource.bind(this)
    });

    this.registerResource({
      uri: 'system://metrics/history',
      name: 'System Metrics History',
      description: 'Historical system performance data',
      mimeType: 'application/json',
      handler: this.handleMetricsHistoryResource.bind(this)
    });

    // Register prompts
    this.registerPrompt({
      name: 'system-health-report',
      description: 'Generate a comprehensive system health report',
      arguments: [
        { name: 'includeHistory', description: 'Include historical data', required: false },
        { name: 'timeRange', description: 'Time range for analysis (1h, 24h, 7d)', required: false }
      ],
      handler: this.handleSystemHealthReportPrompt.bind(this)
    });

    this.registerPrompt({
      name: 'performance-analysis',
      description: 'Analyze system performance and provide recommendations',
      arguments: [
        { name: 'focus', description: 'Focus area (cpu, memory, disk, network)', required: false }
      ],
      handler: this.handlePerformanceAnalysisPrompt.bind(this)
    });

    this.log(LogLevel.INFO, 'System Monitoring Server initialized successfully');
  }

  private createGetSystemInfoTool(): MCPTool {
    return {
      name: 'get-system-info',
      description: 'Get comprehensive system information',
      category: 'system',
      tags: ['system', 'info', 'hardware'],
      inputSchema: z.object({
        includeHardware: z.boolean().default(true).describe('Include hardware details'),
        includeOS: z.boolean().default(true).describe('Include OS information')
      }),
      handler: this.handleGetSystemInfo.bind(this)
    };
  }

  private createGetMetricsTool(): MCPTool {
    return {
      name: 'get-metrics',
      description: 'Get current system performance metrics',
      category: 'system',
      tags: ['metrics', 'performance', 'monitoring'],
      inputSchema: z.object({
        detailed: z.boolean().default(false).describe('Include detailed metrics')
      }),
      handler: this.handleGetMetrics.bind(this)
    };
  }

  private createGetProcessesTool(): MCPTool {
    return {
      name: 'get-processes',
      description: 'List running processes with resource usage',
      category: 'system',
      tags: ['processes', 'monitoring'],
      inputSchema: z.object({
        sortBy: z.enum(['cpu', 'memory', 'name', 'pid']).default('cpu').describe('Sort processes by'),
        limit: z.number().default(20).describe('Maximum number of processes to return'),
        filter: z.string().optional().describe('Filter processes by name')
      }),
      handler: this.handleGetProcesses.bind(this)
    };
  }

  private createKillProcessTool(): MCPTool {
    return {
      name: 'kill-process',
      description: 'Terminate a process by PID or name',
      category: 'system',
      tags: ['process', 'management'],
      security: {
        requiresAuth: true,
        permissions: ['system:process:kill']
      },
      inputSchema: z.object({
        pid: z.number().optional().describe('Process ID to kill'),
        name: z.string().optional().describe('Process name to kill'),
        force: z.boolean().default(false).describe('Force kill (SIGKILL)')
      }),
      handler: this.handleKillProcess.bind(this)
    };
  }

  private createGetNetworkStatsTool(): MCPTool {
    return {
      name: 'get-network-stats',
      description: 'Get network interface statistics',
      category: 'system',
      tags: ['network', 'statistics'],
      inputSchema: z.object({
        interface: z.string().optional().describe('Specific network interface')
      }),
      handler: this.handleGetNetworkStats.bind(this)
    };
  }

  private createGetDiskUsageTool(): MCPTool {
    return {
      name: 'get-disk-usage',
      description: 'Get disk usage information for all mounted filesystems',
      category: 'system',
      tags: ['disk', 'storage', 'usage'],
      inputSchema: z.object({
        path: z.string().optional().describe('Specific path to check')
      }),
      handler: this.handleGetDiskUsage.bind(this)
    };
  }

  private createStartMonitoringTool(): MCPTool {
    return {
      name: 'start-monitoring',
      description: 'Start continuous system monitoring',
      category: 'system',
      tags: ['monitoring', 'continuous'],
      inputSchema: z.object({
        interval: z.number().default(60).describe('Monitoring interval in seconds'),
        enableAlerts: z.boolean().default(true).describe('Enable alert generation')
      }),
      handler: this.handleStartMonitoring.bind(this)
    };
  }

  private createStopMonitoringTool(): MCPTool {
    return {
      name: 'stop-monitoring',
      description: 'Stop continuous system monitoring',
      category: 'system',
      tags: ['monitoring'],
      inputSchema: z.object({}),
      handler: this.handleStopMonitoring.bind(this)
    };
  }

  private createGetAlertsTool(): MCPTool {
    return {
      name: 'get-alerts',
      description: 'Get system alerts and warnings',
      category: 'system',
      tags: ['alerts', 'warnings'],
      inputSchema: z.object({
        severity: z.enum(['low', 'medium', 'high', 'critical']).optional().describe('Filter by severity'),
        limit: z.number().default(50).describe('Maximum number of alerts to return')
      }),
      handler: this.handleGetAlerts.bind(this)
    };
  }

  private createSetThresholdsTool(): MCPTool {
    return {
      name: 'set-thresholds',
      description: 'Set alert thresholds for system metrics',
      category: 'system',
      tags: ['configuration', 'thresholds'],
      inputSchema: z.object({
        cpu: z.number().min(0).max(100).optional().describe('CPU usage threshold (%)'),
        memory: z.number().min(0).max(100).optional().describe('Memory usage threshold (%)'),
        disk: z.number().min(0).max(100).optional().describe('Disk usage threshold (%)'),
        temperature: z.number().min(0).max(100).optional().describe('Temperature threshold (°C)')
      }),
      handler: this.handleSetThresholds.bind(this)
    };
  }

  private createGetHealthCheckTool(): MCPTool {
    return {
      name: 'get-health-check',
      description: 'Perform comprehensive system health check',
      category: 'system',
      tags: ['health', 'check', 'diagnostics'],
      inputSchema: z.object({
        includeRecommendations: z.boolean().default(true).describe('Include optimization recommendations')
      }),
      handler: this.handleGetHealthCheck.bind(this)
    };
  }

  private createGetPerformanceReportTool(): MCPTool {
    return {
      name: 'get-performance-report',
      description: 'Generate detailed performance analysis report',
      category: 'system',
      tags: ['performance', 'report', 'analysis'],
      inputSchema: z.object({
        timeRange: z.enum(['1h', '24h', '7d', '30d']).default('24h').describe('Time range for analysis'),
        format: z.enum(['json', 'summary']).default('json').describe('Report format')
      }),
      handler: this.handleGetPerformanceReport.bind(this)
    };
  }

  private async handleGetSystemInfo(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { includeHardware = true, includeOS = true } = params;
      
      this.log(LogLevel.INFO, 'Getting system information', { context });

      const systemInfo: any = {};

      if (includeOS) {
        systemInfo.os = {
          platform: os.platform(),
          type: os.type(),
          release: os.release(),
          arch: os.arch(),
          hostname: os.hostname(),
          uptime: os.uptime()
        };
      }

      if (includeHardware) {
        const [cpu, memory, system] = await Promise.all([
          si.cpu(),
          si.mem(),
          si.system()
        ]);

        systemInfo.hardware = {
          cpu: {
            manufacturer: cpu.manufacturer,
            brand: cpu.brand,
            cores: cpu.cores,
            physicalCores: cpu.physicalCores,
            speed: cpu.speed
          },
          memory: {
            total: memory.total,
            free: memory.free,
            used: memory.used
          },
          system: {
            manufacturer: system.manufacturer,
            model: system.model,
            version: system.version
          }
        };
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(systemInfo, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Get system info failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error getting system information: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleGetMetrics(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { detailed = false } = params;
      
      this.log(LogLevel.INFO, 'Getting system metrics', { context });

      const metrics = await this.collectSystemMetrics();

      if (!detailed) {
        // Return simplified metrics
        const simplified = {
          timestamp: metrics.timestamp,
          cpu: { usage: metrics.cpu.usage },
          memory: { usage: metrics.memory.usage },
          disk: { usage: metrics.disk.usage },
          uptime: metrics.uptime
        };

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(simplified, null, 2)
          }]
        };
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(metrics, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Get metrics failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error getting system metrics: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleGetProcesses(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { sortBy = 'cpu', limit = 20, filter } = params;
      
      this.log(LogLevel.INFO, 'Getting process list', { context });

      const processes = await si.processes();
      let processData = processes.list.map(proc => ({
        pid: proc.pid,
        name: proc.name,
        cpu: proc.cpu,
        memory: proc.mem,
        command: proc.command,
        user: proc.user,
        state: proc.state,
        startTime: new Date(proc.started)
      }));

      // Apply filter if specified
      if (filter) {
        processData = processData.filter(proc => 
          proc.name.toLowerCase().includes(filter.toLowerCase()) ||
          proc.command.toLowerCase().includes(filter.toLowerCase())
        );
      }

      // Sort processes
      processData.sort((a, b) => {
        switch (sortBy) {
          case 'cpu':
            return b.cpu - a.cpu;
          case 'memory':
            return b.memory - a.memory;
          case 'name':
            return a.name.localeCompare(b.name);
          case 'pid':
            return a.pid - b.pid;
          default:
            return 0;
        }
      });

      // Limit results
      processData = processData.slice(0, limit);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            totalProcesses: processes.all,
            runningProcesses: processes.running,
            processes: processData
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Get processes failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error getting process list: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleStartMonitoring(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { interval = 60, enableAlerts = true } = params;
      
      this.log(LogLevel.INFO, `Starting system monitoring (interval: ${interval}s)`, { context });

      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
      }

      this.monitoringInterval = setInterval(async () => {
        try {
          const metrics = await this.collectSystemMetrics();
          this.metricsHistory.push(metrics);

          // Keep only last 1000 entries
          if (this.metricsHistory.length > 1000) {
            this.metricsHistory = this.metricsHistory.slice(-1000);
          }

          if (enableAlerts) {
            this.checkAlerts(metrics);
          }
        } catch (error) {
          this.log(LogLevel.ERROR, 'Error during monitoring cycle', { error });
        }
      }, interval * 1000);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            message: `System monitoring started with ${interval}s interval`,
            alertsEnabled: enableAlerts
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Start monitoring failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error starting monitoring: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleStopMonitoring(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      this.log(LogLevel.INFO, 'Stopping system monitoring', { context });

      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            message: 'System monitoring stopped',
            metricsCollected: this.metricsHistory.length
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Stop monitoring failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error stopping monitoring: ${error.message}`
        }],
        isError: true
      };
    }
  }

  // Helper methods
  private async collectSystemMetrics(): Promise<SystemMetrics> {
    const [cpuLoad, memory, fsSize, networkStats] = await Promise.all([
      si.currentLoad(),
      si.mem(),
      si.fsSize(),
      si.networkStats()
    ]);

    const cpuTemp = await si.cpuTemperature().catch(() => ({ main: null }));

    return {
      timestamp: new Date(),
      cpu: {
        usage: Math.round(cpuLoad.currentLoad),
        cores: cpuLoad.cpus.length,
        temperature: cpuTemp.main,
        speed: cpuLoad.cpus[0]?.speed || 0
      },
      memory: {
        total: memory.total,
        used: memory.used,
        free: memory.free,
        usage: Math.round((memory.used / memory.total) * 100)
      },
      disk: {
        total: fsSize.reduce((acc, fs) => acc + fs.size, 0),
        used: fsSize.reduce((acc, fs) => acc + fs.used, 0),
        free: fsSize.reduce((acc, fs) => acc + fs.available, 0),
        usage: Math.round((fsSize.reduce((acc, fs) => acc + fs.used, 0) / fsSize.reduce((acc, fs) => acc + fs.size, 0)) * 100)
      },
      network: {
        bytesReceived: networkStats.reduce((acc, net) => acc + net.rx_bytes, 0),
        bytesSent: networkStats.reduce((acc, net) => acc + net.tx_bytes, 0),
        packetsReceived: networkStats.reduce((acc, net) => acc + net.rx_packets, 0),
        packetsSent: networkStats.reduce((acc, net) => acc + net.tx_packets, 0)
      },
      uptime: os.uptime(),
      loadAverage: os.loadavg()
    };
  }

  private checkAlerts(metrics: SystemMetrics): void {
    const alerts: SystemAlert[] = [];

    // CPU alert
    if (metrics.cpu.usage > this.alertThresholds.cpu) {
      alerts.push({
        id: `cpu_${Date.now()}`,
        type: 'cpu',
        severity: metrics.cpu.usage > 95 ? 'critical' : 'high',
        message: `High CPU usage: ${metrics.cpu.usage}%`,
        timestamp: new Date(),
        value: metrics.cpu.usage,
        threshold: this.alertThresholds.cpu
      });
    }

    // Memory alert
    if (metrics.memory.usage > this.alertThresholds.memory) {
      alerts.push({
        id: `memory_${Date.now()}`,
        type: 'memory',
        severity: metrics.memory.usage > 95 ? 'critical' : 'high',
        message: `High memory usage: ${metrics.memory.usage}%`,
        timestamp: new Date(),
        value: metrics.memory.usage,
        threshold: this.alertThresholds.memory
      });
    }

    // Disk alert
    if (metrics.disk.usage > this.alertThresholds.disk) {
      alerts.push({
        id: `disk_${Date.now()}`,
        type: 'disk',
        severity: metrics.disk.usage > 98 ? 'critical' : 'high',
        message: `High disk usage: ${metrics.disk.usage}%`,
        timestamp: new Date(),
        value: metrics.disk.usage,
        threshold: this.alertThresholds.disk
      });
    }

    // Temperature alert
    if (metrics.cpu.temperature && metrics.cpu.temperature > this.alertThresholds.temperature) {
      alerts.push({
        id: `temp_${Date.now()}`,
        type: 'cpu',
        severity: metrics.cpu.temperature > 85 ? 'critical' : 'medium',
        message: `High CPU temperature: ${metrics.cpu.temperature}°C`,
        timestamp: new Date(),
        value: metrics.cpu.temperature,
        threshold: this.alertThresholds.temperature
      });
    }

    this.alerts.push(...alerts);

    // Keep only last 500 alerts
    if (this.alerts.length > 500) {
      this.alerts = this.alerts.slice(-500);
    }

    // Log alerts
    alerts.forEach(alert => {
      this.log(LogLevel.WARN, `System Alert: ${alert.message}`, { alert });
    });
  }

  // Additional handler methods would be implemented here...
  private async handleKillProcess(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for process termination
    return { content: [{ type: 'text', text: 'Kill process not fully implemented' }] };
  }

  private async handleGetNetworkStats(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for network statistics
    return { content: [{ type: 'text', text: 'Network stats not fully implemented' }] };
  }

  private async handleGetDiskUsage(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for disk usage
    return { content: [{ type: 'text', text: 'Disk usage not fully implemented' }] };
  }

  private async handleGetAlerts(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for alerts retrieval
    return { content: [{ type: 'text', text: JSON.stringify(this.alerts, null, 2) }] };
  }

  private async handleSetThresholds(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for threshold configuration
    return { content: [{ type: 'text', text: 'Set thresholds not fully implemented' }] };
  }

  private async handleGetHealthCheck(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for health check
    return { content: [{ type: 'text', text: 'Health check not fully implemented' }] };
  }

  private async handleGetPerformanceReport(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for performance report
    return { content: [{ type: 'text', text: 'Performance report not fully implemented' }] };
  }

  private async handleCurrentMetricsResource(uri: string, context: ExecutionContext): Promise<any> {
    const metrics = await this.collectSystemMetrics();
    return { contents: [{ uri, mimeType: 'application/json', text: JSON.stringify(metrics) }] };
  }

  private async handleMetricsHistoryResource(uri: string, context: ExecutionContext): Promise<any> {
    return { contents: [{ uri, mimeType: 'application/json', text: JSON.stringify(this.metricsHistory) }] };
  }

  private async handleSystemHealthReportPrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    return {
      description: 'System health report prompt',
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: 'Generate a comprehensive system health report including current metrics, alerts, and recommendations.'
        }
      }]
    };
  }

  private async handlePerformanceAnalysisPrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    const { focus = 'general' } = args;
    return {
      description: 'Performance analysis prompt',
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: `Analyze system performance with focus on ${focus} and provide optimization recommendations.`
        }
      }]
    };
  }
}
