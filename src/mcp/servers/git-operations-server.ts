/**
 * Git Operations MCP Server
 * 
 * This server provides comprehensive Git operations including:
 * - Repository management
 * - Branch operations
 * - Commit history analysis
 * - Diff analysis
 * - Merge conflict resolution
 * - Repository statistics
 */

import { z } from 'zod';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs/promises';
import * as path from 'path';
import { BaseMCPServer } from '../core/base-server.js';
import {
  MCPServerConfig,
  MCPTool,
  ToolResult,
  ExecutionContext,
  LogLevel
} from '../types/index.js';

const execAsync = promisify(exec);

interface GitCommit {
  hash: string;
  author: string;
  email: string;
  date: string;
  message: string;
  files: string[];
}

interface GitBranch {
  name: string;
  current: boolean;
  remote: boolean;
  lastCommit: string;
}

interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  staged: string[];
  modified: string[];
  untracked: string[];
  conflicted: string[];
}

interface RepositoryStats {
  totalCommits: number;
  totalBranches: number;
  totalContributors: number;
  linesAdded: number;
  linesDeleted: number;
  mostActiveFiles: Array<{ file: string; commits: number }>;
  contributorStats: Array<{ author: string; commits: number; linesAdded: number; linesDeleted: number }>;
}

export class GitOperationsServer extends BaseMCPServer {
  constructor() {
    const config: MCPServerConfig = {
      name: 'git-operations',
      version: '1.0.0',
      description: 'Comprehensive Git repository operations and analysis server',
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      }
    };

    super(config);
  }

  protected async initializeServer(): Promise<void> {
    this.log(LogLevel.INFO, 'Initializing Git Operations Server');

    // Register tools
    this.registerTool(this.createGetStatusTool());
    this.registerTool(this.createGetHistoryTool());
    this.registerTool(this.createGetBranchesTool());
    this.registerTool(this.createCreateBranchTool());
    this.registerTool(this.createSwitchBranchTool());
    this.registerTool(this.createGetDiffTool());
    this.registerTool(this.createCommitTool());
    this.registerTool(this.createPushTool());
    this.registerTool(this.createPullTool());
    this.registerTool(this.createMergeTool());
    this.registerTool(this.createGetStatsTool());
    this.registerTool(this.createAnalyzeCommitsTool());
    this.registerTool(this.createFindCommitTool());

    // Register resources
    this.registerResource({
      uri: 'git://repository/{repoPath}/commits',
      name: 'Repository Commits',
      description: 'Access to repository commit history',
      mimeType: 'application/json',
      handler: this.handleCommitsResource.bind(this)
    });

    this.registerResource({
      uri: 'git://repository/{repoPath}/branches',
      name: 'Repository Branches',
      description: 'Access to repository branches',
      mimeType: 'application/json',
      handler: this.handleBranchesResource.bind(this)
    });

    // Register prompts
    this.registerPrompt({
      name: 'commit-message',
      description: 'Generate a conventional commit message',
      arguments: [
        { name: 'type', description: 'Commit type (feat, fix, docs, etc.)', required: true },
        { name: 'scope', description: 'Commit scope', required: false },
        { name: 'description', description: 'Brief description of changes', required: true }
      ],
      handler: this.handleCommitMessagePrompt.bind(this)
    });

    this.registerPrompt({
      name: 'release-notes',
      description: 'Generate release notes from commit history',
      arguments: [
        { name: 'repoPath', description: 'Repository path', required: true },
        { name: 'fromTag', description: 'Starting tag/commit', required: false },
        { name: 'toTag', description: 'Ending tag/commit', required: false }
      ],
      handler: this.handleReleaseNotesPrompt.bind(this)
    });

    this.log(LogLevel.INFO, 'Git Operations Server initialized successfully');
  }

  private createGetStatusTool(): MCPTool {
    return {
      name: 'git-status',
      description: 'Get the current status of a Git repository',
      category: 'git',
      tags: ['git', 'status', 'repository'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository')
      }),
      handler: this.handleGetStatus.bind(this)
    };
  }

  private createGetHistoryTool(): MCPTool {
    return {
      name: 'git-history',
      description: 'Get commit history for a repository or specific file',
      category: 'git',
      tags: ['git', 'history', 'commits'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        filePath: z.string().optional().describe('Specific file to get history for'),
        limit: z.number().default(50).describe('Maximum number of commits to return'),
        since: z.string().optional().describe('Show commits since date (YYYY-MM-DD)'),
        author: z.string().optional().describe('Filter by author')
      }),
      handler: this.handleGetHistory.bind(this)
    };
  }

  private createGetBranchesTool(): MCPTool {
    return {
      name: 'git-branches',
      description: 'List all branches in the repository',
      category: 'git',
      tags: ['git', 'branches'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        includeRemote: z.boolean().default(true).describe('Include remote branches')
      }),
      handler: this.handleGetBranches.bind(this)
    };
  }

  private createCreateBranchTool(): MCPTool {
    return {
      name: 'git-create-branch',
      description: 'Create a new branch',
      category: 'git',
      tags: ['git', 'branch', 'create'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        branchName: z.string().describe('Name of the new branch'),
        fromBranch: z.string().optional().describe('Branch to create from (defaults to current)')
      }),
      handler: this.handleCreateBranch.bind(this)
    };
  }

  private createSwitchBranchTool(): MCPTool {
    return {
      name: 'git-switch-branch',
      description: 'Switch to a different branch',
      category: 'git',
      tags: ['git', 'branch', 'switch'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        branchName: z.string().describe('Name of the branch to switch to')
      }),
      handler: this.handleSwitchBranch.bind(this)
    };
  }

  private createGetDiffTool(): MCPTool {
    return {
      name: 'git-diff',
      description: 'Get diff between commits, branches, or working directory',
      category: 'git',
      tags: ['git', 'diff', 'changes'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        from: z.string().optional().describe('Starting commit/branch (defaults to HEAD)'),
        to: z.string().optional().describe('Ending commit/branch (defaults to working directory)'),
        filePath: z.string().optional().describe('Specific file to diff'),
        staged: z.boolean().default(false).describe('Show staged changes only')
      }),
      handler: this.handleGetDiff.bind(this)
    };
  }

  private createCommitTool(): MCPTool {
    return {
      name: 'git-commit',
      description: 'Create a new commit with staged changes',
      category: 'git',
      tags: ['git', 'commit'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        message: z.string().describe('Commit message'),
        addAll: z.boolean().default(false).describe('Stage all changes before committing')
      }),
      handler: this.handleCommit.bind(this)
    };
  }

  private createPushTool(): MCPTool {
    return {
      name: 'git-push',
      description: 'Push commits to remote repository',
      category: 'git',
      tags: ['git', 'push', 'remote'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        remote: z.string().default('origin').describe('Remote name'),
        branch: z.string().optional().describe('Branch to push (defaults to current)')
      }),
      handler: this.handlePush.bind(this)
    };
  }

  private createPullTool(): MCPTool {
    return {
      name: 'git-pull',
      description: 'Pull changes from remote repository',
      category: 'git',
      tags: ['git', 'pull', 'remote'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        remote: z.string().default('origin').describe('Remote name'),
        branch: z.string().optional().describe('Branch to pull (defaults to current)')
      }),
      handler: this.handlePull.bind(this)
    };
  }

  private createMergeTool(): MCPTool {
    return {
      name: 'git-merge',
      description: 'Merge a branch into the current branch',
      category: 'git',
      tags: ['git', 'merge'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        branchName: z.string().describe('Branch to merge'),
        noFastForward: z.boolean().default(false).describe('Force merge commit')
      }),
      handler: this.handleMerge.bind(this)
    };
  }

  private createGetStatsTool(): MCPTool {
    return {
      name: 'git-stats',
      description: 'Get comprehensive repository statistics',
      category: 'git',
      tags: ['git', 'statistics', 'analysis'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        since: z.string().optional().describe('Calculate stats since date (YYYY-MM-DD)')
      }),
      handler: this.handleGetStats.bind(this)
    };
  }

  private createAnalyzeCommitsTool(): MCPTool {
    return {
      name: 'git-analyze-commits',
      description: 'Analyze commit patterns and trends',
      category: 'git',
      tags: ['git', 'analysis', 'commits'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        period: z.enum(['day', 'week', 'month']).default('week').describe('Analysis period'),
        limit: z.number().default(100).describe('Number of commits to analyze')
      }),
      handler: this.handleAnalyzeCommits.bind(this)
    };
  }

  private createFindCommitTool(): MCPTool {
    return {
      name: 'git-find-commit',
      description: 'Find commits by message, author, or file changes',
      category: 'git',
      tags: ['git', 'search', 'commits'],
      inputSchema: z.object({
        repoPath: z.string().describe('Path to the Git repository'),
        query: z.string().describe('Search query'),
        searchType: z.enum(['message', 'author', 'file']).default('message').describe('Type of search'),
        limit: z.number().default(20).describe('Maximum results to return')
      }),
      handler: this.handleFindCommit.bind(this)
    };
  }

  private async handleGetStatus(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { repoPath } = params;
      
      this.log(LogLevel.INFO, `Getting Git status for: ${repoPath}`, { context });

      const status = await this.getGitStatus(repoPath);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(status, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Git status failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error getting Git status: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleGetHistory(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { repoPath, filePath, limit = 50, since, author } = params;
      
      this.log(LogLevel.INFO, `Getting Git history for: ${repoPath}`, { context });

      const commits = await this.getGitHistory(repoPath, filePath, limit, since, author);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            totalCommits: commits.length,
            commits
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Git history failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error getting Git history: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleGetBranches(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { repoPath, includeRemote = true } = params;
      
      this.log(LogLevel.INFO, `Getting Git branches for: ${repoPath}`, { context });

      const branches = await this.getGitBranches(repoPath, includeRemote);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            totalBranches: branches.length,
            currentBranch: branches.find(b => b.current)?.name,
            branches
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Git branches failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error getting Git branches: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleCreateBranch(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { repoPath, branchName, fromBranch } = params;
      
      this.log(LogLevel.INFO, `Creating Git branch: ${branchName}`, { context });

      const result = await this.createGitBranch(repoPath, branchName, fromBranch);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            branchName,
            message: `Branch '${branchName}' created successfully`,
            output: result
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Git create branch failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error creating Git branch: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleSwitchBranch(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { repoPath, branchName } = params;
      
      this.log(LogLevel.INFO, `Switching to Git branch: ${branchName}`, { context });

      const result = await this.switchGitBranch(repoPath, branchName);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            branchName,
            message: `Switched to branch '${branchName}'`,
            output: result
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Git switch branch failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error switching Git branch: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleGetDiff(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { repoPath, from, to, filePath, staged = false } = params;
      
      this.log(LogLevel.INFO, `Getting Git diff for: ${repoPath}`, { context });

      const diff = await this.getGitDiff(repoPath, from, to, filePath, staged);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            diff,
            summary: this.analyzeDiff(diff)
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Git diff failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error getting Git diff: ${error.message}`
        }],
        isError: true
      };
    }
  }

  // Helper methods for Git operations
  private async executeGitCommand(repoPath: string, command: string): Promise<string> {
    const { stdout } = await execAsync(`git -C "${repoPath}" ${command}`);
    return stdout.trim();
  }

  private async getGitStatus(repoPath: string): Promise<GitStatus> {
    const statusOutput = await this.executeGitCommand(repoPath, 'status --porcelain -b');
    const lines = statusOutput.split('\n').filter(line => line.trim());
    
    const branchLine = lines[0];
    const branch = branchLine.match(/## (.+?)(?:\.\.\.|$)/)?.[1] || 'unknown';
    
    const ahead = branchLine.match(/ahead (\d+)/)?.[1] || '0';
    const behind = branchLine.match(/behind (\d+)/)?.[1] || '0';
    
    const staged: string[] = [];
    const modified: string[] = [];
    const untracked: string[] = [];
    const conflicted: string[] = [];
    
    lines.slice(1).forEach(line => {
      const status = line.substring(0, 2);
      const file = line.substring(3);
      
      if (status.includes('U') || status.includes('A') && status.includes('A')) {
        conflicted.push(file);
      } else if (status[0] !== ' ' && status[0] !== '?') {
        staged.push(file);
      } else if (status[1] !== ' ') {
        modified.push(file);
      } else if (status === '??') {
        untracked.push(file);
      }
    });
    
    return {
      branch,
      ahead: parseInt(ahead),
      behind: parseInt(behind),
      staged,
      modified,
      untracked,
      conflicted
    };
  }

  private async getGitHistory(
    repoPath: string, 
    filePath?: string, 
    limit: number = 50, 
    since?: string, 
    author?: string
  ): Promise<GitCommit[]> {
    let command = `log --pretty=format:"%H|%an|%ae|%ad|%s" --date=iso -${limit}`;
    
    if (since) {
      command += ` --since="${since}"`;
    }
    
    if (author) {
      command += ` --author="${author}"`;
    }
    
    if (filePath) {
      command += ` -- "${filePath}"`;
    }
    
    const output = await this.executeGitCommand(repoPath, command);
    const lines = output.split('\n').filter(line => line.trim());
    
    const commits: GitCommit[] = [];
    
    for (const line of lines) {
      const [hash, author, email, date, message] = line.split('|');
      
      // Get files changed in this commit
      const filesOutput = await this.executeGitCommand(repoPath, `show --name-only --pretty=format: ${hash}`);
      const files = filesOutput.split('\n').filter(f => f.trim());
      
      commits.push({
        hash,
        author,
        email,
        date,
        message,
        files
      });
    }
    
    return commits;
  }

  private async getGitBranches(repoPath: string, includeRemote: boolean): Promise<GitBranch[]> {
    const command = includeRemote ? 'branch -a' : 'branch';
    const output = await this.executeGitCommand(repoPath, command);
    const lines = output.split('\n').filter(line => line.trim());
    
    const branches: GitBranch[] = [];
    
    for (const line of lines) {
      const current = line.startsWith('*');
      const remote = line.includes('remotes/');
      const name = line.replace(/^\*?\s+/, '').replace(/^remotes\/[^\/]+\//, '');
      
      if (name && !name.includes('HEAD ->')) {
        // Get last commit for this branch
        try {
          const lastCommit = await this.executeGitCommand(repoPath, `log -1 --pretty=format:"%H" ${name}`);
          branches.push({
            name,
            current,
            remote,
            lastCommit
          });
        } catch (error) {
          // Branch might not exist locally
          branches.push({
            name,
            current,
            remote,
            lastCommit: ''
          });
        }
      }
    }
    
    return branches;
  }

  private async createGitBranch(repoPath: string, branchName: string, fromBranch?: string): Promise<string> {
    const command = fromBranch ? `checkout -b ${branchName} ${fromBranch}` : `checkout -b ${branchName}`;
    return await this.executeGitCommand(repoPath, command);
  }

  private async switchGitBranch(repoPath: string, branchName: string): Promise<string> {
    return await this.executeGitCommand(repoPath, `checkout ${branchName}`);
  }

  private async getGitDiff(
    repoPath: string, 
    from?: string, 
    to?: string, 
    filePath?: string, 
    staged: boolean = false
  ): Promise<string> {
    let command = 'diff';
    
    if (staged) {
      command += ' --staged';
    }
    
    if (from && to) {
      command += ` ${from}..${to}`;
    } else if (from) {
      command += ` ${from}`;
    }
    
    if (filePath) {
      command += ` -- "${filePath}"`;
    }
    
    return await this.executeGitCommand(repoPath, command);
  }

  private analyzeDiff(diff: string): any {
    const lines = diff.split('\n');
    let additions = 0;
    let deletions = 0;
    const files: string[] = [];
    
    lines.forEach(line => {
      if (line.startsWith('+') && !line.startsWith('+++')) {
        additions++;
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        deletions++;
      } else if (line.startsWith('diff --git')) {
        const match = line.match(/diff --git a\/(.+) b\/(.+)/);
        if (match) {
          files.push(match[1]);
        }
      }
    });
    
    return {
      filesChanged: files.length,
      additions,
      deletions,
      files
    };
  }

  // Additional handler methods would continue here...
  private async handleCommit(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for commit operation
    return { content: [{ type: 'text', text: 'Commit operation not fully implemented' }] };
  }

  private async handlePush(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for push operation
    return { content: [{ type: 'text', text: 'Push operation not fully implemented' }] };
  }

  private async handlePull(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for pull operation
    return { content: [{ type: 'text', text: 'Pull operation not fully implemented' }] };
  }

  private async handleMerge(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for merge operation
    return { content: [{ type: 'text', text: 'Merge operation not fully implemented' }] };
  }

  private async handleGetStats(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for repository statistics
    return { content: [{ type: 'text', text: 'Stats operation not fully implemented' }] };
  }

  private async handleAnalyzeCommits(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for commit analysis
    return { content: [{ type: 'text', text: 'Commit analysis not fully implemented' }] };
  }

  private async handleFindCommit(params: any, context: ExecutionContext): Promise<ToolResult> {
    // Implementation for commit search
    return { content: [{ type: 'text', text: 'Commit search not fully implemented' }] };
  }

  private async handleCommitsResource(uri: string, context: ExecutionContext): Promise<any> {
    // Implementation for commits resource
    return { contents: [{ uri, mimeType: 'application/json', text: '{}' }] };
  }

  private async handleBranchesResource(uri: string, context: ExecutionContext): Promise<any> {
    // Implementation for branches resource
    return { contents: [{ uri, mimeType: 'application/json', text: '{}' }] };
  }

  private async handleCommitMessagePrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    const { type, scope, description } = args;
    const message = scope ? `${type}(${scope}): ${description}` : `${type}: ${description}`;
    
    return {
      description: 'Conventional commit message',
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: message
        }
      }]
    };
  }

  private async handleReleaseNotesPrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    const { repoPath, fromTag, toTag } = args;
    
    return {
      description: `Release notes for ${repoPath}`,
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: `Generate release notes for repository ${repoPath} from ${fromTag || 'last release'} to ${toTag || 'current'}`
        }
      }]
    };
  }
}
