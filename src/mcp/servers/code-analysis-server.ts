/**
 * Code Analysis MCP Server
 * 
 * This server provides comprehensive code analysis capabilities including:
 * - Static code analysis
 * - Security vulnerability detection
 * - Code quality metrics
 * - Dependency analysis
 * - Code complexity analysis
 */

import { z } from 'zod';
import * as fs from 'fs/promises';
import * as path from 'path';
import { glob } from 'fast-glob';
import { BaseMCPServer } from '../core/base-server.js';
import {
  MCPServerConfig,
  MCPTool,
  ToolResult,
  ExecutionContext,
  LogLevel
} from '../types/index.js';

interface CodeMetrics {
  linesOfCode: number;
  complexity: number;
  maintainabilityIndex: number;
  technicalDebt: number;
}

interface SecurityIssue {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  line: number;
  file: string;
  recommendation: string;
}

interface DependencyInfo {
  name: string;
  version: string;
  vulnerabilities: number;
  outdated: boolean;
  license: string;
}

export class CodeAnalysisServer extends BaseMCPServer {
  private supportedExtensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs', '.php', '.rb', '.go'];
  private securityPatterns: Map<string, RegExp[]> = new Map();

  constructor() {
    const config: MCPServerConfig = {
      name: 'code-analysis',
      version: '1.0.0',
      description: 'Comprehensive code analysis and security scanning server',
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      }
    };

    super(config);
    this.initializeSecurityPatterns();
  }

  protected async initializeServer(): Promise<void> {
    this.log(LogLevel.INFO, 'Initializing Code Analysis Server');

    // Register tools
    this.registerTool(this.createAnalyzeCodeTool());
    this.registerTool(this.createScanSecurityTool());
    this.registerTool(this.createCalculateMetricsTool());
    this.registerTool(this.createAnalyzeDependenciesTool());
    this.registerTool(this.createGenerateReportTool());

    // Register resources
    this.registerResource({
      uri: 'code-analysis://reports/{reportId}',
      name: 'Analysis Reports',
      description: 'Access to generated code analysis reports',
      mimeType: 'application/json',
      handler: this.handleReportResource.bind(this)
    });

    // Register prompts
    this.registerPrompt({
      name: 'code-review',
      description: 'Generate a comprehensive code review prompt',
      arguments: [
        { name: 'directory', description: 'Directory to analyze', required: true },
        { name: 'focus', description: 'Focus area (security, performance, maintainability)', required: false }
      ],
      handler: this.handleCodeReviewPrompt.bind(this)
    });

    this.log(LogLevel.INFO, 'Code Analysis Server initialized successfully');
  }

  private createAnalyzeCodeTool(): MCPTool {
    return {
      name: 'analyze-code',
      description: 'Perform comprehensive static code analysis on files or directories',
      category: 'development',
      tags: ['code-analysis', 'static-analysis', 'quality'],
      inputSchema: z.object({
        path: z.string().describe('File or directory path to analyze'),
        recursive: z.boolean().default(true).describe('Recursively analyze subdirectories'),
        extensions: z.array(z.string()).optional().describe('File extensions to include'),
        excludePatterns: z.array(z.string()).optional().describe('Patterns to exclude')
      }),
      handler: this.handleAnalyzeCode.bind(this)
    };
  }

  private createScanSecurityTool(): MCPTool {
    return {
      name: 'scan-security',
      description: 'Scan code for security vulnerabilities and potential threats',
      category: 'security',
      tags: ['security', 'vulnerability', 'scanning'],
      inputSchema: z.object({
        path: z.string().describe('File or directory path to scan'),
        severity: z.enum(['low', 'medium', 'high', 'critical']).optional().describe('Minimum severity level'),
        includePatterns: z.array(z.string()).optional().describe('Security patterns to include')
      }),
      handler: this.handleScanSecurity.bind(this)
    };
  }

  private createCalculateMetricsTool(): MCPTool {
    return {
      name: 'calculate-metrics',
      description: 'Calculate code quality metrics and complexity analysis',
      category: 'development',
      tags: ['metrics', 'complexity', 'quality'],
      inputSchema: z.object({
        path: z.string().describe('File or directory path to analyze'),
        includeTests: z.boolean().default(false).describe('Include test files in metrics')
      }),
      handler: this.handleCalculateMetrics.bind(this)
    };
  }

  private createAnalyzeDependenciesTool(): MCPTool {
    return {
      name: 'analyze-dependencies',
      description: 'Analyze project dependencies for vulnerabilities and updates',
      category: 'development',
      tags: ['dependencies', 'security', 'updates'],
      inputSchema: z.object({
        projectPath: z.string().describe('Project root directory path'),
        packageManager: z.enum(['npm', 'yarn', 'pip', 'maven', 'gradle']).optional().describe('Package manager type')
      }),
      handler: this.handleAnalyzeDependencies.bind(this)
    };
  }

  private createGenerateReportTool(): MCPTool {
    return {
      name: 'generate-report',
      description: 'Generate comprehensive analysis report',
      category: 'reporting',
      tags: ['report', 'analysis', 'summary'],
      inputSchema: z.object({
        path: z.string().describe('Path that was analyzed'),
        format: z.enum(['json', 'html', 'markdown']).default('json').describe('Report format'),
        includeRecommendations: z.boolean().default(true).describe('Include improvement recommendations')
      }),
      handler: this.handleGenerateReport.bind(this)
    };
  }

  private async handleAnalyzeCode(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { path: targetPath, recursive = true, extensions, excludePatterns = [] } = params;
      
      this.log(LogLevel.INFO, `Starting code analysis for: ${targetPath}`, { context });

      // Get files to analyze
      const files = await this.getFilesToAnalyze(targetPath, recursive, extensions, excludePatterns);
      
      if (files.length === 0) {
        return {
          content: [{
            type: 'text',
            text: 'No files found to analyze with the specified criteria.'
          }]
        };
      }

      const results = [];
      let totalLines = 0;
      let totalFiles = files.length;

      for (const file of files) {
        try {
          const content = await fs.readFile(file, 'utf-8');
          const lines = content.split('\n').length;
          totalLines += lines;

          const analysis = await this.analyzeFile(file, content);
          results.push({
            file: path.relative(targetPath, file),
            ...analysis
          });
        } catch (error) {
          this.log(LogLevel.WARN, `Failed to analyze file: ${file}`, { error });
        }
      }

      const summary = {
        totalFiles,
        totalLines,
        averageLinesPerFile: Math.round(totalLines / totalFiles),
        analysisResults: results
      };

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(summary, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Code analysis failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error during code analysis: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleScanSecurity(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { path: targetPath, severity = 'low', includePatterns } = params;
      
      this.log(LogLevel.INFO, `Starting security scan for: ${targetPath}`, { context });

      const files = await this.getFilesToAnalyze(targetPath, true);
      const securityIssues: SecurityIssue[] = [];

      for (const file of files) {
        try {
          const content = await fs.readFile(file, 'utf-8');
          const issues = await this.scanFileForSecurity(file, content, severity, includePatterns);
          securityIssues.push(...issues);
        } catch (error) {
          this.log(LogLevel.WARN, `Failed to scan file: ${file}`, { error });
        }
      }

      const severityCounts = {
        critical: securityIssues.filter(i => i.severity === 'critical').length,
        high: securityIssues.filter(i => i.severity === 'high').length,
        medium: securityIssues.filter(i => i.severity === 'medium').length,
        low: securityIssues.filter(i => i.severity === 'low').length
      };

      const result = {
        summary: {
          totalIssues: securityIssues.length,
          severityBreakdown: severityCounts,
          filesScanned: files.length
        },
        issues: securityIssues
      };

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Security scan failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error during security scan: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleCalculateMetrics(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { path: targetPath, includeTests = false } = params;
      
      this.log(LogLevel.INFO, `Calculating metrics for: ${targetPath}`, { context });

      const files = await this.getFilesToAnalyze(targetPath, true);
      const filteredFiles = includeTests ? files : files.filter(f => !this.isTestFile(f));

      let totalMetrics: CodeMetrics = {
        linesOfCode: 0,
        complexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0
      };

      const fileMetrics = [];

      for (const file of filteredFiles) {
        try {
          const content = await fs.readFile(file, 'utf-8');
          const metrics = await this.calculateFileMetrics(file, content);
          fileMetrics.push({
            file: path.relative(targetPath, file),
            ...metrics
          });

          totalMetrics.linesOfCode += metrics.linesOfCode;
          totalMetrics.complexity += metrics.complexity;
          totalMetrics.maintainabilityIndex += metrics.maintainabilityIndex;
          totalMetrics.technicalDebt += metrics.technicalDebt;
        } catch (error) {
          this.log(LogLevel.WARN, `Failed to calculate metrics for file: ${file}`, { error });
        }
      }

      // Calculate averages
      const fileCount = filteredFiles.length;
      if (fileCount > 0) {
        totalMetrics.maintainabilityIndex = Math.round(totalMetrics.maintainabilityIndex / fileCount);
        totalMetrics.complexity = Math.round(totalMetrics.complexity / fileCount);
      }

      const result = {
        summary: totalMetrics,
        filesAnalyzed: fileCount,
        fileMetrics: fileMetrics
      };

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Metrics calculation failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error calculating metrics: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleAnalyzeDependencies(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { projectPath, packageManager } = params;
      
      this.log(LogLevel.INFO, `Analyzing dependencies for: ${projectPath}`, { context });

      const dependencies = await this.analyzeDependencies(projectPath, packageManager);

      const result = {
        summary: {
          totalDependencies: dependencies.length,
          vulnerableDependencies: dependencies.filter(d => d.vulnerabilities > 0).length,
          outdatedDependencies: dependencies.filter(d => d.outdated).length
        },
        dependencies
      };

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Dependency analysis failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error analyzing dependencies: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleGenerateReport(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { path: targetPath, format = 'json', includeRecommendations = true } = params;
      
      this.log(LogLevel.INFO, `Generating ${format} report for: ${targetPath}`, { context });

      // Run all analyses
      const codeAnalysis = await this.handleAnalyzeCode({ path: targetPath }, context);
      const securityScan = await this.handleScanSecurity({ path: targetPath }, context);
      const metrics = await this.handleCalculateMetrics({ path: targetPath }, context);

      const report = {
        metadata: {
          generatedAt: new Date().toISOString(),
          targetPath,
          format,
          serverVersion: this.config.version
        },
        codeAnalysis: JSON.parse(codeAnalysis.content[0].text || '{}'),
        securityScan: JSON.parse(securityScan.content[0].text || '{}'),
        metrics: JSON.parse(metrics.content[0].text || '{}'),
        recommendations: includeRecommendations ? this.generateRecommendations() : []
      };

      let formattedReport: string;
      switch (format) {
        case 'html':
          formattedReport = this.formatReportAsHTML(report);
          break;
        case 'markdown':
          formattedReport = this.formatReportAsMarkdown(report);
          break;
        default:
          formattedReport = JSON.stringify(report, null, 2);
      }

      return {
        content: [{
          type: 'text',
          text: formattedReport
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Report generation failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error generating report: ${error.message}`
        }],
        isError: true
      };
    }
  }

  // Helper methods will be added in the next part due to length constraints
  private async getFilesToAnalyze(
    targetPath: string, 
    recursive: boolean = true, 
    extensions?: string[], 
    excludePatterns: string[] = []
  ): Promise<string[]> {
    const stats = await fs.stat(targetPath);
    
    if (stats.isFile()) {
      return [targetPath];
    }

    const fileExtensions = extensions || this.supportedExtensions;
    const patterns = fileExtensions.map(ext => 
      recursive ? `${targetPath}/**/*${ext}` : `${targetPath}/*${ext}`
    );

    const files = await glob(patterns, {
      ignore: [
        '**/node_modules/**',
        '**/dist/**',
        '**/build/**',
        '**/.git/**',
        ...excludePatterns
      ]
    });

    return files;
  }

  private async analyzeFile(filePath: string, content: string): Promise<any> {
    const lines = content.split('\n');
    const extension = path.extname(filePath);
    
    return {
      linesOfCode: lines.length,
      extension,
      size: content.length,
      complexity: this.calculateComplexity(content, extension),
      issues: this.findCodeIssues(content, extension)
    };
  }

  private calculateComplexity(content: string, extension: string): number {
    // Simple complexity calculation based on control structures
    const complexityPatterns = [
      /\bif\b/g, /\belse\b/g, /\bwhile\b/g, /\bfor\b/g,
      /\bswitch\b/g, /\bcatch\b/g, /\btry\b/g, /\?\s*:/g
    ];
    
    let complexity = 1; // Base complexity
    complexityPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    });
    
    return complexity;
  }

  private findCodeIssues(content: string, extension: string): string[] {
    const issues = [];
    
    // Common code issues
    if (content.includes('console.log')) {
      issues.push('Contains console.log statements');
    }
    if (content.includes('TODO') || content.includes('FIXME')) {
      issues.push('Contains TODO/FIXME comments');
    }
    if (content.match(/\s{4,}/)) {
      issues.push('Inconsistent indentation detected');
    }
    
    return issues;
  }

  private initializeSecurityPatterns(): void {
    // Initialize security patterns for different languages
    this.securityPatterns.set('javascript', [
      /eval\s*\(/g, // eval usage
      /innerHTML\s*=/g, // innerHTML assignment
      /document\.write\s*\(/g, // document.write
      /\$\{.*\}/g, // template literal injection
    ]);

    this.securityPatterns.set('sql', [
      /SELECT.*FROM.*WHERE.*=.*\+/g, // SQL injection
      /INSERT.*VALUES.*\+/g, // SQL injection in INSERT
    ]);
  }

  private async scanFileForSecurity(
    filePath: string, 
    content: string, 
    minSeverity: string, 
    includePatterns?: string[]
  ): Promise<SecurityIssue[]> {
    const issues: SecurityIssue[] = [];
    const lines = content.split('\n');
    const extension = path.extname(filePath).substring(1);
    
    const patterns = this.securityPatterns.get(extension) || [];
    
    patterns.forEach(pattern => {
      lines.forEach((line, index) => {
        if (pattern.test(line)) {
          issues.push({
            type: 'potential-vulnerability',
            severity: 'medium',
            description: `Potential security issue detected: ${pattern.source}`,
            line: index + 1,
            file: filePath,
            recommendation: 'Review this code for security implications'
          });
        }
      });
    });
    
    return issues;
  }

  private async calculateFileMetrics(filePath: string, content: string): Promise<CodeMetrics> {
    const lines = content.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    
    return {
      linesOfCode: nonEmptyLines.length,
      complexity: this.calculateComplexity(content, path.extname(filePath)),
      maintainabilityIndex: Math.max(0, 171 - 5.2 * Math.log(nonEmptyLines.length) - 0.23 * this.calculateComplexity(content, path.extname(filePath))),
      technicalDebt: Math.round(nonEmptyLines.length * 0.1) // Simple estimation
    };
  }

  private async analyzeDependencies(projectPath: string, packageManager?: string): Promise<DependencyInfo[]> {
    // Simplified dependency analysis - in real implementation, would parse package files
    return [
      {
        name: 'example-package',
        version: '1.0.0',
        vulnerabilities: 0,
        outdated: false,
        license: 'MIT'
      }
    ];
  }

  private isTestFile(filePath: string): boolean {
    const testPatterns = [
      /\.test\./,
      /\.spec\./,
      /\/test\//,
      /\/tests\//,
      /__tests__/
    ];
    
    return testPatterns.some(pattern => pattern.test(filePath));
  }

  private generateRecommendations(): string[] {
    return [
      'Consider implementing automated code formatting',
      'Add comprehensive unit tests',
      'Review security patterns and implement secure coding practices',
      'Consider refactoring complex functions to improve maintainability'
    ];
  }

  private formatReportAsHTML(report: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Code Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #e9e9e9; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Code Analysis Report</h1>
        <p>Generated: ${report.metadata.generatedAt}</p>
        <p>Target: ${report.metadata.targetPath}</p>
    </div>
    <div class="section">
        <h2>Summary</h2>
        <div class="metric">Files: ${report.codeAnalysis.totalFiles || 0}</div>
        <div class="metric">Lines: ${report.codeAnalysis.totalLines || 0}</div>
        <div class="metric">Security Issues: ${report.securityScan.summary?.totalIssues || 0}</div>
    </div>
</body>
</html>`;
  }

  private formatReportAsMarkdown(report: any): string {
    return `# Code Analysis Report

**Generated:** ${report.metadata.generatedAt}
**Target:** ${report.metadata.targetPath}

## Summary
- **Files Analyzed:** ${report.codeAnalysis.totalFiles || 0}
- **Total Lines:** ${report.codeAnalysis.totalLines || 0}
- **Security Issues:** ${report.securityScan.summary?.totalIssues || 0}

## Recommendations
${report.recommendations.map((rec: string) => `- ${rec}`).join('\n')}
`;
  }

  private async handleReportResource(uri: string, context: ExecutionContext): Promise<any> {
    // Implementation for accessing stored reports
    return {
      contents: [{
        uri,
        mimeType: 'application/json',
        text: JSON.stringify({ message: 'Report resource handler not fully implemented' })
      }]
    };
  }

  private async handleCodeReviewPrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    const { directory, focus = 'general' } = args;
    
    return {
      description: `Code review prompt for ${directory}`,
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: `Please perform a comprehensive code review of the ${directory} directory, focusing on ${focus} aspects. Analyze code quality, security, performance, and maintainability.`
        }
      }]
    };
  }
}
