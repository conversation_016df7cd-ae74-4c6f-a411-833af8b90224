/**
 * File Manipulation MCP Server
 * 
 * This server provides comprehensive file and directory operations including:
 * - File CRUD operations (Create, Read, Update, Delete)
 * - Directory management
 * - File search and filtering
 * - Batch operations
 * - File metadata analysis
 * - Content transformation
 */

import { z } from 'zod';
import * as fs from 'fs/promises';
import * as path from 'path';
import { glob } from 'fast-glob';
import { createHash } from 'crypto';
import { BaseMCPServer } from '../core/base-server.js';
import {
  MCPServerConfig,
  MCPTool,
  ToolResult,
  ExecutionContext,
  LogLevel
} from '../types/index.js';

interface FileInfo {
  path: string;
  name: string;
  size: number;
  type: 'file' | 'directory';
  extension?: string;
  mimeType?: string;
  created: Date;
  modified: Date;
  accessed: Date;
  permissions: string;
  hash?: string;
}

interface SearchResult {
  files: FileInfo[];
  totalFound: number;
  searchTime: number;
  query: string;
}

interface BatchOperationResult {
  successful: string[];
  failed: Array<{ path: string; error: string }>;
  totalProcessed: number;
}

export class FileManipulationServer extends BaseMCPServer {
  private allowedPaths: string[] = [];
  private maxFileSize = 100 * 1024 * 1024; // 100MB default
  private supportedMimeTypes = new Map([
    ['.txt', 'text/plain'],
    ['.json', 'application/json'],
    ['.js', 'text/javascript'],
    ['.ts', 'text/typescript'],
    ['.html', 'text/html'],
    ['.css', 'text/css'],
    ['.md', 'text/markdown'],
    ['.xml', 'application/xml'],
    ['.csv', 'text/csv'],
    ['.log', 'text/plain']
  ]);

  constructor(allowedPaths: string[] = []) {
    const config: MCPServerConfig = {
      name: 'file-manipulation',
      version: '1.0.0',
      description: 'Comprehensive file and directory manipulation server with security controls',
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      },
      security: {
        authorization: {
          enabled: true,
          policies: ['file:read', 'file:write', 'file:delete']
        }
      }
    };

    super(config);
    this.allowedPaths = allowedPaths.length > 0 ? allowedPaths : [process.cwd()];
  }

  protected async initializeServer(): Promise<void> {
    this.log(LogLevel.INFO, 'Initializing File Manipulation Server');

    // Register tools
    this.registerTool(this.createReadFileTool());
    this.registerTool(this.createWriteFileTool());
    this.registerTool(this.createDeleteFileTool());
    this.registerTool(this.createCopyFileTool());
    this.registerTool(this.createMoveFileTool());
    this.registerTool(this.createCreateDirectoryTool());
    this.registerTool(this.createListDirectoryTool());
    this.registerTool(this.createSearchFilesTool());
    this.registerTool(this.createGetFileInfoTool());
    this.registerTool(this.createBatchOperationTool());
    this.registerTool(this.createFindDuplicatesTool());
    this.registerTool(this.createCompressFilesTool());

    // Register resources
    this.registerResource({
      uri: 'file://{filePath}',
      name: 'File Content',
      description: 'Access to file contents',
      mimeType: 'text/plain',
      handler: this.handleFileResource.bind(this)
    });

    this.registerResource({
      uri: 'directory://{dirPath}',
      name: 'Directory Listing',
      description: 'Directory contents and metadata',
      mimeType: 'application/json',
      handler: this.handleDirectoryResource.bind(this)
    });

    // Register prompts
    this.registerPrompt({
      name: 'file-organization',
      description: 'Generate file organization recommendations',
      arguments: [
        { name: 'directory', description: 'Directory to analyze', required: true },
        { name: 'criteria', description: 'Organization criteria (type, date, size)', required: false }
      ],
      handler: this.handleFileOrganizationPrompt.bind(this)
    });

    this.log(LogLevel.INFO, 'File Manipulation Server initialized successfully');
  }

  private createReadFileTool(): MCPTool {
    return {
      name: 'read-file',
      description: 'Read content from a file',
      category: 'file',
      tags: ['file', 'read', 'content'],
      security: {
        requiresAuth: false,
        permissions: ['file:read']
      },
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to read'),
        encoding: z.enum(['utf8', 'base64', 'hex']).default('utf8').describe('File encoding'),
        maxSize: z.number().optional().describe('Maximum file size to read (bytes)')
      }),
      handler: this.handleReadFile.bind(this)
    };
  }

  private createWriteFileTool(): MCPTool {
    return {
      name: 'write-file',
      description: 'Write content to a file',
      category: 'file',
      tags: ['file', 'write', 'content'],
      security: {
        requiresAuth: true,
        permissions: ['file:write']
      },
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to write'),
        content: z.string().describe('Content to write to the file'),
        encoding: z.enum(['utf8', 'base64', 'hex']).default('utf8').describe('Content encoding'),
        createDirectories: z.boolean().default(false).describe('Create parent directories if they don\'t exist'),
        backup: z.boolean().default(false).describe('Create backup of existing file')
      }),
      handler: this.handleWriteFile.bind(this)
    };
  }

  private createDeleteFileTool(): MCPTool {
    return {
      name: 'delete-file',
      description: 'Delete a file or directory',
      category: 'file',
      tags: ['file', 'delete', 'remove'],
      security: {
        requiresAuth: true,
        permissions: ['file:delete']
      },
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file or directory to delete'),
        recursive: z.boolean().default(false).describe('Delete directories recursively'),
        force: z.boolean().default(false).describe('Force deletion without confirmation')
      }),
      handler: this.handleDeleteFile.bind(this)
    };
  }

  private createCopyFileTool(): MCPTool {
    return {
      name: 'copy-file',
      description: 'Copy a file or directory to another location',
      category: 'file',
      tags: ['file', 'copy', 'duplicate'],
      security: {
        requiresAuth: true,
        permissions: ['file:read', 'file:write']
      },
      inputSchema: z.object({
        sourcePath: z.string().describe('Source file or directory path'),
        destinationPath: z.string().describe('Destination path'),
        overwrite: z.boolean().default(false).describe('Overwrite existing files'),
        preserveTimestamps: z.boolean().default(true).describe('Preserve original timestamps')
      }),
      handler: this.handleCopyFile.bind(this)
    };
  }

  private createMoveFileTool(): MCPTool {
    return {
      name: 'move-file',
      description: 'Move or rename a file or directory',
      category: 'file',
      tags: ['file', 'move', 'rename'],
      security: {
        requiresAuth: true,
        permissions: ['file:write', 'file:delete']
      },
      inputSchema: z.object({
        sourcePath: z.string().describe('Source file or directory path'),
        destinationPath: z.string().describe('Destination path'),
        overwrite: z.boolean().default(false).describe('Overwrite existing files')
      }),
      handler: this.handleMoveFile.bind(this)
    };
  }

  private createCreateDirectoryTool(): MCPTool {
    return {
      name: 'create-directory',
      description: 'Create a new directory',
      category: 'file',
      tags: ['directory', 'create', 'mkdir'],
      security: {
        requiresAuth: true,
        permissions: ['file:write']
      },
      inputSchema: z.object({
        dirPath: z.string().describe('Path of the directory to create'),
        recursive: z.boolean().default(true).describe('Create parent directories if they don\'t exist'),
        mode: z.string().optional().describe('Directory permissions (octal, e.g., "755")')
      }),
      handler: this.handleCreateDirectory.bind(this)
    };
  }

  private createListDirectoryTool(): MCPTool {
    return {
      name: 'list-directory',
      description: 'List contents of a directory',
      category: 'file',
      tags: ['directory', 'list', 'ls'],
      security: {
        requiresAuth: false,
        permissions: ['file:read']
      },
      inputSchema: z.object({
        dirPath: z.string().describe('Directory path to list'),
        recursive: z.boolean().default(false).describe('List subdirectories recursively'),
        includeHidden: z.boolean().default(false).describe('Include hidden files'),
        sortBy: z.enum(['name', 'size', 'modified', 'type']).default('name').describe('Sort criteria'),
        filter: z.string().optional().describe('Filter pattern (glob)')
      }),
      handler: this.handleListDirectory.bind(this)
    };
  }

  private createSearchFilesTool(): MCPTool {
    return {
      name: 'search-files',
      description: 'Search for files based on various criteria',
      category: 'file',
      tags: ['search', 'find', 'filter'],
      inputSchema: z.object({
        searchPath: z.string().describe('Root path to search from'),
        pattern: z.string().optional().describe('File name pattern (glob)'),
        content: z.string().optional().describe('Search for content within files'),
        extension: z.string().optional().describe('File extension filter'),
        minSize: z.number().optional().describe('Minimum file size (bytes)'),
        maxSize: z.number().optional().describe('Maximum file size (bytes)'),
        modifiedAfter: z.string().optional().describe('Modified after date (ISO string)'),
        modifiedBefore: z.string().optional().describe('Modified before date (ISO string)'),
        limit: z.number().default(100).describe('Maximum results to return')
      }),
      handler: this.handleSearchFiles.bind(this)
    };
  }

  private createGetFileInfoTool(): MCPTool {
    return {
      name: 'get-file-info',
      description: 'Get detailed information about a file or directory',
      category: 'file',
      tags: ['info', 'metadata', 'stats'],
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file or directory'),
        includeHash: z.boolean().default(false).describe('Calculate file hash'),
        hashAlgorithm: z.enum(['md5', 'sha1', 'sha256']).default('sha256').describe('Hash algorithm to use')
      }),
      handler: this.handleGetFileInfo.bind(this)
    };
  }

  private createBatchOperationTool(): MCPTool {
    return {
      name: 'batch-operation',
      description: 'Perform batch operations on multiple files',
      category: 'file',
      tags: ['batch', 'bulk', 'multiple'],
      security: {
        requiresAuth: true,
        permissions: ['file:write']
      },
      inputSchema: z.object({
        operation: z.enum(['copy', 'move', 'delete', 'rename']).describe('Operation to perform'),
        files: z.array(z.string()).describe('List of file paths'),
        destination: z.string().optional().describe('Destination directory (for copy/move)'),
        pattern: z.string().optional().describe('Rename pattern with {name}, {ext} placeholders'),
        dryRun: z.boolean().default(false).describe('Preview operations without executing')
      }),
      handler: this.handleBatchOperation.bind(this)
    };
  }

  private createFindDuplicatesTool(): MCPTool {
    return {
      name: 'find-duplicates',
      description: 'Find duplicate files based on content hash',
      category: 'file',
      tags: ['duplicates', 'hash', 'cleanup'],
      inputSchema: z.object({
        searchPath: z.string().describe('Root path to search for duplicates'),
        minSize: z.number().default(1024).describe('Minimum file size to consider (bytes)'),
        hashAlgorithm: z.enum(['md5', 'sha1', 'sha256']).default('md5').describe('Hash algorithm for comparison'),
        includeEmpty: z.boolean().default(false).describe('Include empty files')
      }),
      handler: this.handleFindDuplicates.bind(this)
    };
  }

  private createCompressFilesTool(): MCPTool {
    return {
      name: 'compress-files',
      description: 'Compress files or directories into archives',
      category: 'file',
      tags: ['compress', 'archive', 'zip'],
      security: {
        requiresAuth: true,
        permissions: ['file:read', 'file:write']
      },
      inputSchema: z.object({
        sources: z.array(z.string()).describe('Files or directories to compress'),
        outputPath: z.string().describe('Output archive path'),
        format: z.enum(['zip', 'tar', 'gzip']).default('zip').describe('Archive format'),
        compressionLevel: z.number().min(0).max(9).default(6).describe('Compression level (0-9)')
      }),
      handler: this.handleCompressFiles.bind(this)
    };
  }

  private async handleReadFile(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { filePath, encoding = 'utf8', maxSize } = params;
      
      this.log(LogLevel.INFO, `Reading file: ${filePath}`, { context });

      // Security check
      if (!this.isPathAllowed(filePath)) {
        throw new Error(`Access denied: Path ${filePath} is not in allowed directories`);
      }

      const stats = await fs.stat(filePath);
      
      if (!stats.isFile()) {
        throw new Error(`Path ${filePath} is not a file`);
      }

      const fileSize = stats.size;
      const sizeLimit = maxSize || this.maxFileSize;
      
      if (fileSize > sizeLimit) {
        throw new Error(`File size (${fileSize} bytes) exceeds limit (${sizeLimit} bytes)`);
      }

      const content = await fs.readFile(filePath, encoding as BufferEncoding);
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            filePath,
            size: fileSize,
            encoding,
            content: content.toString()
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Read file failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error reading file: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleWriteFile(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { filePath, content, encoding = 'utf8', createDirectories = false, backup = false } = params;
      
      this.log(LogLevel.INFO, `Writing file: ${filePath}`, { context });

      // Security check
      if (!this.isPathAllowed(filePath)) {
        throw new Error(`Access denied: Path ${filePath} is not in allowed directories`);
      }

      // Create backup if requested and file exists
      if (backup) {
        try {
          await fs.access(filePath);
          const backupPath = `${filePath}.backup.${Date.now()}`;
          await fs.copyFile(filePath, backupPath);
          this.log(LogLevel.INFO, `Created backup: ${backupPath}`);
        } catch (error) {
          // File doesn't exist, no backup needed
        }
      }

      // Create parent directories if requested
      if (createDirectories) {
        const dir = path.dirname(filePath);
        await fs.mkdir(dir, { recursive: true });
      }

      await fs.writeFile(filePath, content, encoding as BufferEncoding);
      
      const stats = await fs.stat(filePath);
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            filePath,
            size: stats.size,
            message: 'File written successfully'
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Write file failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error writing file: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleListDirectory(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { dirPath, recursive = false, includeHidden = false, sortBy = 'name', filter } = params;
      
      this.log(LogLevel.INFO, `Listing directory: ${dirPath}`, { context });

      // Security check
      if (!this.isPathAllowed(dirPath)) {
        throw new Error(`Access denied: Path ${dirPath} is not in allowed directories`);
      }

      const files = await this.listDirectoryContents(dirPath, recursive, includeHidden, filter);
      
      // Sort files
      files.sort((a, b) => {
        switch (sortBy) {
          case 'size':
            return b.size - a.size;
          case 'modified':
            return b.modified.getTime() - a.modified.getTime();
          case 'type':
            return a.type.localeCompare(b.type);
          default:
            return a.name.localeCompare(b.name);
        }
      });

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            directory: dirPath,
            totalFiles: files.length,
            files
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'List directory failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error listing directory: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleSearchFiles(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { 
        searchPath, 
        pattern, 
        content, 
        extension, 
        minSize, 
        maxSize, 
        modifiedAfter, 
        modifiedBefore, 
        limit = 100 
      } = params;
      
      this.log(LogLevel.INFO, `Searching files in: ${searchPath}`, { context });

      const startTime = Date.now();
      const results = await this.searchFiles({
        searchPath,
        pattern,
        content,
        extension,
        minSize,
        maxSize,
        modifiedAfter: modifiedAfter ? new Date(modifiedAfter) : undefined,
        modifiedBefore: modifiedBefore ? new Date(modifiedBefore) : undefined,
        limit
      });
      
      const searchTime = Date.now() - startTime;

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            ...results,
            searchTime
          }, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Search files failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error searching files: ${error.message}`
        }],
        isError: true
      };
    }
  }

  // Helper methods
  private isPathAllowed(filePath: string): boolean {
    const absolutePath = path.resolve(filePath);
    return this.allowedPaths.some(allowedPath => 
      absolutePath.startsWith(path.resolve(allowedPath))
    );
  }

  private async listDirectoryContents(
    dirPath: string, 
    recursive: boolean, 
    includeHidden: boolean, 
    filter?: string
  ): Promise<FileInfo[]> {
    const files: FileInfo[] = [];
    
    let pattern = recursive ? `${dirPath}/**/*` : `${dirPath}/*`;
    if (filter) {
      pattern = recursive ? `${dirPath}/**/${filter}` : `${dirPath}/${filter}`;
    }

    const globOptions = {
      dot: includeHidden,
      onlyFiles: false
    };

    const paths = await glob(pattern, globOptions);
    
    for (const filePath of paths) {
      try {
        const fileInfo = await this.getFileInfo(filePath);
        files.push(fileInfo);
      } catch (error) {
        this.log(LogLevel.WARN, `Failed to get info for ${filePath}`, { error });
      }
    }

    return files;
  }

  private async getFileInfo(filePath: string, includeHash: boolean = false, hashAlgorithm: string = 'sha256'): Promise<FileInfo> {
    const stats = await fs.stat(filePath);
    const parsedPath = path.parse(filePath);
    
    const fileInfo: FileInfo = {
      path: filePath,
      name: parsedPath.base,
      size: stats.size,
      type: stats.isDirectory() ? 'directory' : 'file',
      extension: parsedPath.ext,
      mimeType: this.supportedMimeTypes.get(parsedPath.ext),
      created: stats.birthtime,
      modified: stats.mtime,
      accessed: stats.atime,
      permissions: stats.mode.toString(8)
    };

    if (includeHash && stats.isFile()) {
      const content = await fs.readFile(filePath);
      const hash = createHash(hashAlgorithm);
      hash.update(content);
      fileInfo.hash = hash.digest('hex');
    }

    return fileInfo;
  }

  private async searchFiles(criteria: any): Promise<SearchResult> {
    const { searchPath, pattern, content, extension, minSize, maxSize, modifiedAfter, modifiedBefore, limit } = criteria;
    
    let searchPattern = pattern || '**/*';
    if (extension) {
      searchPattern = `**/*.${extension.replace(/^\./, '')}`;
    }

    const fullPattern = path.join(searchPath, searchPattern);
    const paths = await glob(fullPattern, { onlyFiles: true });
    
    const matchedFiles: FileInfo[] = [];
    
    for (const filePath of paths) {
      if (matchedFiles.length >= limit) break;
      
      try {
        const fileInfo = await this.getFileInfo(filePath);
        
        // Apply filters
        if (minSize && fileInfo.size < minSize) continue;
        if (maxSize && fileInfo.size > maxSize) continue;
        if (modifiedAfter && fileInfo.modified < modifiedAfter) continue;
        if (modifiedBefore && fileInfo.modified > modifiedBefore) continue;
        
        // Content search
        if (content && fileInfo.type === 'file') {
          try {
            const fileContent = await fs.readFile(filePath, 'utf8');
            if (!fileContent.includes(content)) continue;
          } catch (error) {
            // Skip binary files or files that can't be read as text
            continue;
          }
        }
        
        matchedFiles.push(fileInfo);
      } catch (error) {
        this.log(LogLevel.WARN, `Failed to process ${filePath}`, { error });
      }
    }

    return {
      files: matchedFiles,
      totalFound: matchedFiles.length,
      searchTime: 0, // Will be set by caller
      query: JSON.stringify(criteria)
    };
  }

  // Additional handler methods would be implemented here...
  private async handleDeleteFile(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Delete file not fully implemented' }] };
  }

  private async handleCopyFile(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Copy file not fully implemented' }] };
  }

  private async handleMoveFile(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Move file not fully implemented' }] };
  }

  private async handleCreateDirectory(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Create directory not fully implemented' }] };
  }

  private async handleGetFileInfo(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Get file info not fully implemented' }] };
  }

  private async handleBatchOperation(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Batch operation not fully implemented' }] };
  }

  private async handleFindDuplicates(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Find duplicates not fully implemented' }] };
  }

  private async handleCompressFiles(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Compress files not fully implemented' }] };
  }

  private async handleFileResource(uri: string, context: ExecutionContext): Promise<any> {
    return { contents: [{ uri, mimeType: 'text/plain', text: 'File resource not fully implemented' }] };
  }

  private async handleDirectoryResource(uri: string, context: ExecutionContext): Promise<any> {
    return { contents: [{ uri, mimeType: 'application/json', text: '{}' }] };
  }

  private async handleFileOrganizationPrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    const { directory, criteria = 'type' } = args;
    
    return {
      description: `File organization recommendations for ${directory}`,
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: `Analyze the files in ${directory} and provide organization recommendations based on ${criteria}.`
        }
      }]
    };
  }
}
