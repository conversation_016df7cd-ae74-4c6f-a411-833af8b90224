/**
 * Data Validation MCP Server
 * 
 * This server provides comprehensive data validation and quality assessment including:
 * - Schema validation (JSON, XML, CSV)
 * - Data quality metrics
 * - Data profiling and analysis
 * - Format conversion and normalization
 * - Data integrity checks
 * - Custom validation rules
 */

import { z } from 'zod';
import * as fs from 'fs/promises';
import { parse as parseCSV } from 'csv-parse/sync';
import { BaseMCPServer } from '../core/base-server.js';
import {
  MCPServerConfig,
  MCPTool,
  ToolResult,
  ExecutionContext,
  LogLevel
} from '../types/index.js';

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  metrics: DataMetrics;
  summary: ValidationSummary;
}

interface ValidationError {
  field: string;
  value: any;
  rule: string;
  message: string;
  severity: 'error' | 'warning';
  line?: number;
  column?: number;
}

interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

interface DataMetrics {
  totalRecords: number;
  validRecords: number;
  invalidRecords: number;
  completeness: number;
  uniqueness: number;
  consistency: number;
  accuracy: number;
}

interface ValidationSummary {
  overallScore: number;
  passRate: number;
  criticalIssues: number;
  recommendations: string[];
}

export class DataValidationServer extends BaseMCPServer {
  private validationSchemas: Map<string, any> = new Map();
  private customRules: Map<string, Function> = new Map();

  constructor() {
    const config: MCPServerConfig = {
      name: 'data-validation',
      version: '1.0.0',
      description: 'Comprehensive data validation, quality assessment, and profiling server',
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      }
    };

    super(config);
    this.initializeBuiltInRules();
  }

  protected async initializeServer(): Promise<void> {
    this.log(LogLevel.INFO, 'Initializing Data Validation Server');

    // Register tools
    this.registerTool(this.createValidateJSONTool());
    this.registerTool(this.createValidateCSVTool());
    this.registerTool(this.createValidateXMLTool());
    this.registerTool(this.createProfileDataTool());
    this.registerTool(this.createCreateSchemaTool());
    this.registerTool(this.createCustomValidationTool());
    this.registerTool(this.createDataQualityTool());
    this.registerTool(this.createFormatConversionTool());
    this.registerTool(this.createDataCleaningTool());
    this.registerTool(this.createBatchValidationTool());

    // Register resources
    this.registerResource({
      uri: 'data://schemas/{schemaId}',
      name: 'Validation Schemas',
      description: 'Access to validation schema definitions',
      mimeType: 'application/json',
      handler: this.handleSchemaResource.bind(this)
    });

    // Register prompts
    this.registerPrompt({
      name: 'data-quality-report',
      description: 'Generate comprehensive data quality assessment',
      arguments: [
        { name: 'dataSource', description: 'Data source to analyze', required: true },
        { name: 'focus', description: 'Focus area (completeness, accuracy, consistency)', required: false }
      ],
      handler: this.handleDataQualityReportPrompt.bind(this)
    });

    this.log(LogLevel.INFO, 'Data Validation Server initialized successfully');
  }

  private createValidateJSONTool(): MCPTool {
    return {
      name: 'validate-json',
      description: 'Validate JSON data against schema or custom rules',
      category: 'validation',
      tags: ['json', 'validation', 'schema'],
      inputSchema: z.object({
        data: z.union([z.string(), z.object({})]).describe('JSON data to validate'),
        schema: z.object({}).optional().describe('JSON schema for validation'),
        schemaId: z.string().optional().describe('ID of stored schema'),
        strictMode: z.boolean().default(false).describe('Enable strict validation mode'),
        customRules: z.array(z.string()).optional().describe('Custom validation rules to apply')
      }),
      handler: this.handleValidateJSON.bind(this)
    };
  }

  private createValidateCSVTool(): MCPTool {
    return {
      name: 'validate-csv',
      description: 'Validate CSV data structure and content',
      category: 'validation',
      tags: ['csv', 'validation', 'tabular'],
      inputSchema: z.object({
        filePath: z.string().optional().describe('Path to CSV file'),
        csvData: z.string().optional().describe('CSV data as string'),
        headers: z.array(z.string()).optional().describe('Expected column headers'),
        delimiter: z.string().default(',').describe('CSV delimiter'),
        validateTypes: z.boolean().default(true).describe('Validate data types'),
        allowEmptyValues: z.boolean().default(true).describe('Allow empty values'),
        maxRows: z.number().optional().describe('Maximum rows to validate')
      }),
      handler: this.handleValidateCSV.bind(this)
    };
  }

  private createValidateXMLTool(): MCPTool {
    return {
      name: 'validate-xml',
      description: 'Validate XML structure and content against XSD or DTD',
      category: 'validation',
      tags: ['xml', 'validation', 'xsd'],
      inputSchema: z.object({
        xmlData: z.string().describe('XML data to validate'),
        xsdSchema: z.string().optional().describe('XSD schema for validation'),
        dtdSchema: z.string().optional().describe('DTD schema for validation'),
        validateNamespaces: z.boolean().default(true).describe('Validate XML namespaces')
      }),
      handler: this.handleValidateXML.bind(this)
    };
  }

  private createProfileDataTool(): MCPTool {
    return {
      name: 'profile-data',
      description: 'Generate comprehensive data profiling report',
      category: 'analysis',
      tags: ['profiling', 'analysis', 'statistics'],
      inputSchema: z.object({
        dataSource: z.string().describe('Data source (file path or data string)'),
        dataType: z.enum(['json', 'csv', 'xml']).describe('Data format type'),
        sampleSize: z.number().optional().describe('Sample size for large datasets'),
        includeDistribution: z.boolean().default(true).describe('Include value distribution analysis'),
        detectPatterns: z.boolean().default(true).describe('Detect data patterns and anomalies')
      }),
      handler: this.handleProfileData.bind(this)
    };
  }

  private createCreateSchemaTool(): MCPTool {
    return {
      name: 'create-schema',
      description: 'Generate validation schema from sample data',
      category: 'schema',
      tags: ['schema', 'generation', 'inference'],
      inputSchema: z.object({
        sampleData: z.union([z.string(), z.object({})]).describe('Sample data to analyze'),
        dataType: z.enum(['json', 'csv', 'xml']).describe('Data format type'),
        schemaName: z.string().describe('Name for the generated schema'),
        strictness: z.enum(['loose', 'moderate', 'strict']).default('moderate').describe('Schema strictness level'),
        includeOptional: z.boolean().default(true).describe('Mark infrequent fields as optional')
      }),
      handler: this.handleCreateSchema.bind(this)
    };
  }

  private createCustomValidationTool(): MCPTool {
    return {
      name: 'custom-validation',
      description: 'Apply custom validation rules to data',
      category: 'validation',
      tags: ['custom', 'rules', 'business-logic'],
      inputSchema: z.object({
        data: z.union([z.string(), z.object({})]).describe('Data to validate'),
        rules: z.array(z.object({
          name: z.string(),
          expression: z.string(),
          message: z.string(),
          severity: z.enum(['error', 'warning']).default('error')
        })).describe('Custom validation rules'),
        context: z.object({}).optional().describe('Additional context for validation')
      }),
      handler: this.handleCustomValidation.bind(this)
    };
  }

  private createDataQualityTool(): MCPTool {
    return {
      name: 'data-quality',
      description: 'Assess overall data quality metrics',
      category: 'quality',
      tags: ['quality', 'metrics', 'assessment'],
      inputSchema: z.object({
        dataSource: z.string().describe('Data source to assess'),
        dimensions: z.array(z.enum(['completeness', 'accuracy', 'consistency', 'uniqueness', 'validity', 'timeliness'])).optional().describe('Quality dimensions to assess'),
        benchmarks: z.object({}).optional().describe('Quality benchmarks to compare against')
      }),
      handler: this.handleDataQuality.bind(this)
    };
  }

  private createFormatConversionTool(): MCPTool {
    return {
      name: 'format-conversion',
      description: 'Convert data between different formats with validation',
      category: 'conversion',
      tags: ['conversion', 'format', 'transformation'],
      inputSchema: z.object({
        inputData: z.string().describe('Input data to convert'),
        inputFormat: z.enum(['json', 'csv', 'xml', 'yaml']).describe('Input data format'),
        outputFormat: z.enum(['json', 'csv', 'xml', 'yaml']).describe('Output data format'),
        validateAfterConversion: z.boolean().default(true).describe('Validate data after conversion'),
        preserveStructure: z.boolean().default(true).describe('Preserve original data structure')
      }),
      handler: this.handleFormatConversion.bind(this)
    };
  }

  private createDataCleaningTool(): MCPTool {
    return {
      name: 'data-cleaning',
      description: 'Clean and normalize data based on validation results',
      category: 'cleaning',
      tags: ['cleaning', 'normalization', 'repair'],
      inputSchema: z.object({
        data: z.union([z.string(), z.object({})]).describe('Data to clean'),
        cleaningRules: z.array(z.string()).optional().describe('Specific cleaning rules to apply'),
        autoFix: z.boolean().default(false).describe('Automatically fix common issues'),
        preserveOriginal: z.boolean().default(true).describe('Keep backup of original data')
      }),
      handler: this.handleDataCleaning.bind(this)
    };
  }

  private createBatchValidationTool(): MCPTool {
    return {
      name: 'batch-validation',
      description: 'Validate multiple data sources in batch',
      category: 'validation',
      tags: ['batch', 'multiple', 'bulk'],
      inputSchema: z.object({
        dataSources: z.array(z.object({
          id: z.string(),
          path: z.string(),
          type: z.enum(['json', 'csv', 'xml']),
          schema: z.string().optional()
        })).describe('List of data sources to validate'),
        parallelProcessing: z.boolean().default(true).describe('Enable parallel processing'),
        stopOnFirstError: z.boolean().default(false).describe('Stop processing on first error'),
        generateReport: z.boolean().default(true).describe('Generate summary report')
      }),
      handler: this.handleBatchValidation.bind(this)
    };
  }

  private async handleValidateJSON(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { data, schema, schemaId, strictMode = false, customRules = [] } = params;
      
      this.log(LogLevel.INFO, 'Validating JSON data', { context });

      let jsonData: any;
      if (typeof data === 'string') {
        try {
          jsonData = JSON.parse(data);
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                isValid: false,
                errors: [{
                  field: 'root',
                  value: data,
                  rule: 'json-syntax',
                  message: 'Invalid JSON syntax',
                  severity: 'error'
                }]
              }, null, 2)
            }],
            isError: true
          };
        }
      } else {
        jsonData = data;
      }

      const validationResult = await this.validateJSON(jsonData, schema, schemaId, strictMode, customRules);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(validationResult, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'JSON validation failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error validating JSON: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleValidateCSV(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { filePath, csvData, headers, delimiter = ',', validateTypes = true, allowEmptyValues = true, maxRows } = params;
      
      this.log(LogLevel.INFO, 'Validating CSV data', { context });

      let data: string;
      if (filePath) {
        data = await fs.readFile(filePath, 'utf-8');
      } else if (csvData) {
        data = csvData;
      } else {
        throw new Error('Either filePath or csvData must be provided');
      }

      const validationResult = await this.validateCSV(data, headers, delimiter, validateTypes, allowEmptyValues, maxRows);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(validationResult, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'CSV validation failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error validating CSV: ${error.message}`
        }],
        isError: true
      };
    }
  }

  private async handleProfileData(params: any, context: ExecutionContext): Promise<ToolResult> {
    try {
      const { dataSource, dataType, sampleSize, includeDistribution = true, detectPatterns = true } = params;
      
      this.log(LogLevel.INFO, `Profiling ${dataType} data`, { context });

      const profile = await this.profileData(dataSource, dataType, sampleSize, includeDistribution, detectPatterns);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(profile, null, 2)
        }]
      };
    } catch (error) {
      this.log(LogLevel.ERROR, 'Data profiling failed', { error, context });
      return {
        content: [{
          type: 'text',
          text: `Error profiling data: ${error.message}`
        }],
        isError: true
      };
    }
  }

  // Helper methods for data validation
  private async validateJSON(data: any, schema?: any, schemaId?: string, strictMode: boolean = false, customRules: string[] = []): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    // Use stored schema if schemaId provided
    let validationSchema = schema;
    if (schemaId && this.validationSchemas.has(schemaId)) {
      validationSchema = this.validationSchemas.get(schemaId);
    }

    // Basic JSON structure validation
    if (validationSchema) {
      try {
        // Simple schema validation (in real implementation, use ajv or similar)
        this.validateAgainstSchema(data, validationSchema, errors, warnings);
      } catch (error) {
        errors.push({
          field: 'schema',
          value: data,
          rule: 'schema-validation',
          message: `Schema validation failed: ${error.message}`,
          severity: 'error'
        });
      }
    }

    // Apply custom rules
    for (const ruleName of customRules) {
      if (this.customRules.has(ruleName)) {
        const rule = this.customRules.get(ruleName)!;
        try {
          rule(data, errors, warnings);
        } catch (error) {
          errors.push({
            field: 'custom-rule',
            value: data,
            rule: ruleName,
            message: `Custom rule failed: ${error.message}`,
            severity: 'error'
          });
        }
      }
    }

    const metrics = this.calculateDataMetrics(data, errors);
    const summary = this.generateValidationSummary(errors, warnings, metrics);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      metrics,
      summary
    };
  }

  private async validateCSV(data: string, expectedHeaders?: string[], delimiter: string = ',', validateTypes: boolean = true, allowEmptyValues: boolean = true, maxRows?: number): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      const records = parseCSV(data, {
        columns: true,
        delimiter,
        skip_empty_lines: true
      });

      const actualHeaders = Object.keys(records[0] || {});
      
      // Validate headers
      if (expectedHeaders) {
        const missingHeaders = expectedHeaders.filter(h => !actualHeaders.includes(h));
        const extraHeaders = actualHeaders.filter(h => !expectedHeaders.includes(h));
        
        missingHeaders.forEach(header => {
          errors.push({
            field: 'headers',
            value: actualHeaders,
            rule: 'required-header',
            message: `Missing required header: ${header}`,
            severity: 'error'
          });
        });

        extraHeaders.forEach(header => {
          warnings.push({
            field: 'headers',
            message: `Unexpected header found: ${header}`,
            suggestion: 'Remove or add to expected headers'
          });
        });
      }

      // Validate data rows
      const rowsToValidate = maxRows ? records.slice(0, maxRows) : records;
      
      rowsToValidate.forEach((record, index) => {
        actualHeaders.forEach(header => {
          const value = record[header];
          
          // Check for empty values
          if (!allowEmptyValues && (value === '' || value === null || value === undefined)) {
            errors.push({
              field: header,
              value,
              rule: 'not-empty',
              message: `Empty value not allowed in column ${header}`,
              severity: 'error',
              line: index + 2 // +2 for header row and 0-based index
            });
          }

          // Type validation (basic)
          if (validateTypes && value !== '' && value !== null && value !== undefined) {
            this.validateCSVCellType(header, value, errors, index + 2);
          }
        });
      });

      const metrics = this.calculateCSVMetrics(records, errors);
      const summary = this.generateValidationSummary(errors, warnings, metrics);

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        metrics,
        summary
      };
    } catch (error) {
      errors.push({
        field: 'csv-structure',
        value: data,
        rule: 'csv-parse',
        message: `CSV parsing failed: ${error.message}`,
        severity: 'error'
      });

      return {
        isValid: false,
        errors,
        warnings,
        metrics: this.getEmptyMetrics(),
        summary: this.generateValidationSummary(errors, warnings, this.getEmptyMetrics())
      };
    }
  }

  private validateAgainstSchema(data: any, schema: any, errors: ValidationError[], warnings: ValidationWarning[]): void {
    // Simplified schema validation - in real implementation, use a proper JSON schema validator
    if (schema.type && typeof data !== schema.type) {
      errors.push({
        field: 'root',
        value: data,
        rule: 'type-check',
        message: `Expected type ${schema.type}, got ${typeof data}`,
        severity: 'error'
      });
    }

    if (schema.required && Array.isArray(schema.required)) {
      schema.required.forEach((field: string) => {
        if (!(field in data)) {
          errors.push({
            field,
            value: undefined,
            rule: 'required-field',
            message: `Required field '${field}' is missing`,
            severity: 'error'
          });
        }
      });
    }
  }

  private validateCSVCellType(header: string, value: any, errors: ValidationError[], line: number): void {
    // Basic type inference and validation
    const stringValue = String(value).trim();
    
    // Check if it looks like a number but isn't valid
    if (/^\d+\.?\d*$/.test(stringValue)) {
      const numValue = Number(stringValue);
      if (isNaN(numValue)) {
        errors.push({
          field: header,
          value,
          rule: 'number-format',
          message: `Invalid number format in column ${header}`,
          severity: 'error',
          line
        });
      }
    }

    // Check for potential date formats
    if (/^\d{4}-\d{2}-\d{2}/.test(stringValue)) {
      const date = new Date(stringValue);
      if (isNaN(date.getTime())) {
        errors.push({
          field: header,
          value,
          rule: 'date-format',
          message: `Invalid date format in column ${header}`,
          severity: 'error',
          line
        });
      }
    }
  }

  private calculateDataMetrics(data: any, errors: ValidationError[]): DataMetrics {
    // Simplified metrics calculation
    const totalRecords = Array.isArray(data) ? data.length : 1;
    const invalidRecords = errors.filter(e => e.severity === 'error').length;
    const validRecords = totalRecords - invalidRecords;

    return {
      totalRecords,
      validRecords,
      invalidRecords,
      completeness: totalRecords > 0 ? (validRecords / totalRecords) * 100 : 0,
      uniqueness: 100, // Simplified
      consistency: 100, // Simplified
      accuracy: totalRecords > 0 ? (validRecords / totalRecords) * 100 : 0
    };
  }

  private calculateCSVMetrics(records: any[], errors: ValidationError[]): DataMetrics {
    const totalRecords = records.length;
    const errorLines = new Set(errors.map(e => e.line).filter(l => l !== undefined));
    const invalidRecords = errorLines.size;
    const validRecords = totalRecords - invalidRecords;

    return {
      totalRecords,
      validRecords,
      invalidRecords,
      completeness: totalRecords > 0 ? (validRecords / totalRecords) * 100 : 0,
      uniqueness: 100, // Would need actual duplicate detection
      consistency: 100, // Would need consistency checks
      accuracy: totalRecords > 0 ? (validRecords / totalRecords) * 100 : 0
    };
  }

  private generateValidationSummary(errors: ValidationError[], warnings: ValidationWarning[], metrics: DataMetrics): ValidationSummary {
    const criticalIssues = errors.filter(e => e.severity === 'error').length;
    const passRate = metrics.totalRecords > 0 ? (metrics.validRecords / metrics.totalRecords) * 100 : 0;
    const overallScore = Math.min(100, Math.max(0, passRate - (criticalIssues * 5)));

    const recommendations: string[] = [];
    if (criticalIssues > 0) {
      recommendations.push('Fix critical validation errors');
    }
    if (warnings.length > 0) {
      recommendations.push('Review and address validation warnings');
    }
    if (metrics.completeness < 90) {
      recommendations.push('Improve data completeness');
    }

    return {
      overallScore,
      passRate,
      criticalIssues,
      recommendations
    };
  }

  private getEmptyMetrics(): DataMetrics {
    return {
      totalRecords: 0,
      validRecords: 0,
      invalidRecords: 0,
      completeness: 0,
      uniqueness: 0,
      consistency: 0,
      accuracy: 0
    };
  }

  private initializeBuiltInRules(): void {
    // Initialize some built-in validation rules
    this.customRules.set('email-format', (data: any, errors: ValidationError[], warnings: ValidationWarning[]) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (typeof data === 'string' && !emailRegex.test(data)) {
        errors.push({
          field: 'email',
          value: data,
          rule: 'email-format',
          message: 'Invalid email format',
          severity: 'error'
        });
      }
    });

    this.customRules.set('phone-format', (data: any, errors: ValidationError[], warnings: ValidationWarning[]) => {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      if (typeof data === 'string' && !phoneRegex.test(data)) {
        errors.push({
          field: 'phone',
          value: data,
          rule: 'phone-format',
          message: 'Invalid phone number format',
          severity: 'error'
        });
      }
    });
  }

  // Additional handler methods would be implemented here...
  private async handleValidateXML(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'XML validation not fully implemented' }] };
  }

  private async handleCreateSchema(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Schema creation not fully implemented' }] };
  }

  private async handleCustomValidation(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Custom validation not fully implemented' }] };
  }

  private async handleDataQuality(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Data quality assessment not fully implemented' }] };
  }

  private async handleFormatConversion(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Format conversion not fully implemented' }] };
  }

  private async handleDataCleaning(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Data cleaning not fully implemented' }] };
  }

  private async handleBatchValidation(params: any, context: ExecutionContext): Promise<ToolResult> {
    return { content: [{ type: 'text', text: 'Batch validation not fully implemented' }] };
  }

  private async profileData(dataSource: string, dataType: string, sampleSize?: number, includeDistribution: boolean = true, detectPatterns: boolean = true): Promise<any> {
    return {
      dataSource,
      dataType,
      profile: 'Data profiling not fully implemented'
    };
  }

  private async handleSchemaResource(uri: string, context: ExecutionContext): Promise<any> {
    return { contents: [{ uri, mimeType: 'application/json', text: '{}' }] };
  }

  private async handleDataQualityReportPrompt(args: Record<string, string>, context: ExecutionContext): Promise<any> {
    const { dataSource, focus = 'comprehensive' } = args;
    
    return {
      description: `Data quality report for ${dataSource}`,
      messages: [{
        role: 'user',
        content: {
          type: 'text',
          text: `Generate a comprehensive data quality assessment for ${dataSource}, focusing on ${focus} aspects.`
        }
      }]
    };
  }
}
