#!/usr/bin/env node

import { program } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';
import path from 'path';

// Banner display
console.log(
  chalk.cyan(`
 ██████╗██╗   ██╗██████╗ ███████╗██████╗ ███████╗███████╗ ██████╗
██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗██╔════╝██╔════╝██╔════╝
██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝███████╗█████╗  ██║
██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗╚════██║██╔══╝  ██║
╚██████╗   ██║   ██████╔╝███████╗██║  ██║███████║███████╗╚██████╗
 ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝╚══════╝╚══════╝ ╚═════╝

████████╗ ██████╗  ██████╗ ██╗     ██╗  ██╗██╗████████╗
╚══██╔══╝██╔═══██╗██╔═══██╗██║     ██║ ██╔╝██║╚══██╔══╝
   ██║   ██║   ██║██║   ██║██║     █████╔╝ ██║   ██║
   ██║   ██║   ██║██║   ██║██║     ██╔═██╗ ██║   ██║
   ██║   ╚██████╔╝╚██████╔╝███████╗██║  ██╗██║   ██║
   ╚═╝    ╚═════╝  ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝   ╚═╝
`)
);

console.log(
  chalk.yellow("Educational Cybersecurity Toolkit - For authorized use only\n")
);

// Legal disclaimer
console.log(chalk.red.bold("LEGAL DISCLAIMER:"));
console.log(chalk.red("This toolkit is for EDUCATIONAL PURPOSES ONLY."));
console.log(
  chalk.red(
    "Only use these tools on systems you own or have explicit permission to test."
  )
);
console.log(
  chalk.red("Unauthorized use may be illegal and result in criminal charges.\n")
);

// Version
program.version("1.0.0");

// Import tool modules
import networkScanner from './tools/network-scanner';
import passwordAnalyzer from './tools/password-analyzer';
import systemInfo from './tools/system-info';
import wirelessAnalyzer from './tools/wireless-analyzer';
import encryptionTools from './tools/encryption-tools';
import packetAnalyzer from './tools/packet-analyzer';
import socialEngineering from './tools/social-engineering';
import aiTools from './tools/ai-tools';
import mobileAnalyzer from './tools/mobile-analyzer';
import webAppScanner from './tools/web-app-scanner';
import steganographyTools from './tools/steganography-tools';
import bluetoothToolkit from './tools/bluetooth-toolkit';
import iotScanner from './tools/iot-scanner';

// Import new tool modules
import exploitFramework from './tools/exploit-framework';
import reverseEngineering from './tools/reverse-engineering';
import digitalForensics from './tools/digital-forensics';
import advancedCryptography from './tools/advanced-cryptography';
import privilegeEscalation from './tools/privilege-escalation';

// Main menu function
async function mainMenu(): Promise<void> {
  const { category } = await inquirer.prompt([
    {
      type: 'list',
      name: 'category',
      message: 'Select a tool category:',
      choices: [
        'Network Scanning',
        'Password Analysis',
        'System Information',
        'Wireless Analysis',
        'Encryption/Decryption',
        'Packet Analysis',
        'Social Engineering Education',
        'AI-Powered Tools',
        'Mobile Device Analyzer',
        'Web Application Scanner',
        'Steganography Tools',
        'Bluetooth Security Toolkit',
        'IoT Security Scanner',
        'Exploit Framework',
        'Reverse Engineering',
        'Digital Forensics',
        'Advanced Cryptography',
        'Privilege Escalation',
        'Exit',
      ],
    },
  ]);

  switch (category) {
    case 'Network Scanning':
      await networkScanner.menu();
      break;
    case 'Password Analysis':
      await passwordAnalyzer.menu();
      break;
    case 'System Information':
      await systemInfo.menu();
      break;
    case 'Wireless Analysis':
      await wirelessAnalyzer.menu();
      break;
    case 'Encryption/Decryption':
      await encryptionTools.menu();
      break;
    case 'Packet Analysis':
      await packetAnalyzer.menu();
      break;
    case 'Social Engineering Education':
      await socialEngineering.menu();
      break;
    case 'AI-Powered Tools':
      await aiTools.menu();
      break;
    case 'Mobile Device Analyzer':
      await mobileAnalyzer.menu();
      break;
    case 'Web Application Scanner':
      await webAppScanner.menu();
      break;
    case 'Steganography Tools':
      await steganographyTools.menu();
      break;
    case 'Bluetooth Security Toolkit':
      await bluetoothToolkit.menu();
      break;
    case 'IoT Security Scanner':
      await iotScanner.menu();
      break;
    case 'Exploit Framework':
      await exploitFramework.menu();
      break;
    case 'Reverse Engineering':
      await reverseEngineering.menu();
      break;
    case 'Digital Forensics':
      await digitalForensics.menu();
      break;
    case 'Advanced Cryptography':
      await advancedCryptography.menu();
      break;
    case 'Privilege Escalation':
      await privilegeEscalation.menu();
      break;
    case 'Exit':
      console.log(chalk.green("Thank you for using CyberSec Toolkit!"));
      process.exit(0);
  }

  // Return to main menu after tool execution
  await mainMenu();
}

// Check if tools directory exists, if not create it
const toolsDir = path.join(__dirname, "tools");
if (!fs.existsSync(toolsDir)) {
  fs.mkdirSync(toolsDir);
}

// Start the application
mainMenu().catch((err) => {
  console.error(chalk.red("An error occurred:"), err);
  process.exit(1);
});
