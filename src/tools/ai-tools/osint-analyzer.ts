/**
 * AI-Powered OSINT (Open Source Intelligence) Tool
 * 
 * This module simulates how AI could be used to gather and analyze
 * open-source intelligence.
 * For educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';

// OSINT source types
interface OsintSource {
  name: string;
  description: string;
  dataTypes: string[];
  reliability: number; // 0-1 scale
  accessLevel: 'Public' | 'Semi-Public' | 'Restricted';
}

// Sample OSINT sources for educational demonstration
const osintSources: OsintSource[] = [
  {
    name: 'Social Media Profiles',
    description: 'Public information from social media platforms',
    dataTypes: ['Personal Details', 'Photos', 'Connections', 'Interests', 'Location History'],
    reliability: 0.7,
    accessLevel: 'Public'
  },
  {
    name: 'Corporate Websites',
    description: 'Public information from company websites and job postings',
    dataTypes: ['Employee Names', 'Job Titles', 'Contact Information', 'Technologies Used'],
    reliability: 0.9,
    accessLevel: 'Public'
  },
  {
    name: 'Public Records',
    description: 'Government records, court documents, property records',
    dataTypes: ['Legal History', 'Property Ownership', 'Business Registrations', 'Licenses'],
    reliability: 0.95,
    accessLevel: 'Public'
  },
  {
    name: 'Data Breaches',
    description: 'Information from previously leaked data',
    dataTypes: ['Email Addresses', 'Usernames', 'Password Hashes', 'Personal Information'],
    reliability: 0.6,
    accessLevel: 'Semi-Public'
  },
  {
    name: 'Technical Footprints',
    description: 'DNS records, IP addresses, domain registrations',
    dataTypes: ['Domain Names', 'IP Addresses', 'Server Information', 'Technology Stack'],
    reliability: 0.85,
    accessLevel: 'Public'
  },
  {
    name: 'Academic & Research Publications',
    description: 'Published papers, research, and academic profiles',
    dataTypes: ['Research Interests', 'Professional Networks', 'Education History'],
    reliability: 0.9,
    accessLevel: 'Public'
  },
  {
    name: 'Forums & Discussion Boards',
    description: 'Public posts and discussions on various platforms',
    dataTypes: ['Technical Knowledge', 'Opinions', 'Interests', 'Expertise Areas'],
    reliability: 0.5,
    accessLevel: 'Public'
  }
];

// OSINT data types that can be collected
interface OsintDataType {
  name: string;
  description: string;
  sensitivityLevel: 'Low' | 'Medium' | 'High';
  sources: string[];
}

// Sample OSINT data types for educational demonstration
const osintDataTypes: OsintDataType[] = [
  {
    name: 'Email Addresses',
    description: 'Personal or work email addresses',
    sensitivityLevel: 'Medium',
    sources: ['Social Media Profiles', 'Corporate Websites', 'Data Breaches', 'Forums & Discussion Boards']
  },
  {
    name: 'Phone Numbers',
    description: 'Personal or work phone numbers',
    sensitivityLevel: 'Medium',
    sources: ['Social Media Profiles', 'Corporate Websites', 'Public Records']
  },
  {
    name: 'Physical Addresses',
    description: 'Home or work addresses',
    sensitivityLevel: 'High',
    sources: ['Public Records', 'Social Media Profiles', 'Corporate Websites']
  },
  {
    name: 'Employment History',
    description: 'Current and past employment information',
    sensitivityLevel: 'Low',
    sources: ['Social Media Profiles', 'Corporate Websites', 'Academic & Research Publications']
  },
  {
    name: 'Technical Skills',
    description: 'Programming languages, tools, technologies',
    sensitivityLevel: 'Low',
    sources: ['Social Media Profiles', 'Corporate Websites', 'Forums & Discussion Boards', 'Academic & Research Publications']
  },
  {
    name: 'Network Infrastructure',
    description: 'Domain names, IP addresses, server configurations',
    sensitivityLevel: 'Medium',
    sources: ['Technical Footprints', 'Corporate Websites', 'Forums & Discussion Boards']
  },
  {
    name: 'Personal Connections',
    description: 'Professional and personal relationships',
    sensitivityLevel: 'Medium',
    sources: ['Social Media Profiles', 'Academic & Research Publications', 'Corporate Websites']
  },
  {
    name: 'Location History',
    description: 'Places visited or lived',
    sensitivityLevel: 'High',
    sources: ['Social Media Profiles', 'Public Records']
  }
];

// Simulate AI OSINT gathering and analysis
async function gatherIntelligence(target: string, targetType: string, dataTypes: string[]): Promise<any> {
  const spinner = ora('AI gathering and analyzing open-source intelligence...').start();
  
  // This is a simulation - we're not actually gathering real intelligence
  return new Promise(resolve => {
    // Simulate processing time
    setTimeout(() => {
      spinner.succeed('AI OSINT analysis complete');
      
      // Generate simulated results
      const results = {
        target,
        targetType,
        dataCollected: [] as any[],
        sourcesCovered: [] as any[],
        analysisTime: `${(Math.random() * 5 + 2).toFixed(1)} minutes`,
        riskAssessment: '',
        recommendations: [] as string[]
      };
      
      // For each requested data type
      dataTypes.forEach(dataType => {
        const dataTypeInfo = osintDataTypes.find(dt => dt.name === dataType);
        
        if (dataTypeInfo) {
          // Determine if this data was "found" in the simulation
          const found = Math.random() > 0.3; // 70% chance of finding data
          
          if (found) {
            // Generate simulated data based on the data type
            let simulatedData: any;
            
            switch (dataType) {
              case 'Email Addresses':
                simulatedData = targetType === 'Person' ? 
                  [`${target.toLowerCase().replace(/\s+/g, '.')}@example.com`] : 
                  [`info@${target.toLowerCase().replace(/\s+/g, '')}.com`, `support@${target.toLowerCase().replace(/\s+/g, '')}.com`];
                break;
              case 'Phone Numbers':
                simulatedData = ['+****************', '+****************'];
                break;
              case 'Physical Addresses':
                simulatedData = ['123 Main St, Anytown, USA'];
                break;
              case 'Employment History':
                simulatedData = targetType === 'Person' ? 
                  ['Senior Developer at TechCorp (2018-Present)', 'Junior Developer at StartupInc (2015-2018)'] : 
                  null;
                break;
              case 'Technical Skills':
                simulatedData = targetType === 'Person' ? 
                  ['JavaScript', 'Python', 'AWS', 'Docker', 'Kubernetes'] : 
                  ['AWS', 'React', 'Node.js', 'MongoDB', 'Docker'];
                break;
              case 'Network Infrastructure':
                simulatedData = targetType === 'Organization' ? 
                  {
                    domains: [`${target.toLowerCase().replace(/\s+/g, '')}.com`],
                    ipRanges: ['***********/24'],
                    technologies: ['Nginx', 'AWS EC2', 'Cloudflare']
                  } : null;
                break;
              case 'Personal Connections':
                simulatedData = targetType === 'Person' ? 
                  ['Connected to 5 C-level executives', 'Connected to 12 developers', 'Member of 3 professional groups'] : 
                  null;
                break;
              case 'Location History':
                simulatedData = targetType === 'Person' ? 
                  ['San Francisco, CA', 'New York, NY', 'Austin, TX'] : 
                  ['Headquarters: San Francisco, CA', 'Offices: New York, London, Singapore'];
                break;
              default:
                simulatedData = 'Simulated data';
            }
            
            // Add to collected data
            if (simulatedData) {
              // Get sources where this data was "found"
              const sources = dataTypeInfo.sources
                .filter(() => Math.random() > 0.3) // Randomly select some sources
                .map(sourceName => {
                  const source = osintSources.find(s => s.name === sourceName);
                  return {
                    name: sourceName,
                    reliability: source ? source.reliability : 0.5,
                    lastUpdated: `${Math.floor(Math.random() * 12) + 1} months ago`
                  };
                });
              
              // Only add if we have sources
              if (sources.length > 0) {
                results.dataCollected.push({
                  type: dataType,
                  data: simulatedData,
                  sensitivityLevel: dataTypeInfo.sensitivityLevel,
                  sources,
                  confidence: calculateConfidence(sources)
                });
                
                // Add unique sources to the sources covered list
                sources.forEach(source => {
                  if (!results.sourcesCovered.some(s => s.name === source.name)) {
                    results.sourcesCovered.push(source);
                  }
                });
              }
            }
          }
        }
      });
      
      // Generate risk assessment based on collected data
      const sensitiveDataCount = results.dataCollected.filter(
        d => d.sensitivityLevel === 'High' || d.sensitivityLevel === 'Medium'
      ).length;
      
      if (sensitiveDataCount > 5) {
        results.riskAssessment = 'High';
        results.recommendations = [
          'Significant amount of sensitive information is publicly available',
          'Review and limit public information exposure',
          'Consider removing sensitive data from public sources',
          'Implement a regular OSINT monitoring program',
          'Train employees on information security awareness'
        ];
      } else if (sensitiveDataCount > 2) {
        results.riskAssessment = 'Medium';
        results.recommendations = [
          'Moderate amount of sensitive information is publicly available',
          'Review public profiles and limit unnecessary information',
          'Consider implementing a basic OSINT monitoring program',
          'Educate key personnel about information security'
        ];
      } else {
        results.riskAssessment = 'Low';
        results.recommendations = [
          'Limited sensitive information found in public sources',
          'Continue good practices in limiting public information',
          'Periodically review public profiles for information leakage'
        ];
      }
      
      resolve(results);
    }, 3000 + Math.random() * 2000); // Simulate processing time
  });
}

// Calculate confidence score based on source reliability
function calculateConfidence(sources: any[]): string {
  if (sources.length === 0) return '0%';
  
  const avgReliability = sources.reduce((sum, source) => sum + source.reliability, 0) / sources.length;
  const confidenceScore = avgReliability * 100;
  
  return `${confidenceScore.toFixed(1)}%`;
}

// Display the menu for AI OSINT tools
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== AI-Powered OSINT Analyzer ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.yellow('Learn how AI can gather and analyze open-source intelligence.'));
  console.log(chalk.red('No actual intelligence gathering is performed.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an option:',
      choices: [
        'Simulate OSINT gathering',
        'View OSINT sources and data types',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Simulate OSINT gathering':
      const { target, targetType, dataTypes } = await inquirer.prompt([
        {
          type: 'input',
          name: 'target',
          message: 'Enter target name (person or organization):',
          validate: (input) => input.trim() !== '' ? true : 'Please enter a target name'
        },
        {
          type: 'list',
          name: 'targetType',
          message: 'Select target type:',
          choices: ['Person', 'Organization']
        },
        {
          type: 'checkbox',
          name: 'dataTypes',
          message: 'Select data types to gather:',
          choices: osintDataTypes.map(dt => dt.name),
          validate: (input) => input.length > 0 ? true : 'Please select at least one data type'
        }
      ]);
      
      const { confirmSimulation } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmSimulation',
          message: 'This will simulate an AI-powered OSINT gathering operation. Continue?',
          default: true
        }
      ]);
      
      if (confirmSimulation) {
        try {
          const results = await gatherIntelligence(target, targetType, dataTypes);
          
          console.log(chalk.green('\nOSINT Analysis Results:'));
          console.log(`Target: ${results.target} (${results.targetType})`);
          console.log(`Analysis Time: ${results.analysisTime}`);
          console.log(`Sources Covered: ${results.sourcesCovered.length}`);
          console.log(`Data Points Collected: ${results.dataCollected.length}`);
          
          console.log(chalk.keyword(
            results.riskAssessment === 'Low' ? 'green' : 
            results.riskAssessment === 'Medium' ? 'yellow' : 'red'
          )(`\nRisk Assessment: ${results.riskAssessment}`));
          
          if (results.dataCollected.length > 0) {
            console.log(chalk.yellow('\nCollected Data:'));
            
            // Group by sensitivity level
            const bySensitivity = {
              'High': results.dataCollected.filter(d => d.sensitivityLevel === 'High'),
              'Medium': results.dataCollected.filter(d => d.sensitivityLevel === 'Medium'),
              'Low': results.dataCollected.filter(d => d.sensitivityLevel === 'Low')
            };
            
            ['High', 'Medium', 'Low'].forEach(level => {
              const data = bySensitivity[level];
              if (data.length > 0) {
                console.log(chalk.keyword(
                  level === 'Low' ? 'green' : 
                  level === 'Medium' ? 'yellow' : 'red'
                )(`\n${level} Sensitivity Data (${data.length}):`));
                
                data.forEach((item: any, index: number) => {
                  console.log(chalk.blue(`\n  ${index + 1}. ${item.type}`));
                  console.log(`     Confidence: ${item.confidence}`);
                  
                  // Format data based on type
                  if (Array.isArray(item.data)) {
                    item.data.forEach((d: any) => console.log(`     - ${d}`));
                  } else if (typeof item.data === 'object' && item.data !== null) {
                    Object.entries(item.data).forEach(([key, value]) => {
                      console.log(`     ${key}:`);
                      if (Array.isArray(value)) {
                        (value as any[]).forEach((v: any) => console.log(`       - ${v}`));
                      } else {
                        console.log(`       ${value}`);
                      }
                    });
                  } else {
                    console.log(`     ${item.data}`);
                  }
                  
                  console.log(chalk.yellow('\n     Sources:'));
                  item.sources.forEach((source: any) => {
                    console.log(`     - ${source.name} (Reliability: ${(source.reliability * 100).toFixed(0)}%, Last Updated: ${source.lastUpdated})`);
                  });
                });
              }
            });
          } else {
            console.log(chalk.green('\nNo data was found for the selected criteria.'));
          }
          
          console.log(chalk.blue('\nRecommendations:'));
          results.recommendations.forEach((recommendation: string) => {
            console.log(`- ${recommendation}`);
          });
          
          console.log(chalk.red('\nIMPORTANT REMINDER:'));
          console.log(chalk.red('This is only a simulation for educational purposes.'));
          console.log(chalk.red('No actual intelligence gathering was performed.'));
          
        } catch (error) {
          console.error(chalk.red('Error in simulation:'), error);
        }
      } else {
        console.log(chalk.yellow('Simulation cancelled.'));
      }
      break;
      
    case 'View OSINT sources and data types':
      console.log(chalk.yellow('\nCommon OSINT Sources:'));
      osintSources.forEach((source, index) => {
        console.log(chalk.blue(`\n${index + 1}. ${source.name} (${source.accessLevel})`));
        console.log(`   ${source.description}`);
        console.log(`   Reliability: ${(source.reliability * 100).toFixed(0)}%`);
        console.log(`   Data Types: ${source.dataTypes.join(', ')}`);
      });
      
      console.log(chalk.yellow('\nCommon OSINT Data Types:'));
      osintDataTypes.forEach((dataType, index) => {
        console.log(chalk.keyword(
          dataType.sensitivityLevel === 'Low' ? 'green' : 
          dataType.sensitivityLevel === 'Medium' ? 'yellow' : 'red'
        )(`\n${index + 1}. ${dataType.name} (${dataType.sensitivityLevel} Sensitivity)`));
        console.log(`   ${dataType.description}`);
        console.log(`   Common Sources: ${dataType.sources.join(', ')}`);
      });
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another OSINT analysis action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

export default {
  menu,
  gatherIntelligence
};
