/**
 * AI-Powered Traffic Analysis Tool
 * 
 * This module simulates how AI could be used to analyze network traffic patterns
 * to detect unusual behavior that might indicate an attack.
 * For educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';

// Traffic pattern types for simulation
interface TrafficPattern {
  name: string;
  description: string;
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
  aiDetectionRate: number; // 0-1 probability of AI detecting this pattern
  indicators: string[];
}

// Sample traffic patterns for educational demonstration
const suspiciousPatterns: TrafficPattern[] = [
  {
    name: 'Port Scanning',
    description: 'Multiple connection attempts to different ports in rapid succession',
    riskLevel: 'Medium',
    aiDetectionRate: 0.95,
    indicators: [
      'Connections to sequential ports',
      'High volume of SYN packets',
      'Connections from single source to multiple destinations',
      'Abnormal connection timing patterns'
    ]
  },
  {
    name: 'DDoS Attack',
    description: 'Distributed denial of service attack pattern',
    riskLevel: 'Critical',
    aiDetectionRate: 0.92,
    indicators: [
      'Abnormal traffic volume',
      'Traffic from unusual geographic distribution',
      'Similar packet structures from different sources',
      'Synchronized request patterns'
    ]
  },
  {
    name: 'Data Exfiltration',
    description: 'Unusual outbound data transfers that may indicate data theft',
    riskLevel: 'High',
    aiDetectionRate: 0.85,
    indicators: [
      'Large outbound data transfers',
      'Unusual destination for sensitive data',
      'Encrypted traffic to non-HTTPS destinations',
      'Periodic, scheduled data transfers'
    ]
  },
  {
    name: 'Command & Control Communication',
    description: 'Traffic patterns consistent with malware communicating with C2 servers',
    riskLevel: 'Critical',
    aiDetectionRate: 0.88,
    indicators: [
      'Periodic beaconing to external servers',
      'Unusual DNS queries',
      'Encrypted traffic to unusual destinations',
      'Traffic with unusual timing patterns'
    ]
  },
  {
    name: 'Lateral Movement',
    description: 'Traffic indicating an attacker moving between systems on the network',
    riskLevel: 'High',
    aiDetectionRate: 0.82,
    indicators: [
      'Authentication from unusual sources',
      'Access to multiple systems in sequence',
      'Use of administrative protocols outside IT department',
      'Unusual working hours access'
    ]
  }
];

// Simulate AI traffic analysis
async function analyzeTraffic(duration: number, networkSize: string, includePatterns: string[]): Promise<any> {
  const spinner = ora('AI analyzing network traffic patterns...').start();
  
  // This is a simulation - we're not actually analyzing real traffic
  return new Promise(resolve => {
    // Simulate processing time based on duration and network size
    const processingTime = duration * (networkSize === 'Large' ? 1.5 : networkSize === 'Medium' ? 1 : 0.5);
    
    setTimeout(() => {
      spinner.succeed('AI traffic analysis complete');
      
      // Generate simulated results
      const detectedPatterns: any[] = [];
      
      // For each pattern the user selected to include in the simulation
      includePatterns.forEach(patternName => {
        const pattern = suspiciousPatterns.find(p => p.name === patternName);
        if (pattern) {
          // Simulate whether the AI detected this pattern based on its detection rate
          const detected = Math.random() < pattern.aiDetectionRate;
          
          if (detected) {
            // If detected, add to results with confidence level
            const confidence = (pattern.aiDetectionRate * 100 * (0.85 + Math.random() * 0.15)).toFixed(1);
            
            detectedPatterns.push({
              pattern: pattern.name,
              description: pattern.description,
              riskLevel: pattern.riskLevel,
              confidence: `${confidence}%`,
              detectedIndicators: pattern.indicators
                .filter(() => Math.random() > 0.3) // Randomly select some indicators
                .map(indicator => ({
                  indicator,
                  occurrences: Math.floor(Math.random() * 50) + 1,
                  firstSeen: `${Math.floor(Math.random() * duration)}m ago`,
                  sourcesCount: Math.floor(Math.random() * 10) + 1
                }))
            });
          }
        }
      });
      
      // Add some normal traffic patterns for realism
      const normalPatterns = [
        'Regular HTTP/HTTPS Web Traffic',
        'DNS Queries',
        'Email Communication',
        'Internal File Sharing',
        'Database Queries'
      ];
      
      const result = {
        analysisTime: `${processingTime.toFixed(1)} seconds`,
        totalTrafficVolume: `${Math.floor(Math.random() * 100) + 50} GB`,
        uniqueIPs: Math.floor(Math.random() * 1000) + 100,
        detectedPatterns,
        normalPatterns: normalPatterns
          .filter(() => Math.random() > 0.3)
          .map(name => ({
            name,
            volume: `${Math.floor(Math.random() * 40) + 10}%`
          }))
      };
      
      resolve(result);
    }, processingTime * 1000); // Simulate processing time
  });
}

// Display the menu for AI traffic analysis tools
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== AI-Powered Traffic Analysis ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.red('No actual traffic analysis is performed.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an AI traffic analysis option:',
      choices: [
        'Simulate AI traffic pattern detection',
        'Back to main menu'
      ]
    }
  ]);
  
  if (action === 'Back to main menu') {
    return;
  }
  
  // Get simulation parameters
  const { duration, networkSize, includePatterns } = await inquirer.prompt([
    {
      type: 'list',
      name: 'duration',
      message: 'Select traffic capture duration to analyze:',
      choices: [
        { name: '5 minutes', value: 5 },
        { name: '15 minutes', value: 15 },
        { name: '30 minutes', value: 30 },
        { name: '1 hour', value: 60 }
      ]
    },
    {
      type: 'list',
      name: 'networkSize',
      message: 'Select network size:',
      choices: ['Small', 'Medium', 'Large']
    },
    {
      type: 'checkbox',
      name: 'includePatterns',
      message: 'Select traffic patterns to include in the simulation:',
      choices: suspiciousPatterns.map(p => p.name),
      validate: (input) => input.length > 0 ? true : 'Please select at least one pattern'
    }
  ]);
  
  try {
    const result = await analyzeTraffic(duration, networkSize, includePatterns);
    
    console.log(chalk.green('\nAI Traffic Analysis Results:'));
    console.log(`Analysis Time: ${result.analysisTime}`);
    console.log(`Total Traffic Volume: ${result.totalTrafficVolume}`);
    console.log(`Unique IP Addresses: ${result.uniqueIPs}`);
    
    if (result.detectedPatterns.length > 0) {
      console.log(chalk.yellow('\nDetected Suspicious Patterns:'));
      result.detectedPatterns.forEach((pattern: any, index: number) => {
        console.log(chalk.red(`\n${index + 1}. ${pattern.pattern} (${pattern.riskLevel} Risk)`));
        console.log(`   Description: ${pattern.description}`);
        console.log(`   AI Confidence: ${pattern.confidence}`);
        
        console.log(chalk.yellow('\n   Detected Indicators:'));
        pattern.detectedIndicators.forEach((indicator: any) => {
          console.log(`   - ${indicator.indicator}`);
          console.log(`     Occurrences: ${indicator.occurrences}`);
          console.log(`     First Seen: ${indicator.firstSeen}`);
          console.log(`     Unique Sources: ${indicator.sourcesCount}`);
        });
      });
    } else {
      console.log(chalk.green('\nNo suspicious patterns detected in this simulation.'));
    }
    
    console.log(chalk.blue('\nNormal Traffic Patterns:'));
    result.normalPatterns.forEach((pattern: any) => {
      console.log(`- ${pattern.name}: ${pattern.volume}`);
    });
    
    console.log(chalk.red('\nIMPORTANT REMINDER:'));
    console.log(chalk.red('This is only a simulation for educational purposes.'));
    console.log(chalk.red('No actual traffic analysis was performed.'));
    
  } catch (error) {
    console.error(chalk.red('Error in simulation:'), error);
  }
  
  // Ask if the user wants to perform another analysis
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another AI traffic analysis?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

export default {
  menu,
  analyzeTraffic
};
