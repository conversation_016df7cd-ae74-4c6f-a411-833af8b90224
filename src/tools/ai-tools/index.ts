/**
 * AI Cybersecurity Tools Index
 * 
 * This module provides access to various AI-powered cybersecurity tools
 * for educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';

// Import AI tools
import passwordCracker from './password-cracker';
import anomalyDetector from './anomaly-detector';
import phishingAnalyzer from './phishing-analyzer';
import vulnerabilityScanner from './vulnerability-scanner';
import malwareAnalyzer from './malware-analyzer';
import trafficAnalyzer from './traffic-analyzer';
import socialEngineeringDetector from './social-engineering-detector';
import codeVulnerabilityScanner from './code-vulnerability-scanner';
import osintAnalyzer from './osint-analyzer';
import deepfakeDetector from './deepfake-detector';
import threatIntelligenceAnalyzer from './threat-intelligence-analyzer';

// Display the menu for AI cybersecurity tools
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== AI-Powered Cybersecurity Tools ===\n'));
  console.log(chalk.yellow('These tools demonstrate how AI can be used in cybersecurity.'));
  console.log(chalk.red('IMPORTANT: All tools are for educational purposes only.'));
  
  const { tool } = await inquirer.prompt([
    {
      type: 'list',
      name: 'tool',
      message: 'Select an AI cybersecurity tool:',
      choices: [
        'AI Password Cracking Simulation',
        'AI Network Anomaly Detection',
        'AI Phishing Email Generator & Detector',
        'AI Vulnerability Scanner',
        'AI Malware Behavior Analyzer',
        'AI Traffic Pattern Analyzer',
        'AI Social Engineering Detector',
        'AI Code Vulnerability Scanner',
        'AI OSINT Analyzer',
        'AI Deepfake Detector',
        'AI Threat Intelligence Analyzer',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (tool) {
    case 'AI Password Cracking Simulation':
      await passwordCracker.menu();
      break;
      
    case 'AI Network Anomaly Detection':
      await anomalyDetector.menu();
      break;
      
    case 'AI Phishing Email Generator & Detector':
      await phishingAnalyzer.menu();
      break;
      
    case 'AI Vulnerability Scanner':
      await vulnerabilityScanner.menu();
      break;
      
    case 'AI Malware Behavior Analyzer':
      await malwareAnalyzer.menu();
      break;
      
    case 'AI Traffic Pattern Analyzer':
      await trafficAnalyzer.menu();
      break;
      
    case 'AI Social Engineering Detector':
      await socialEngineeringDetector.menu();
      break;
      
    case 'AI Code Vulnerability Scanner':
      await codeVulnerabilityScanner.menu();
      break;
      
    case 'AI OSINT Analyzer':
      await osintAnalyzer.menu();
      break;
      
    case 'AI Deepfake Detector':
      await deepfakeDetector.menu();
      break;
      
    case 'AI Threat Intelligence Analyzer':
      await threatIntelligenceAnalyzer.menu();
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Return to AI tools menu after tool execution
  await menu();
}

export default {
  menu,
  tools: {
    passwordCracker,
    anomalyDetector,
    phishingAnalyzer,
    vulnerabilityScanner,
    malwareAnalyzer,
    trafficAnalyzer,
    socialEngineeringDetector,
    codeVulnerabilityScanner,
    osintAnalyzer,
    deepfakeDetector,
    threatIntelligenceAnalyzer
  }
};
