/**
 * AI-Based Social Engineering Detector
 * 
 * This module simulates how AI could be used to analyze messages and communications
 * to identify potential social engineering attempts.
 * For educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';

// Social engineering indicators
interface SocialEngineeringIndicator {
  name: string;
  description: string;
  weight: number; // 0-1 weight in detection algorithm
  examples: string[];
}

// Sample social engineering indicators for educational demonstration
const indicators: SocialEngineeringIndicator[] = [
  {
    name: 'Urgency',
    description: 'Creating a sense of urgency to force quick decisions',
    weight: 0.8,
    examples: [
      'This offer expires in 24 hours',
      'Immediate action required',
      'Respond now to avoid account suspension',
      'Limited time offer'
    ]
  },
  {
    name: 'Authority',
    description: 'Impersonating or claiming to be an authority figure',
    weight: 0.85,
    examples: [
      'This is the IT department',
      'Message from your CEO',
      'Your bank security team',
      'Government tax authority notification'
    ]
  },
  {
    name: 'Fear',
    description: 'Using fear to manipulate the recipient',
    weight: 0.9,
    examples: [
      'Your account has been compromised',
      'Suspicious activity detected',
      'Legal action will be taken',
      'Security breach detected'
    ]
  },
  {
    name: '<PERSON><PERSON>',
    description: 'Offering a reward that seems too good to be true',
    weight: 0.75,
    examples: [
      'You\'ve won a prize',
      'Free gift card',
      'Exclusive discount',
      'Unexpected inheritance'
    ]
  },
  {
    name: 'Unusual Requests',
    description: 'Requests that are unusual or violate normal procedures',
    weight: 0.95,
    examples: [
      'Send gift cards as payment',
      'Disable your antivirus temporarily',
      'Share your password for verification',
      'Install this remote access software'
    ]
  },
  {
    name: 'Personal Information',
    description: 'Requesting sensitive personal or financial information',
    weight: 0.9,
    examples: [
      'Confirm your social security number',
      'Update your payment details',
      'Verify your date of birth and address',
      'Send a photo of your ID'
    ]
  },
  {
    name: 'Suspicious Links/Attachments',
    description: 'Encouraging clicking on links or opening attachments',
    weight: 0.85,
    examples: [
      'Click here to verify your account',
      'Open the attached invoice',
      'Review the attached document',
      'Follow this link to claim your prize'
    ]
  },
  {
    name: 'Grammatical Errors',
    description: 'Poor grammar, spelling, or formatting',
    weight: 0.6,
    examples: [
      'Obvious spelling mistakes',
      'Inconsistent formatting',
      'Strange sentence structures',
      'Mixed languages or character sets'
    ]
  }
];

// Analyze message for social engineering attempts
function analyzeMessage(message: string): any {
  // Results object
  const results = {
    overallRisk: 0,
    riskLevel: '',
    detectedIndicators: [] as any[],
    explanation: '',
    recommendations: [] as string[]
  };
  
  // Check for each indicator
  indicators.forEach(indicator => {
    const matches = [];
    
    // Simple detection simulation - check if any examples or keywords are present
    // In a real system, this would use NLP and machine learning
    indicator.examples.forEach(example => {
      const simplifiedExample = example.toLowerCase();
      const simplifiedMessage = message.toLowerCase();
      
      if (simplifiedMessage.includes(simplifiedExample) || 
          simplifiedMessage.includes(simplifiedExample.split(' ')[0])) {
        matches.push(example);
      }
    });
    
    // If we found matches for this indicator
    if (matches.length > 0) {
      // Calculate confidence based on number of matches and indicator weight
      const confidence = Math.min(0.5 + (matches.length * 0.1) + (Math.random() * 0.2), 0.99);
      
      // Add to detected indicators
      results.detectedIndicators.push({
        indicator: indicator.name,
        description: indicator.description,
        confidence: `${(confidence * 100).toFixed(1)}%`,
        matches: matches
      });
      
      // Add to overall risk score
      results.overallRisk += confidence * indicator.weight;
    }
  });
  
  // Normalize overall risk to 0-100 scale
  results.overallRisk = Math.min(results.overallRisk * 25, 100);
  
  // Determine risk level
  if (results.overallRisk < 20) {
    results.riskLevel = 'Low';
    results.explanation = 'This message shows few signs of being a social engineering attempt.';
    results.recommendations = [
      'Normal caution is sufficient',
      'Follow standard security practices'
    ];
  } else if (results.overallRisk < 50) {
    results.riskLevel = 'Medium';
    results.explanation = 'This message shows some characteristics of social engineering techniques.';
    results.recommendations = [
      'Verify the sender through a different communication channel',
      'Do not click links or open attachments without verification',
      'Do not provide sensitive information in response'
    ];
  } else if (results.overallRisk < 75) {
    results.riskLevel = 'High';
    results.explanation = 'This message contains multiple social engineering techniques.';
    results.recommendations = [
      'Treat this message as suspicious',
      'Do not respond or engage',
      'Report to your security team',
      'Verify through official channels if necessary'
    ];
  } else {
    results.riskLevel = 'Critical';
    results.explanation = 'This message shows strong indicators of being a social engineering attack.';
    results.recommendations = [
      'Do not respond or engage in any way',
      'Report to security immediately',
      'If you've already responded, contact IT security',
      'Alert colleagues if this is a widespread campaign'
    ];
  }
  
  return results;
}

// Display the menu for AI social engineering detection
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== AI Social Engineering Detector ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.yellow('Learn to identify social engineering attempts in communications.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an option:',
      choices: [
        'Analyze message for social engineering',
        'View common social engineering techniques',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Analyze message for social engineering':
      const { message } = await inquirer.prompt([
        {
          type: 'editor',
          name: 'message',
          message: 'Enter the message to analyze (opens in your default editor):',
          default: 'Dear User, We have detected suspicious activity on your account. Click here immediately to verify your identity or your account will be suspended within 24 hours. You will need to provide your account password and social security number for verification purposes.'
        }
      ]);
      
      const spinner = ora('AI analyzing message for social engineering techniques...').start();
      
      // Simulate processing time
      setTimeout(() => {
        spinner.succeed('Analysis complete');
        
        const results = analyzeMessage(message);
        
        console.log(chalk.green('\nAI Analysis Results:'));
        console.log(`Risk Level: ${chalk.keyword(
          results.riskLevel === 'Low' ? 'green' : 
          results.riskLevel === 'Medium' ? 'yellow' : 
          results.riskLevel === 'High' ? 'orange' : 'red'
        )(`${results.riskLevel} (${results.overallRisk.toFixed(1)}%)`)}`);
        
        console.log(`\nExplanation: ${results.explanation}`);
        
        if (results.detectedIndicators.length > 0) {
          console.log(chalk.yellow('\nDetected Social Engineering Techniques:'));
          results.detectedIndicators.forEach((detected: any, index: number) => {
            console.log(chalk.red(`\n${index + 1}. ${detected.indicator}`));
            console.log(`   Description: ${detected.description}`);
            console.log(`   AI Confidence: ${detected.confidence}`);
            
            console.log(chalk.yellow('\n   Detected in message:'));
            detected.matches.forEach((match: string) => {
              console.log(`   - "${match}"`);
            });
          });
        } else {
          console.log(chalk.green('\nNo clear social engineering techniques detected.'));
        }
        
        console.log(chalk.blue('\nRecommendations:'));
        results.recommendations.forEach((recommendation: string) => {
          console.log(`- ${recommendation}`);
        });
        
        console.log(chalk.red('\nIMPORTANT REMINDER:'));
        console.log(chalk.red('This is only a simulation for educational purposes.'));
        console.log(chalk.red('Always use human judgment alongside automated tools.'));
        
      }, 2000 + Math.random() * 1000);
      break;
      
    case 'View common social engineering techniques':
      console.log(chalk.yellow('\nCommon Social Engineering Techniques:'));
      indicators.forEach((indicator, index) => {
        console.log(chalk.red(`\n${index + 1}. ${indicator.name}`));
        console.log(`   ${indicator.description}`);
        console.log(chalk.yellow('\n   Examples:'));
        indicator.examples.forEach(example => {
          console.log(`   - "${example}"`);
        });
      });
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another social engineering detection action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

export default {
  menu,
  analyzeMessage
};
