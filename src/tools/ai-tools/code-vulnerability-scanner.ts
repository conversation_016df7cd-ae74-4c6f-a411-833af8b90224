/**
 * AI Code Vulnerability Scanner
 * 
 * This module simulates how AI could be used to scan code for potential
 * security vulnerabilities.
 * For educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import fs from 'fs';
import path from 'path';

// Vulnerability types for simulation
interface VulnerabilityType {
  id: string;
  name: string;
  description: string;
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  cwe?: string; // Common Weakness Enumeration reference
  languages: string[];
  patterns: RegExp[];
  falsePositiveRate: number; // 0-1 probability of false positive
  remediation: string;
}

// Sample vulnerability types for educational demonstration
const vulnerabilityTypes: VulnerabilityType[] = [
  {
    id: 'SQL-INJ',
    name: 'SQL Injection',
    description: 'Unsanitized user input used in SQL queries',
    severity: 'Critical',
    cwe: 'CWE-89',
    languages: ['JavaScript', 'PHP', 'Python', 'Java', 'C#'],
    patterns: [
      /\bexecute\s*\(\s*["']SELECT.*\$.*["']/i,
      /\bquery\s*\(\s*["']SELECT.*\$.*["']/i,
      /\bexec\s*\(\s*["']SELECT.*\$.*["']/i,
      /\bmysqli_query\s*\(\s*["']SELECT.*\$.*["']/i,
      /\bcursor\.execute\s*\(\s*["']SELECT.*\%.*["']/i,
      /\bcursor\.execute\s*\(\s*["']SELECT.*\{.*\}.*["']/i,
      /\bcursor\.execute\s*\(\s*f["']SELECT.*\{.*\}.*["']/i
    ],
    falsePositiveRate: 0.2,
    remediation: 'Use parameterized queries or prepared statements instead of string concatenation'
  },
  {
    id: 'XSS',
    name: 'Cross-Site Scripting (XSS)',
    description: 'Unsanitized user input rendered in HTML',
    severity: 'High',
    cwe: 'CWE-79',
    languages: ['JavaScript', 'PHP', 'Python', 'Ruby', 'Java'],
    patterns: [
      /\binnerHTML\s*=\s*.*\$/,
      /\bdocument\.write\s*\(\s*.*\$/,
      /\beval\s*\(\s*.*\$/,
      /\bsetHTML\s*\(\s*.*\$/,
      /\brender\s*\(\s*.*\$.*\)/,
      /\becho\s+\$_GET/,
      /\becho\s+\$_POST/,
      /\bprint\s+\$_GET/,
      /\bprint\s+\$_POST/
    ],
    falsePositiveRate: 0.3,
    remediation: 'Use context-appropriate output encoding and content security policies'
  },
  {
    id: 'CSRF',
    name: 'Cross-Site Request Forgery',
    description: 'Missing CSRF tokens in state-changing operations',
    severity: 'Medium',
    cwe: 'CWE-352',
    languages: ['JavaScript', 'PHP', 'Python', 'Ruby', 'Java'],
    patterns: [
      /\bform\s+action\s*=\s*["'][^"']*["']\s*method\s*=\s*["']post["']/i,
      /\$_POST\s*\[\s*["'][^"']*["']\s*\]/,
      /\brequest\.POST\s*\[\s*["'][^"']*["']\s*\]/,
      /\bparams\s*\[\s*:.*\s*\]/
    ],
    falsePositiveRate: 0.5,
    remediation: 'Implement anti-CSRF tokens and validate them on all state-changing requests'
  },
  {
    id: 'PATH-TRAV',
    name: 'Path Traversal',
    description: 'Unsanitized user input used in file paths',
    severity: 'High',
    cwe: 'CWE-22',
    languages: ['JavaScript', 'PHP', 'Python', 'Java', 'C#', 'C++'],
    patterns: [
      /\bfs\.readFile\s*\(\s*.*\$/,
      /\bFile\.open\s*\(\s*.*\$/,
      /\bfopen\s*\(\s*.*\$/,
      /\bopen\s*\(\s*.*\$/,
      /\bnew FileInputStream\s*\(\s*.*\$/,
      /\bPath\.get\s*\(\s*.*\$/
    ],
    falsePositiveRate: 0.25,
    remediation: 'Validate and sanitize file paths, use path canonicalization, and restrict to specific directories'
  },
  {
    id: 'HARD-CRED',
    name: 'Hardcoded Credentials',
    description: 'Credentials or secrets embedded in code',
    severity: 'Critical',
    cwe: 'CWE-798',
    languages: ['JavaScript', 'PHP', 'Python', 'Java', 'C#', 'Ruby', 'Go'],
    patterns: [
      /\bpassword\s*=\s*["'][^"']{8,}["']/i,
      /\bpasswd\s*=\s*["'][^"']{8,}["']/i,
      /\bsecret\s*=\s*["'][^"']{8,}["']/i,
      /\bapikey\s*=\s*["'][^"']{8,}["']/i,
      /\bapi_key\s*=\s*["'][^"']{8,}["']/i,
      /\btoken\s*=\s*["'][^"']{8,}["']/i,
      /\baccess_key\s*=\s*["'][^"']{8,}["']/i
    ],
    falsePositiveRate: 0.4,
    remediation: 'Store credentials in environment variables or a secure credential management system'
  },
  {
    id: 'INSEC-DESER',
    name: 'Insecure Deserialization',
    description: 'Deserializing untrusted data',
    severity: 'High',
    cwe: 'CWE-502',
    languages: ['JavaScript', 'PHP', 'Python', 'Java', 'C#'],
    patterns: [
      /\bJSON\.parse\s*\(\s*.*\$/,
      /\bunserialize\s*\(\s*.*\$/,
      /\bpickle\.loads\s*\(\s*.*\$/,
      /\bObjectInputStream\s*\(\s*.*\$/,
      /\beval\s*\(\s*.*\$/,
      /\bYAML\.load\s*\(\s*.*\$/
    ],
    falsePositiveRate: 0.35,
    remediation: 'Validate and sanitize data before deserialization, use safer alternatives, or implement integrity checks'
  }
];

// Simulate AI code scanning
async function scanCode(code: string, language: string): Promise<any> {
  const spinner = ora('AI analyzing code for vulnerabilities...').start();
  
  // This is a simulation - we're not actually performing a comprehensive scan
  return new Promise(resolve => {
    // Simulate processing time
    setTimeout(() => {
      spinner.succeed('AI code vulnerability scan complete');
      
      // Generate simulated results
      const results = {
        scannedLines: code.split('\n').length,
        language,
        vulnerabilities: [] as any[],
        overallRisk: 'Low',
        scanTime: `${(Math.random() * 2 + 0.5).toFixed(1)} seconds`
      };
      
      // Get vulnerability types applicable to this language
      const applicableVulnerabilities = vulnerabilityTypes.filter(v => 
        v.languages.includes(language)
      );
      
      // Scan for each vulnerability type
      applicableVulnerabilities.forEach(vulnType => {
        // Check each pattern
        vulnType.patterns.forEach(pattern => {
          // Split code into lines for line number tracking
          const lines = code.split('\n');
          
          lines.forEach((line, lineIndex) => {
            if (pattern.test(line)) {
              // Simulate false positive check
              const isFalsePositive = Math.random() < vulnType.falsePositiveRate;
              
              if (!isFalsePositive) {
                // Calculate confidence level (higher for more severe issues)
                const severityFactor = 
                  vulnType.severity === 'Critical' ? 0.9 :
                  vulnType.severity === 'High' ? 0.8 :
                  vulnType.severity === 'Medium' ? 0.7 : 0.6;
                
                const confidence = (severityFactor + Math.random() * 0.2).toFixed(2);
                
                // Add vulnerability to results
                results.vulnerabilities.push({
                  type: vulnType.name,
                  id: vulnType.id,
                  severity: vulnType.severity,
                  confidence: `${(parseFloat(confidence) * 100).toFixed(1)}%`,
                  lineNumber: lineIndex + 1,
                  code: line.trim(),
                  description: vulnType.description,
                  cwe: vulnType.cwe,
                  remediation: vulnType.remediation
                });
              }
            }
          });
        });
      });
      
      // Determine overall risk based on found vulnerabilities
      if (results.vulnerabilities.some(v => v.severity === 'Critical')) {
        results.overallRisk = 'Critical';
      } else if (results.vulnerabilities.some(v => v.severity === 'High')) {
        results.overallRisk = 'High';
      } else if (results.vulnerabilities.some(v => v.severity === 'Medium')) {
        results.overallRisk = 'Medium';
      }
      
      resolve(results);
    }, 2000 + Math.random() * 1000); // Simulate processing time
  });
}

// Display the menu for AI code vulnerability scanning
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== AI Code Vulnerability Scanner ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.yellow('Learn how AI can identify potential security vulnerabilities in code.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select an option:',
      choices: [
        'Scan code snippet',
        'View vulnerability types',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Scan code snippet':
      const { language, codeInput } = await inquirer.prompt([
        {
          type: 'list',
          name: 'language',
          message: 'Select the programming language:',
          choices: ['JavaScript', 'Python', 'PHP', 'Java', 'C#', 'Ruby']
        },
        {
          type: 'list',
          name: 'codeInput',
          message: 'How would you like to input code?',
          choices: ['Enter in editor', 'Sample vulnerable code']
        }
      ]);
      
      let code = '';
      
      if (codeInput === 'Enter in editor') {
        const response = await inquirer.prompt([
          {
            type: 'editor',
            name: 'code',
            message: 'Enter the code to scan (opens in your default editor):',
            default: language === 'JavaScript' ? 
              `// Example JavaScript code\nfunction getUserData(userId) {\n  const query = "SELECT * FROM users WHERE id = " + userId;\n  return db.execute(query);\n}\n\napp.get('/profile', (req, res) => {\n  const userData = getUserData(req.query.id);\n  res.send('<div>' + userData.name + '</div>');\n});` :
              language === 'Python' ?
              `# Example Python code\ndef get_user_data(user_id):\n    query = "SELECT * FROM users WHERE id = " + user_id\n    cursor.execute(query)\n    return cursor.fetchone()\n\<EMAIL>('/profile')\ndef profile():\n    user_data = get_user_data(request.args.get('id'))\n    return f"<div>{user_data['name']}</div>"` :
              `// Example code\n// Enter your code here`
          }
        ]);
        code = response.code;
      } else {
        // Provide sample vulnerable code based on language
        switch (language) {
          case 'JavaScript':
            code = `// Example vulnerable JavaScript code
const express = require('express');
const mysql = require('mysql');
const app = express();

// Create database connection
const db = mysql.createConnection({
  host: 'localhost',
  user: 'admin',
  password: 'admin123secure',
  database: 'userdb'
});

// Get user profile
app.get('/profile', (req, res) => {
  const userId = req.query.id;
  
  // SQL Injection vulnerability
  const query = "SELECT * FROM users WHERE id = " + userId;
  db.query(query, (err, user) => {
    if (err) throw err;
    
    // XSS vulnerability
    res.send('<div>Welcome back, ' + user.name + '</div>');
  });
});

// Process file download
app.get('/download', (req, res) => {
  const fileName = req.query.file;
  
  // Path traversal vulnerability
  fs.readFile('/var/www/files/' + fileName, (err, data) => {
    if (err) throw err;
    res.send(data);
  });
});

// Process user data
app.post('/api/data', (req, res) => {
  // Insecure deserialization
  const userData = JSON.parse(req.body.data);
  
  // Process user data...
  res.json({success: true});
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});`;
            break;
          case 'Python':
            code = `# Example vulnerable Python code
from flask import Flask, request, render_template_string
import sqlite3
import pickle
import os

app = Flask(__name__)

# Database connection
def get_db():
    conn = sqlite3.connect('users.db')
    conn.row_factory = sqlite3.Row
    return conn

# SQL Injection vulnerability
@app.route('/user')
def user_profile():
    user_id = request.args.get('id')
    db = get_db()
    query = "SELECT * FROM users WHERE id = " + user_id
    user = db.execute(query).fetchone()
    
    # XSS vulnerability
    return render_template_string("<h1>Welcome, {{user['name']}}</h1>")

# Path traversal vulnerability
@app.route('/download')
def download_file():
    filename = request.args.get('file')
    file_path = os.path.join('/var/www/files/', filename)
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    return content

# Insecure deserialization
@app.route('/api/data', methods=['POST'])
def process_data():
    data = request.form.get('data')
    
    # Insecure deserialization
    user_data = pickle.loads(data)
    
    # Process user data...
    return {'success': True}

# Hardcoded credentials
API_KEY = "********************************************"

if __name__ == '__main__':
    app.run(debug=True)`;
            break;
          case 'PHP':
            code = `<?php
// Example vulnerable PHP code

// Database connection
$conn = mysqli_connect("localhost", "root", "password123", "users_db");

// SQL Injection vulnerability
$user_id = $_GET['id'];
$query = "SELECT * FROM users WHERE id = " . $user_id;
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

// XSS vulnerability
echo "<h1>Welcome, " . $user['name'] . "</h1>";

// Path traversal vulnerability
$file = $_GET['file'];
$content = file_get_contents("/var/www/files/" . $file);
echo $content;

// CSRF vulnerability (no CSRF token)
if ($_POST['action'] == 'update_profile') {
    $new_email = $_POST['email'];
    mysqli_query($conn, "UPDATE users SET email = '$new_email' WHERE id = " . $user_id);
    echo "Profile updated!";
}

// Insecure deserialization
$user_data = unserialize($_POST['data']);

// Hardcoded credentials
$api_key = "********************************************";
?>`;
            break;
          default:
            code = `// Example vulnerable code for ${language}\n// This is a simulation`;
        }
      }
      
      try {
        const results = await scanCode(code, language);
        
        console.log(chalk.green('\nAI Code Vulnerability Scan Results:'));
        console.log(`Language: ${results.language}`);
        console.log(`Lines Scanned: ${results.scannedLines}`);
        console.log(`Scan Time: ${results.scanTime}`);
        console.log(`Overall Risk: ${chalk.keyword(
          results.overallRisk === 'Low' ? 'green' : 
          results.overallRisk === 'Medium' ? 'yellow' : 
          results.overallRisk === 'High' ? 'orange' : 'red'
        )(results.overallRisk)}`);
        
        if (results.vulnerabilities.length > 0) {
          console.log(chalk.yellow(`\nDetected ${results.vulnerabilities.length} potential vulnerabilities:`));
          
          // Group by severity for better presentation
          const bySeverity = {
            'Critical': results.vulnerabilities.filter(v => v.severity === 'Critical'),
            'High': results.vulnerabilities.filter(v => v.severity === 'High'),
            'Medium': results.vulnerabilities.filter(v => v.severity === 'Medium'),
            'Low': results.vulnerabilities.filter(v => v.severity === 'Low')
          };
          
          ['Critical', 'High', 'Medium', 'Low'].forEach(severity => {
            const vulns = bySeverity[severity];
            if (vulns.length > 0) {
              console.log(chalk.keyword(
                severity === 'Low' ? 'green' : 
                severity === 'Medium' ? 'yellow' : 
                severity === 'High' ? 'orange' : 'red'
              )(`\n${severity} Severity (${vulns.length}):`));
              
              vulns.forEach((vuln: any, index: number) => {
                console.log(chalk.red(`\n  ${index + 1}. ${vuln.type} [${vuln.id}]`));
                console.log(`     Line ${vuln.lineNumber}: ${chalk.gray(vuln.code)}`);
                console.log(`     Description: ${vuln.description}`);
                console.log(`     Confidence: ${vuln.confidence}`);
                if (vuln.cwe) {
                  console.log(`     CWE: ${vuln.cwe}`);
                }
                console.log(chalk.green(`     Remediation: ${vuln.remediation}`));
              });
            }
          });
        } else {
          console.log(chalk.green('\nNo vulnerabilities detected in this code sample.'));
        }
        
        console.log(chalk.red('\nIMPORTANT REMINDER:'));
        console.log(chalk.red('This is only a simulation for educational purposes.'));
        console.log(chalk.red('A real security audit would be more comprehensive.'));
        
      } catch (error) {
        console.error(chalk.red('Error in simulation:'), error);
      }
      break;
      
    case 'View vulnerability types':
      console.log(chalk.yellow('\nCommon Code Vulnerability Types:'));
      vulnerabilityTypes.forEach((vuln, index) => {
        console.log(chalk.keyword(
          vuln.severity === 'Low' ? 'green' : 
          vuln.severity === 'Medium' ? 'yellow' : 
          vuln.severity === 'High' ? 'orange' : 'red'
        )(`\n${index + 1}. ${vuln.name} [${vuln.id}] - ${vuln.severity}`));
        console.log(`   ${vuln.description}`);
        if (vuln.cwe) {
          console.log(`   CWE: ${vuln.cwe}`);
        }
        console.log(`   Applicable Languages: ${vuln.languages.join(', ')}`);
        console.log(chalk.green(`   Remediation: ${vuln.remediation}`));
      });
      break;
      
    case 'Back to main menu':
      return;
  }
  
  // Ask if the user wants to perform another action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another code vulnerability scanning action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

export default {
  menu,
  scanCode
};
