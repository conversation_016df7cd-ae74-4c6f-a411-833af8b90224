/**
 * Reverse Engineering Tools
 * 
 * This module simulates tools for reverse engineering binaries and applications.
 * For educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import fs from 'fs';
import path from 'path';

// Binary analysis types
interface BinaryAnalysisResult {
  fileType: string;
  architecture: string;
  entryPoint?: string;
  sections?: any[];
  imports?: string[];
  strings?: string[];
  vulnerabilities?: any[];
  protections?: string[];
  notes: string;
}

// Sample binary analysis for educational demonstration
function analyzeBinary(filePath: string): Promise<BinaryAnalysisResult> {
  const spinner = ora('Analyzing binary file...').start();
  
  // This is a simulation - we're not actually analyzing real binaries
  return new Promise((resolve, reject) => {
    // Simulate processing time
    setTimeout(() => {
      // Check if file exists (for realism)
      if (!fs.existsSync(filePath)) {
        spinner.fail('File not found');
        reject(new Error('File not found'));
        return;
      }
      
      // Get file extension
      const ext = path.extname(filePath).toLowerCase();
      
      // Generate simulated results based on file extension
      let result: BinaryAnalysisResult;
      
      if (ext === '.exe' || ext === '.dll') {
        // Windows PE file simulation
        spinner.succeed('Binary analysis complete: Windows PE file');
        
        result = {
          fileType: 'Windows PE Executable',
          architecture: Math.random() > 0.5 ? 'x86_64' : 'x86',
          entryPoint: '0x140001000',
          sections: [
            { name: '.text', address: '0x140001000', size: '0x1000', permissions: 'rx' },
            { name: '.data', address: '0x140002000', size: '0x1000', permissions: 'rw' },
            { name: '.rdata', address: '0x140003000', size: '0x1000', permissions: 'r' }
          ],
          imports: [
            'kernel32.dll: GetProcAddress, LoadLibraryA, VirtualAlloc',
            'user32.dll: MessageBoxA, RegisterClassExA',
            'ws2_32.dll: socket, connect, send, recv'
          ],
          strings: [
            'GetProcAddress',
            'LoadLibraryA',
            'C:\\Windows\\System32',
            'Software\\Microsoft\\Windows\\CurrentVersion\\Run',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
          ],
          vulnerabilities: [
            { type: 'Insecure Function', details: 'Use of strcpy() without bounds checking', severity: 'High' },
            { type: 'Hardcoded Credentials', details: 'Possible API key or password in .data section', severity: 'Medium' }
          ],
          protections: [
            'No ASLR',
            'No DEP',
            'No Stack Canaries'
          ],
          notes: 'This is a simulated analysis for educational purposes only'
        };
      } else if (ext === '.elf' || ext === '') {
        // Linux ELF file simulation
        spinner.succeed('Binary analysis complete: Linux ELF file');
        
        result = {
          fileType: 'Linux ELF Executable',
          architecture: Math.random() > 0.5 ? 'x86_64' : 'ARM64',
          entryPoint: '0x400080',
          sections: [
            { name: '.text', address: '0x400080', size: '0x1000', permissions: 'rx' },
            { name: '.data', address: '0x600000', size: '0x1000', permissions: 'rw' },
            { name: '.bss', address: '0x601000', size: '0x1000', permissions: 'rw' }
          ],
          imports: [
            'libc.so.6: printf, scanf, malloc, system',
            'libpthread.so.0: pthread_create, pthread_join',
            'libssl.so.1.1: SSL_read, SSL_write, SSL_connect'
          ],
          strings: [
            '/etc/passwd',
            '/tmp/log.txt',
            'Connection established',
            'Usage: %s [options]',
            'https://api.example.com/v1/'
          ],
          vulnerabilities: [
            { type: 'Format String Vulnerability', details: 'Uncontrolled format string in printf()', severity: 'High' },
            { type: 'Command Injection', details: 'Use of system() with user input', severity: 'Critical' }
          ],
          protections: [
            'Partial RELRO',
            'No PIE',
            'Has Canary'
          ],
          notes: 'This is a simulated analysis for educational purposes only'
        };
      } else if (ext === '.apk') {
        // Android APK simulation
        spinner.succeed('Binary analysis complete: Android APK');
        
        result = {
          fileType: 'Android Package (APK)',
          architecture: 'Multi (ARM, ARM64, x86)',
          sections: [],
          imports: [
            'android.permission.INTERNET',
            'android.permission.READ_CONTACTS',
            'android.permission.ACCESS_FINE_LOCATION',
            'android.permission.CAMERA'
          ],
          strings: [
            'https://analytics.example.com/track',
            'SharedPreferences',
            'onCreate',
            'api_key=',
            'login_successful'
          ],
          vulnerabilities: [
            { type: 'Insecure Data Storage', details: 'Sensitive data stored in SharedPreferences', severity: 'Medium' },
            { type: 'Excessive Permissions', details: 'App requests permissions it may not need', severity: 'Medium' },
            { type: 'Insecure Communication', details: 'HTTP used instead of HTTPS for API calls', severity: 'High' }
          ],
          protections: [
            'No Root Detection',
            'No SSL Pinning',
            'No Code Obfuscation'
          ],
          notes: 'This is a simulated analysis for educational purposes only'
        };
      } else {
        // Generic binary simulation
        spinner.succeed('Binary analysis complete');
        
        result = {
          fileType: 'Unknown Binary Format',
          architecture: 'Unknown',
          strings: [
            'GetProcAddress',
            'malloc',
            'https://',
            'password',
            'admin'
          ],
          vulnerabilities: [
            { type: 'Unknown', details: 'Limited analysis available for this file type', severity: 'Unknown' }
          ],
          protections: [
            'Unknown'
          ],
          notes: 'This is a simulated analysis for educational purposes only'
        };
      }
      
      resolve(result);
    }, 3000 + Math.random() * 2000); // Simulate processing time
  });
}

// Decompile code simulation
function decompileCode(filePath: string, language: string): Promise<string> {
  const spinner = ora(`Decompiling to ${language}...`).start();
  
  // This is a simulation - we're not actually decompiling anything
  return new Promise((resolve, reject) => {
    // Simulate processing time
    setTimeout(() => {
      // Check if file exists (for realism)
      if (!fs.existsSync(filePath)) {
        spinner.fail('File not found');
        reject(new Error('File not found'));
        return;
      }
      
      spinner.succeed(`Decompilation to ${language} complete`);
      
      // Generate simulated decompiled code based on target language
      let decompiled = '';
      
      switch (language) {
        case 'C':
          decompiled = `
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>

// Decompiled from ${path.basename(filePath)}
// WARNING: This is simulated decompiled code for educational purposes only

int check_auth(char *username, char *password) {
    // Vulnerable strcmp implementation
    if (strcmp(username, "admin") == 0 && strcmp(password, "s3cr3t_p4ss") == 0) {
        return 1;
    }
    return 0;
}

int connect_to_server(char *server, int port) {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    struct sockaddr_in server_addr;
    
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    server_addr.sin_addr.s_addr = inet_addr(server);
    
    if (connect(sock, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        return -1;
    }
    
    return sock;
}

int main(int argc, char *argv[]) {
    char username[32];
    char password[32];
    char command[256];
    
    printf("Enter username: ");
    scanf("%s", username);  // Buffer overflow vulnerability
    
    printf("Enter password: ");
    scanf("%s", password);  // Buffer overflow vulnerability
    
    if (check_auth(username, password)) {
        printf("Authentication successful\\n");
        
        printf("Enter command: ");
        scanf("%s", command);
        
        system(command);  // Command injection vulnerability
    } else {
        printf("Authentication failed\\n");
    }
    
    return 0;
}`;
          break;
          
        case 'Java':
          decompiled = `
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

// Decompiled from ${path.basename(filePath)}
// WARNING: This is simulated decompiled code for educational purposes only

public class DecompiledApp {
    private static final String API_KEY = "1234567890abcdef";
    private static final String SERVER_URL = "https://api.example.com/data";
    
    public static void main(String[] args) {
        try {
            String username = args[0];
            String password = args[1];
            
            if (authenticate(username, password)) {
                System.out.println("Authentication successful");
                fetchData(username);
            } else {
                System.out.println("Authentication failed");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static boolean authenticate(String username, String password) {
        // Hardcoded credentials vulnerability
        return username.equals("admin") && password.equals("admin123");
    }
    
    private static void fetchData(String username) {
        try {
            URL url = new URL(SERVER_URL + "?user=" + username);  // Potential injection
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Authorization", "Basic " + 
                Base64.getEncoder().encodeToString((username + ":" + API_KEY).getBytes()));
            
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            
            System.out.println("Data: " + response.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}`;
          break;
          
        case 'Python':
          decompiled = `
import os
import sys
import requests
import base64
import sqlite3
from cryptography.fernet import Fernet

# Decompiled from ${path.basename(filePath)}
# WARNING: This is simulated decompiled code for educational purposes only

API_KEY = "1234567890abcdef"
SERVER_URL = "https://api.example.com/data"
DB_PATH = "/tmp/app_data.db"

def authenticate(username, password):
    # Insecure authentication
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # SQL Injection vulnerability
    query = f"SELECT * FROM users WHERE username = '{username}' AND password = '{password}'"
    cursor.execute(query)
    
    user = cursor.fetchone()
    conn.close()
    
    return user is not None

def encrypt_data(data):
    key = b'supersecretkeythatisnotverysecure123='  # Hardcoded encryption key
    f = Fernet(key)
    return f.encrypt(data.encode()).decode()

def fetch_data(username):
    try:
        # Potential injection vulnerability
        response = requests.get(
            f"{SERVER_URL}?user={username}",
            headers={"Authorization": f"Bearer {API_KEY}"}
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": "Failed to fetch data"}
    except Exception as e:
        return {"error": str(e)}

def execute_command(cmd):
    # Command injection vulnerability
    os.system(cmd)

def main():
    if len(sys.argv) < 3:
        print("Usage: python app.py <username> <password>")
        return
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    if authenticate(username, password):
        print("Authentication successful")
        
        data = fetch_data(username)
        print(f"Data: {data}")
        
        if len(sys.argv) > 3:
            execute_command(sys.argv[3])
    else:
        print("Authentication failed")

if __name__ == "__main__":
    main()`;
          break;
          
        default:
          decompiled = `
// Decompiled from ${path.basename(filePath)} to ${language}
// WARNING: This is simulated decompiled code for educational purposes only

// This is a placeholder for decompiled code
// In a real decompiler, this would contain actual decompiled source code
// The structure and quality would depend on the original binary and the decompiler used

// Simulated vulnerabilities that might be found in decompiled code:
// 1. Hardcoded credentials
// 2. Insecure cryptographic implementations
// 3. Command injection vulnerabilities
// 4. Buffer overflow vulnerabilities
// 5. Insecure network communications`;
      }
      
      resolve(decompiled);
    }, 4000 + Math.random() * 3000); // Simulate processing time
  });
}

// Display the menu for reverse engineering tools
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== Reverse Engineering Tools ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.yellow('Learn about reverse engineering techniques and binary analysis.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a reverse engineering tool:',
      choices: [
        'Binary Analysis',
        'Decompile Binary',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Binary Analysis':
      const { filePath } = await inquirer.prompt([
        {
          type: 'input',
          name: 'filePath',
          message: 'Enter path to binary file:',
          default: './samples/example.exe'
        }
      ]);
      
      try {
        const result = await analyzeBinary(filePath);
        
        console.log(chalk.green('\nBinary Analysis Results:'));
        console.log(`File Type: ${result.fileType}`);
        console.log(`Architecture: ${result.architecture}`);
        
        if (result.entryPoint) {
          console.log(`Entry Point: ${result.entryPoint}`);
        }
        
        if (result.sections && result.sections.length > 0) {
          console.log(chalk.yellow('\nSections:'));
          result.sections.forEach(section => {
            console.log(`- ${section.name}: Address ${section.address}, Size ${section.size}, Permissions ${section.permissions}`);
          });
        }
        
        if (result.imports && result.imports.length > 0) {
          console.log(chalk.yellow('\nImported Libraries/Functions:'));
          result.imports.forEach(imp => {
            console.log(`- ${imp}`);
          });
        }
        
        if (result.strings && result.strings.length > 0) {
          console.log(chalk.yellow('\nInteresting Strings:'));
          result.strings.forEach(str => {
            console.log(`- "${str}"`);
          });
        }
        
        if (result.vulnerabilities && result.vulnerabilities.length > 0) {
          console.log(chalk.red('\nPotential Vulnerabilities:'));
          result.vulnerabilities.forEach(vuln => {
            console.log(chalk.keyword(
              vuln.severity === 'Low' ? 'green' : 
              vuln.severity === 'Medium' ? 'yellow' : 
              vuln.severity === 'High' ? 'orange' : 'red'
            )(`- ${vuln.type} (${vuln.severity}): ${vuln.details}`));
          });
        }
        
        if (result.protections && result.protections.length > 0) {
          console.log(chalk.blue('\nSecurity Protections:'));
          result.protections.forEach(protection => {
            console.log(`- ${protection}`);
          });
        }
        
      } catch (error) {
        console.error(chalk.red('Error in analysis:'), error);
      }
      break;
      
    case 'Decompile Binary':
      const { binaryPath, targetLanguage } = await inquirer.prompt([
        {
          type: 'input',
          name: 'binaryPath',
          message: 'Enter path to binary file:',
          default: './samples/example.exe'
        },
        {
          type: 'list',
          name: 'targetLanguage',
          message: 'Select target decompilation language:',
          choices: ['C', 'Java', 'Python', 'Assembly']
        }
      ]);
      
      try {
        const decompiled = await decompileCode(binaryPath, targetLanguage);
        
        console.log(chalk.green('\nDecompiled Code:'));
        console.log(chalk.yellow('Note: This is simulated decompiled code for educational purposes only'));
        console.log(chalk.gray(decompiled));
        
        console.log(chalk.red('\nPotential Security Issues in Decompiled Code:'));
        console.log('- Look for hardcoded credentials or API keys');
        console.log('- Check for insecure function calls (strcpy, system, etc.)');
        console.log('- Identify input validation issues');
        console.log('- Examine error handling and exception management');
        console.log('- Review cryptographic implementations');
        
      } catch (error) {
        console.error(chalk.red('Error in decompilation:'), error);
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  console.log(chalk.red('\nIMPORTANT REMINDER:'));
  console.log(chalk.red('This is only a simulation for educational purposes.'));
  console.log(chalk.red('No actual reverse engineering was performed.'));
  
  // Ask if the user wants to perform another action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another reverse engineering action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

export default {
  menu,
  analyzeBinary,
  decompileCode
};
