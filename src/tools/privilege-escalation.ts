/**
 * Privilege Escalation Tools
 * 
 * This module simulates tools for identifying privilege escalation vulnerabilities.
 * For educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';

// Privilege escalation vulnerability types
interface PrivEscVulnerability {
  id: string;
  name: string;
  description: string;
  affectedSystems: string[];
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  detectionMethods: string[];
  exploitationDifficulty: 'Easy' | 'Medium' | 'Hard';
  mitigations: string[];
}

// Sample privilege escalation vulnerabilities for educational demonstration
const privEscVulnerabilities: PrivEscVulnerability[] = [
  {
    id: 'PE-001',
    name: 'Misconfigured SUID Binary',
    description: 'Executable files with the SUID bit set that can be leveraged to escalate privileges',
    affectedSystems: ['Linux', 'Unix'],
    severity: 'High',
    detectionMethods: [
      'Find SUID binaries: find / -perm -u=s -type f 2>/dev/null',
      'Check for unusual or custom SUID binaries',
      'Verify permissions and ownership'
    ],
    exploitationDifficulty: 'Easy',
    mitigations: [
      'Regularly audit SUID binaries',
      'Remove SUID bit from unnecessary binaries',
      'Restrict execution permissions',
      'Use capabilities instead of SUID where possible'
    ]
  },
  {
    id: 'PE-002',
    name: 'Kernel Exploit',
    description: 'Exploiting kernel vulnerabilities to gain elevated privileges',
    affectedSystems: ['Windows', 'Linux', 'macOS'],
    severity: 'Critical',
    detectionMethods: [
      'Check kernel version and patch level',
      'Monitor for unusual system calls',
      'Use vulnerability scanners to identify known kernel vulnerabilities'
    ],
    exploitationDifficulty: 'Hard',
    mitigations: [
      'Keep kernel and operating system updated',
      'Apply security patches promptly',
      'Implement kernel hardening measures',
      'Use security modules like SELinux or AppArmor'
    ]
  },
  {
    id: 'PE-003',
    name: 'Unquoted Service Path',
    description: 'Windows services with unquoted paths that can be exploited to run arbitrary executables',
    affectedSystems: ['Windows'],
    severity: 'Medium',
    detectionMethods: [
      'Check service paths: wmic service get name,pathname,startmode | findstr /i auto | findstr /i /v "C:\\Windows\\\\"',
      'Look for spaces in service paths without quotes',
      'Verify write permissions to service directories'
    ],
    exploitationDifficulty: 'Medium',
    mitigations: [
      'Use quotes around service paths',
      'Restrict write permissions to service directories',
      'Place services in locations without spaces in the path',
      'Use Windows Defender Application Control policies'
    ]
  },
  {
    id: 'PE-004',
    name: 'DLL Hijacking',
    description: 'Exploiting the Windows DLL search order to load malicious DLLs',
    affectedSystems: ['Windows'],
    severity: 'High',
    detectionMethods: [
      'Monitor for unusual DLL loading',
      'Check application directories for write permissions',
      'Use Process Monitor to identify DLL search order'
    ],
    exploitationDifficulty: 'Medium',
    mitigations: [
      'Use absolute paths when loading DLLs',
      'Implement proper DLL search order',
      'Restrict write permissions to application directories',
      'Use Windows Defender Application Control policies'
    ]
  },
  {
    id: 'PE-005',
    name: 'Sudo Rights / Misconfiguration',
    description: 'Exploiting overly permissive sudo configurations',
    affectedSystems: ['Linux', 'Unix', 'macOS'],
    severity: 'High',
    detectionMethods: [
      'Check sudo permissions: sudo -l',
      'Review sudoers file for misconfigurations',
      'Look for wildcards or dangerous allowed commands'
    ],
    exploitationDifficulty: 'Easy',
    mitigations: [
      'Follow principle of least privilege in sudo configurations',
      'Regularly audit sudo permissions',
      'Avoid wildcards in sudoers entries',
      'Use sudo command restrictions'
    ]
  },
  {
    id: 'PE-006',
    name: 'Weak Service Permissions',
    description: 'Windows services with weak permissions that allow modification',
    affectedSystems: ['Windows'],
    severity: 'High',
    detectionMethods: [
      'Check service permissions: accesschk.exe -uwcqv "Authenticated Users" *',
      'Review service binary permissions',
      'Check for services running as SYSTEM with weak permissions'
    ],
    exploitationDifficulty: 'Easy',
    mitigations: [
      'Restrict service permissions',
      'Run services with least privilege',
      'Protect service binaries and configurations',
      'Implement proper access controls'
    ]
  },
  {
    id: 'PE-007',
    name: 'Scheduled Task Abuse',
    description: 'Exploiting writable scheduled tasks or their target scripts/executables',
    affectedSystems: ['Windows', 'Linux', 'macOS'],
    severity: 'Medium',
    detectionMethods: [
      'Windows: schtasks /query /fo LIST /v',
      'Linux: check crontab entries and /etc/cron.* directories',
      'Check permissions on scheduled task targets'
    ],
    exploitationDifficulty: 'Medium',
    mitigations: [
      'Restrict permissions on scheduled tasks',
      'Protect task definitions and target executables',
      'Run scheduled tasks with least privilege',
      'Use absolute paths in task definitions'
    ]
  },
  {
    id: 'PE-008',
    name: 'Docker Escape',
    description: 'Escaping Docker containers to gain access to the host system',
    affectedSystems: ['Linux', 'Windows with Docker'],
    severity: 'Critical',
    detectionMethods: [
      'Check for containers running with privileged flag',
      'Look for mounted sensitive directories',
      'Check for exposed Docker socket'
    ],
    exploitationDifficulty: 'Hard',
    mitigations: [
      'Avoid running containers with --privileged flag',
      'Do not mount sensitive host directories',
      'Use user namespaces',
      'Implement proper seccomp and AppArmor profiles',
      'Keep Docker updated'
    ]
  }
];

// System configuration issues that can lead to privilege escalation
interface SystemConfigIssue {
  category: string;
  checks: {
    name: string;
    command: string;
    description: string;
    risk: 'Low' | 'Medium' | 'High' | 'Critical';
  }[];
}

// Sample system configuration checks for educational demonstration
const systemConfigChecks: SystemConfigIssue[] = [
  {
    category: 'Linux System Information',
    checks: [
      {
        name: 'Kernel Version',
        command: 'uname -a',
        description: 'Check kernel version for known vulnerabilities',
        risk: 'Medium'
      },
      {
        name: 'System Information',
        command: 'cat /etc/issue; cat /etc/*-release',
        description: 'Identify OS version and distribution',
        risk: 'Low'
      },
      {
        name: 'Mounted Filesystems',
        command: 'mount',
        description: 'Check for NFS shares or unusual mounts',
        risk: 'Medium'
      },
      {
        name: 'Environment Variables',
        command: 'env',
        description: 'Look for sensitive information in environment variables',
        risk: 'Medium'
      }
    ]
  },
  {
    category: 'Linux User & Group Information',
    checks: [
      {
        name: 'Current User',
        command: 'id',
        description: 'Check current user privileges and groups',
        risk: 'Low'
      },
      {
        name: 'Sudo Access',
        command: 'sudo -l',
        description: 'Check what commands the current user can run with sudo',
        risk: 'High'
      },
      {
        name: 'Users with Login',
        command: 'cat /etc/passwd | grep -v "nologin\\|false"',
        description: 'Identify users with valid login shells',
        risk: 'Medium'
      },
      {
        name: 'Superuser Accounts',
        command: 'grep -v -E "^#" /etc/passwd | awk -F: \'$3 == 0 { print $1 }\'',
        description: 'Find accounts with UID 0 (root privileges)',
        risk: 'Critical'
      }
    ]
  },
  {
    category: 'Linux Permissions & Sensitive Files',
    checks: [
      {
        name: 'SUID Binaries',
        command: 'find / -perm -u=s -type f 2>/dev/null',
        description: 'Find SUID binaries that can be used for privilege escalation',
        risk: 'High'
      },
      {
        name: 'SGID Binaries',
        command: 'find / -perm -g=s -type f 2>/dev/null',
        description: 'Find SGID binaries that can be used for privilege escalation',
        risk: 'High'
      },
      {
        name: 'World-Writable Files',
        command: 'find / -perm -2 -type f -not -path "/proc/*" 2>/dev/null',
        description: 'Find files writable by anyone',
        risk: 'High'
      },
      {
        name: 'World-Writable Directories',
        command: 'find / -perm -2 -type d -not -path "/proc/*" 2>/dev/null',
        description: 'Find directories writable by anyone',
        risk: 'High'
      }
    ]
  },
  {
    category: 'Windows System Information',
    checks: [
      {
        name: 'System Information',
        command: 'systeminfo',
        description: 'Get detailed system information including patches',
        risk: 'Medium'
      },
      {
        name: 'Installed Patches',
        command: 'wmic qfe get Caption,Description,HotFixID,InstalledOn',
        description: 'List installed patches and hotfixes',
        risk: 'Medium'
      },
      {
        name: 'Environment Variables',
        command: 'set',
        description: 'Check environment variables for sensitive information',
        risk: 'Medium'
      },
      {
        name: 'Network Configuration',
        command: 'ipconfig /all',
        description: 'View network configuration and connected networks',
        risk: 'Low'
      }
    ]
  },
  {
    category: 'Windows User & Privilege Information',
    checks: [
      {
        name: 'Current User',
        command: 'whoami /all',
        description: 'Show current user, groups, and privileges',
        risk: 'Low'
      },
      {
        name: 'Local Users',
        command: 'net user',
        description: 'List all local users',
        risk: 'Medium'
      },
      {
        name: 'Local Administrators',
        command: 'net localgroup Administrators',
        description: 'List members of the local Administrators group',
        risk: 'High'
      },
      {
        name: 'User Privileges',
        command: 'whoami /priv',
        description: 'Show current user privileges that can be exploited',
        risk: 'High'
      }
    ]
  },
  {
    category: 'Windows Services & Applications',
    checks: [
      {
        name: 'Running Services',
        command: 'tasklist /SVC',
        description: 'List running services and their associated processes',
        risk: 'Medium'
      },
      {
        name: 'Service Permissions',
        command: 'accesschk.exe -uwcqv "Authenticated Users" *',
        description: 'Check for services with weak permissions',
        risk: 'High'
      },
      {
        name: 'Unquoted Service Paths',
        command: 'wmic service get name,pathname,startmode | findstr /i auto | findstr /i /v "C:\\Windows\\\\"',
        description: 'Find services with unquoted paths',
        risk: 'High'
      },
      {
        name: 'Scheduled Tasks',
        command: 'schtasks /query /fo LIST /v',
        description: 'List scheduled tasks that might be exploitable',
        risk: 'Medium'
      }
    ]
  }
];

// Simulate system scan for privilege escalation vulnerabilities
async function scanSystem(osType: 'Windows' | 'Linux', scanLevel: 'Basic' | 'Advanced'): Promise<any> {
  const spinner = ora('Scanning system for privilege escalation vulnerabilities...').start();
  
  // This is a simulation - we're not actually scanning the system
  return new Promise(resolve => {
    // Simulate processing time based on scan level
    const processingTime = scanLevel === 'Basic' ? 3000 : 6000;
    
    setTimeout(() => {
      spinner.text = 'Checking system configuration...';
      
      setTimeout(() => {
        spinner.text = 'Analyzing permissions...';
        
        setTimeout(() => {
          spinner.text = 'Identifying potential vulnerabilities...';
          
          setTimeout(() => {
            spinner.succeed('System scan complete');
            
            // Generate simulated results
            const results = {
              osType,
              scanLevel,
              scanTime: `${((processingTime + Math.random() * 3000) / 1000).toFixed(1)} seconds`,
              systemInfo: {
                osVersion: osType === 'Windows' ? 'Windows 10 Pro 21H2' : 'Ubuntu 20.04 LTS',
                kernelVersion: osType === 'Windows' ? '10.0.19044' : '5.4.0-109-generic',
                architecture: 'x86_64',
                hostname: 'test-system',
                currentUser: 'testuser',
                userGroups: osType === 'Windows' ? ['Users', 'Remote Desktop Users'] : ['testuser', 'docker']
              },
              vulnerabilities: [] as any[],
              configIssues: [] as any[],
              overallRisk: 'Medium'
            };
            
            // Select vulnerabilities based on OS type
            const applicableVulns = privEscVulnerabilities.filter(vuln => 
              vuln.affectedSystems.includes(osType)
            );
            
            // For basic scan, find fewer vulnerabilities
            const vulnCount = scanLevel === 'Basic' ? 
              Math.floor(Math.random() * 2) + 1 : // 1-2 vulnerabilities for basic scan
              Math.floor(Math.random() * 3) + 2;  // 2-4 vulnerabilities for advanced scan
            
            // Randomly select vulnerabilities
            for (let i = 0; i < vulnCount && applicableVulns.length > 0; i++) {
              const index = Math.floor(Math.random() * applicableVulns.length);
              const vuln = applicableVulns[index];
              
              // Add vulnerability with detection details
              results.vulnerabilities.push({
                id: vuln.id,
                name: vuln.name,
                description: vuln.description,
                severity: vuln.severity,
                details: `Detected potential ${vuln.name} vulnerability`,
                exploitationRisk: vuln.exploitationDifficulty === 'Easy' ? 'High' : 
                                 vuln.exploitationDifficulty === 'Medium' ? 'Medium' : 'Low',
                mitigations: vuln.mitigations
              });
              
              // Remove from available vulnerabilities to avoid duplicates
              applicableVulns.splice(index, 1);
            }
            
            // Select configuration issues based on OS type
            const applicableChecks = systemConfigChecks.filter(check => 
              check.category.includes(osType)
            );
            
            // For each applicable category
            applicableChecks.forEach(category => {
              // For basic scan, find fewer issues
              const issueCount = scanLevel === 'Basic' ? 
                Math.floor(Math.random() * 2) + 1 : // 1-2 issues per category for basic scan
                Math.floor(Math.random() * 3) + 1;  // 1-3 issues per category for advanced scan
              
              // Randomly select issues
              const selectedChecks = [];
              const availableChecks = [...category.checks];
              
              for (let i = 0; i < issueCount && availableChecks.length > 0; i++) {
                const index = Math.floor(Math.random() * availableChecks.length);
                selectedChecks.push(availableChecks[index]);
                availableChecks.splice(index, 1);
              }
              
              // Add configuration issues
              if (selectedChecks.length > 0) {
                results.configIssues.push({
                  category: category.category,
                  issues: selectedChecks.map(check => ({
                    name: check.name,
                    description: check.description,
                    risk: check.risk,
                    command: check.command,
                    finding: generateFinding(check.name, osType)
                  }))
                });
              }
            });
            
            // Determine overall risk based on findings
            if (results.vulnerabilities.some(v => v.severity === 'Critical')) {
              results.overallRisk = 'Critical';
            } else if (results.vulnerabilities.some(v => v.severity === 'High')) {
              results.overallRisk = 'High';
            }
            
            resolve(results);
          }, 2000 + Math.random() * 1000);
        }, 2000 + Math.random() * 1000);
      }, 2000 + Math.random() * 1000);
    }, processingTime); // Simulate processing time
  });
}

// Generate simulated finding for a check
function generateFinding(checkName: string, osType: 'Windows' | 'Linux'): string {
  switch (checkName) {
    case 'Kernel Version':
      return osType === 'Windows' ? 
        'Windows 10 Kernel Version 10.0.19044 (no known exploitable vulnerabilities found)' : 
        'Linux Kernel 5.4.0-109-generic (potentially vulnerable to CVE-2022-0847 "Dirty Pipe")';
    case 'SUID Binaries':
      return '/usr/bin/sudo\n/usr/bin/pkexec\n/usr/bin/passwd\n/usr/bin/gpasswd\n/usr/bin/newgrp\n/usr/bin/chsh\n/usr/bin/chfn\n/usr/bin/mount\n/usr/bin/su\n/usr/bin/umount\n/usr/bin/fusermount\n/usr/bin/ntfs-3g\n/usr/bin/pppd';
    case 'Sudo Access':
      return 'User testuser may run the following commands on test-system:\n    (ALL : ALL) /usr/bin/less\n    (ALL : ALL) /usr/bin/vim\n    (ALL : ALL) NOPASSWD: /usr/bin/find';
    case 'World-Writable Files':
      return '/var/www/html/config.php\n/var/log/test.log\n/tmp/cleanup.sh';
    case 'Unquoted Service Paths':
      return 'CustomApp Service C:\\Program Files\\Custom App\\custom service.exe (Start: Auto)\nBackup Manager C:\\Program Files\\Backup Manager\\backup service.exe (Start: Auto)';
    case 'Service Permissions':
      return 'SERVICE_CHANGE_CONFIG: BackupService\nSERVICE_ALL_ACCESS: CustomUpdateService';
    case 'Local Administrators':
      return 'Administrator\ntestuser\nHelpDesk';
    case 'User Privileges':
      return 'SeImpersonatePrivilege\nSeLoadDriverPrivilege\nSeTakeOwnershipPrivilege';
    default:
      return 'No significant findings';
  }
}

// Display the menu for privilege escalation tools
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== Privilege Escalation Tools ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.yellow('Learn about privilege escalation vulnerabilities and how to identify them.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a privilege escalation tool:',
      choices: [
        'Scan System for Vulnerabilities',
        'View Privilege Escalation Techniques',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'Scan System for Vulnerabilities':
      const { osType, scanLevel } = await inquirer.prompt([
        {
          type: 'list',
          name: 'osType',
          message: 'Select target operating system:',
          choices: ['Windows', 'Linux']
        },
        {
          type: 'list',
          name: 'scanLevel',
          message: 'Select scan level:',
          choices: ['Basic', 'Advanced']
        }
      ]);
      
      const { confirmScan } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmScan',
          message: 'This will simulate a privilege escalation vulnerability scan. Continue?',
          default: true
        }
      ]);
      
      if (confirmScan) {
        try {
          const results = await scanSystem(osType, scanLevel);
          
          console.log(chalk.green('\nSystem Scan Results:'));
          console.log(`Operating System: ${results.systemInfo.osVersion}`);
          console.log(`Kernel Version: ${results.systemInfo.kernelVersion}`);
          console.log(`Scan Time: ${results.scanTime}`);
          console.log(`Overall Risk: ${chalk.keyword(
            results.overallRisk === 'Low' ? 'green' : 
            results.overallRisk === 'Medium' ? 'yellow' : 
            results.overallRisk === 'High' ? 'orange' : 'red'
          )(results.overallRisk)}`);
          
          if (results.vulnerabilities.length > 0) {
            console.log(chalk.red(`\nDetected ${results.vulnerabilities.length} potential privilege escalation vulnerabilities:`));
            
            // Group by severity
            const bySeverity = {
              'Critical': results.vulnerabilities.filter(v => v.severity === 'Critical'),
              'High': results.vulnerabilities.filter(v => v.severity === 'High'),
              'Medium': results.vulnerabilities.filter(v => v.severity === 'Medium'),
              'Low': results.vulnerabilities.filter(v => v.severity === 'Low')
            };
            
            ['Critical', 'High', 'Medium', 'Low'].forEach(severity => {
              const vulns = bySeverity[severity];
              if (vulns.length > 0) {
                console.log(chalk.keyword(
                  severity === 'Low' ? 'green' : 
                  severity === 'Medium' ? 'yellow' : 
                  severity === 'High' ? 'orange' : 'red'
                )(`\n${severity} Severity Vulnerabilities:`));
                
                vulns.forEach((vuln: any) => {
                  console.log(chalk.blue(`\n  ${vuln.id}: ${vuln.name}`));
                  console.log(`  ${vuln.description}`);
                  console.log(`  Details: ${vuln.details}`);
                  console.log(`  Exploitation Risk: ${vuln.exploitationRisk}`);
                  
                  console.log(chalk.green('\n  Mitigations:'));
                  vuln.mitigations.forEach((mitigation: string, index: number) => {
                    console.log(`  ${index + 1}. ${mitigation}`);
                  });
                });
              }
            });
          } else {
            console.log(chalk.green('\nNo privilege escalation vulnerabilities detected.'));
          }
          
          if (results.configIssues.length > 0) {
            console.log(chalk.yellow('\nSystem Configuration Issues:'));
            
            results.configIssues.forEach((category: any) => {
              console.log(chalk.blue(`\n${category.category}:`));
              
              category.issues.forEach((issue: any) => {
                console.log(chalk.keyword(
                  issue.risk === 'Low' ? 'green' : 
                  issue.risk === 'Medium' ? 'yellow' : 
                  issue.risk === 'High' ? 'orange' : 'red'
                )(`\n  ${issue.name} (${issue.risk} Risk):`));
                console.log(`  ${issue.description}`);
                console.log(`  Command: ${chalk.gray(issue.command)}`);
                console.log(chalk.yellow('\n  Finding:'));
                console.log(`  ${chalk.gray(issue.finding)}`);
              });
            });
          }
          
        } catch (error) {
          console.error(chalk.red('Error in system scan:'), error);
        }
      } else {
        console.log(chalk.yellow('System scan cancelled.'));
      }
      break;
      
    case 'View Privilege Escalation Techniques':
      console.log(chalk.yellow('\nPrivilege Escalation Techniques:'));
      
      // Group by affected systems
      const bySystem = {
        'Windows': privEscVulnerabilities.filter(v => v.affectedSystems.includes('Windows')),
        'Linux/Unix': privEscVulnerabilities.filter(v => v.affectedSystems.includes('Linux') || v.affectedSystems.includes('Unix')),
        'Cross-Platform': privEscVulnerabilities.filter(v => 
          v.affectedSystems.includes('Windows') && 
          (v.affectedSystems.includes('Linux') || v.affectedSystems.includes('Unix'))
        )
      };
      
      ['Windows', 'Linux/Unix', 'Cross-Platform'].forEach(system => {
        const vulns = bySystem[system];
        if (vulns.length > 0) {
          console.log(chalk.blue(`\n${system} Privilege Escalation Techniques:`));
          
          vulns.forEach(vuln => {
            console.log(chalk.keyword(
              vuln.severity === 'Low' ? 'green' : 
              vuln.severity === 'Medium' ? 'yellow' : 
              vuln.severity === 'High' ? 'orange' : 'red'
            )(`\n  ${vuln.id}: ${vuln.name} (${vuln.severity})`));
            console.log(`  ${vuln.description}`);
            console.log(`  Exploitation Difficulty: ${vuln.exploitationDifficulty}`);
            
            console.log(chalk.yellow('\n  Detection Methods:'));
            vuln.detectionMethods.forEach((method, index) => {
              console.log(`  ${index + 1}. ${method}`);
            });
            
            console.log(chalk.green('\n  Mitigations:'));
            vuln.mitigations.forEach((mitigation, index) => {
              console.log(`  ${index + 1}. ${mitigation}`);
            });
          });
        }
      });
      break;
      
    case 'Back to main menu':
      return;
  }
  
  console.log(chalk.red('\nIMPORTANT REMINDER:'));
  console.log(chalk.red('This is only a simulation for educational purposes.'));
  console.log(chalk.red('Always obtain proper authorization before testing for privilege escalation vulnerabilities.'));
  
  // Ask if the user wants to perform another action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another privilege escalation action?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

export default {
  menu,
  scanSystem
};
