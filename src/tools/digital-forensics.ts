/**
 * Digital Forensics Tools
 * 
 * This module simulates tools for digital forensics investigation.
 * For educational purposes ONLY.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import fs from 'fs';
import path from 'path';

// File types for forensic analysis
interface ForensicFileInfo {
  fileName: string;
  fileType: string;
  fileSize: string;
  created: string;
  modified: string;
  accessed: string;
  md5Hash: string;
  sha256Hash: string;
  deleted: boolean;
  hidden: boolean;
  metadata?: any;
}

// Memory analysis result
interface MemoryAnalysisResult {
  runningProcesses: any[];
  networkConnections: any[];
  loadedModules: any[];
  suspiciousIndicators: any[];
  extractedStrings: string[];
  timeline: any[];
}

// Disk image analysis result
interface DiskAnalysisResult {
  partitions: any[];
  fileSystem: string;
  recoveredFiles: ForensicFileInfo[];
  deletedFiles: ForensicFileInfo[];
  suspiciousFiles: ForensicFileInfo[];
  timeline: any[];
}

// Simulate file forensic analysis
async function analyzeFile(filePath: string): Promise<ForensicFileInfo> {
  const spinner = ora('Performing forensic analysis on file...').start();
  
  // This is a simulation - we're not actually analyzing real files
  return new Promise((resolve, reject) => {
    // Simulate processing time
    setTimeout(() => {
      // Check if file exists (for realism)
      if (!fs.existsSync(filePath)) {
        spinner.fail('File not found');
        reject(new Error('File not found'));
        return;
      }
      
      spinner.succeed('File forensic analysis complete');
      
      // Get file stats for realism
      const stats = fs.statSync(filePath);
      const ext = path.extname(filePath).toLowerCase();
      
      // Generate simulated results
      const result: ForensicFileInfo = {
        fileName: path.basename(filePath),
        fileType: getFileType(ext),
        fileSize: formatFileSize(stats.size),
        created: stats.birthtime.toISOString(),
        modified: stats.mtime.toISOString(),
        accessed: stats.atime.toISOString(),
        md5Hash: generateFakeHash('md5'),
        sha256Hash: generateFakeHash('sha256'),
        deleted: false,
        hidden: path.basename(filePath).startsWith('.')
      };
      
      // Add metadata based on file type
      if (ext === '.jpg' || ext === '.jpeg' || ext === '.png') {
        result.metadata = {
          dimensions: '1920x1080',
          camera: 'iPhone 12 Pro',
          location: 'Latitude: 37.7749, Longitude: -122.4194',
          takenOn: '2023-01-15T14:32:10Z'
        };
      } else if (ext === '.pdf') {
        result.metadata = {
          author: 'John Smith',
          creator: 'Adobe Acrobat Pro',
          created: '2023-02-10T09:15:22Z',
          modified: '2023-02-12T16:45:30Z',
          pages: 15
        };
      } else if (ext === '.docx' || ext === '.xlsx') {
        result.metadata = {
          author: 'Jane Doe',
          company: 'Example Corp',
          created: '2023-03-05T11:22:33Z',
          lastModifiedBy: 'Mike Johnson',
          revisions: 8
        };
      }
      
      resolve(result);
    }, 2000 + Math.random() * 1000); // Simulate processing time
  });
}

// Simulate memory dump analysis
async function analyzeMemoryDump(filePath: string): Promise<MemoryAnalysisResult> {
  const spinner = ora('Analyzing memory dump...').start();
  
  // This is a simulation - we're not actually analyzing real memory dumps
  return new Promise((resolve, reject) => {
    // Simulate processing time
    setTimeout(() => {
      // Check if file exists (for realism)
      if (!fs.existsSync(filePath)) {
        spinner.fail('Memory dump file not found');
        reject(new Error('File not found'));
        return;
      }
      
      spinner.succeed('Memory analysis complete');
      
      // Generate simulated results
      const result: MemoryAnalysisResult = {
        runningProcesses: [
          { pid: 1234, name: 'explorer.exe', path: 'C:\\Windows\\explorer.exe', user: 'SYSTEM', memory: '45 MB' },
          { pid: 2345, name: 'chrome.exe', path: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', user: 'User', memory: '250 MB' },
          { pid: 3456, name: 'svchost.exe', path: 'C:\\Windows\\System32\\svchost.exe', user: 'SYSTEM', memory: '32 MB' },
          { pid: 4567, name: 'suspicious.exe', path: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\suspicious.exe', user: 'User', memory: '15 MB', suspicious: true }
        ],
        networkConnections: [
          { process: 'chrome.exe', pid: 2345, localAddr: '***********:54321', remoteAddr: '*************:443', state: 'ESTABLISHED', protocol: 'TCP' },
          { process: 'suspicious.exe', pid: 4567, localAddr: '***********:55555', remoteAddr: '*************:8080', state: 'ESTABLISHED', protocol: 'TCP', suspicious: true }
        ],
        loadedModules: [
          { process: 'explorer.exe', module: 'ntdll.dll', path: 'C:\\Windows\\System32\\ntdll.dll' },
          { process: 'explorer.exe', module: 'kernel32.dll', path: 'C:\\Windows\\System32\\kernel32.dll' },
          { process: 'suspicious.exe', module: 'crypto.dll', path: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\crypto.dll', suspicious: true }
        ],
        suspiciousIndicators: [
          { type: 'Process Injection', details: 'Evidence of code injection into explorer.exe', severity: 'High' },
          { type: 'Keylogger', details: 'Keyboard hook detected in suspicious.exe', severity: 'Critical' },
          { type: 'Encrypted Communication', details: 'Suspicious encrypted traffic from unknown process', severity: 'Medium' }
        ],
        extractedStrings: [
          'C:\\Users\\<USER>\\Documents\\passwords.txt',
          'https://malicious-command-server.example.com/report',
          'SELECT * FROM users WHERE username=',
          'AES-256-CBC',
          'GET /exfil.php?data=',
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
        ],
        timeline: [
          { time: '2023-06-10T14:32:10Z', event: 'suspicious.exe process started', details: 'Executed from temporary directory' },
          { time: '2023-06-10T14:32:15Z', event: 'Network connection established', details: 'Connection to *************:8080' },
          { time: '2023-06-10T14:35:22Z', event: 'File access', details: 'Reading C:\\Users\\<USER>\\Documents\\passwords.txt' },
          { time: '2023-06-10T14:36:05Z', event: 'Process injection detected', details: 'Code injected into explorer.exe' },
          { time: '2023-06-10T14:40:12Z', event: 'Data exfiltration', details: 'Data sent to remote server' }
        ]
      };
      
      resolve(result);
    }, 5000 + Math.random() * 3000); // Simulate processing time
  });
}

// Simulate disk image analysis
async function analyzeDiskImage(filePath: string): Promise<DiskAnalysisResult> {
  const spinner = ora('Analyzing disk image...').start();
  
  // This is a simulation - we're not actually analyzing real disk images
  return new Promise((resolve, reject) => {
    // Simulate processing time
    setTimeout(() => {
      // Check if file exists (for realism)
      if (!fs.existsSync(filePath)) {
        spinner.fail('Disk image file not found');
        reject(new Error('File not found'));
        return;
      }
      
      spinner.text = 'Analyzing partition structure...';
      
      setTimeout(() => {
        spinner.text = 'Recovering deleted files...';
        
        setTimeout(() => {
          spinner.text = 'Analyzing file system artifacts...';
          
          setTimeout(() => {
            spinner.succeed('Disk image analysis complete');
            
            // Generate simulated results
            const result: DiskAnalysisResult = {
              partitions: [
                { number: 1, type: 'EFI System Partition', size: '100 MB', offset: '1 MB' },
                { number: 2, type: 'Microsoft Reserved', size: '16 MB', offset: '101 MB' },
                { number: 3, type: 'Basic Data Partition', size: '500 GB', offset: '117 MB', fileSystem: 'NTFS' }
              ],
              fileSystem: 'NTFS',
              recoveredFiles: [
                {
                  fileName: 'budget_2023.xlsx',
                  fileType: 'Microsoft Excel Spreadsheet',
                  fileSize: '2.5 MB',
                  created: '2023-01-15T10:23:45Z',
                  modified: '2023-03-20T14:32:10Z',
                  accessed: '2023-04-02T09:15:30Z',
                  md5Hash: generateFakeHash('md5'),
                  sha256Hash: generateFakeHash('sha256'),
                  deleted: false,
                  hidden: false
                },
                {
                  fileName: 'confidential_report.pdf',
                  fileType: 'Adobe PDF Document',
                  fileSize: '5.2 MB',
                  created: '2023-02-10T16:42:15Z',
                  modified: '2023-02-10T16:42:15Z',
                  accessed: '2023-04-01T11:22:33Z',
                  md5Hash: generateFakeHash('md5'),
                  sha256Hash: generateFakeHash('sha256'),
                  deleted: false,
                  hidden: false
                }
              ],
              deletedFiles: [
                {
                  fileName: 'passwords.txt',
                  fileType: 'Text Document',
                  fileSize: '1.2 KB',
                  created: '2023-03-15T08:12:45Z',
                  modified: '2023-03-25T19:42:10Z',
                  accessed: '2023-04-01T20:15:30Z',
                  md5Hash: generateFakeHash('md5'),
                  sha256Hash: generateFakeHash('sha256'),
                  deleted: true,
                  hidden: false
                },
                {
                  fileName: 'evidence_cleanup.bat',
                  fileType: 'Windows Batch File',
                  fileSize: '4.5 KB',
                  created: '2023-04-01T20:10:15Z',
                  modified: '2023-04-01T20:10:15Z',
                  accessed: '2023-04-01T20:15:30Z',
                  md5Hash: generateFakeHash('md5'),
                  sha256Hash: generateFakeHash('sha256'),
                  deleted: true,
                  hidden: false
                }
              ],
              suspiciousFiles: [
                {
                  fileName: 'svchost_backup.exe',
                  fileType: 'Windows Executable',
                  fileSize: '250 KB',
                  created: '2023-04-01T18:45:22Z',
                  modified: '2023-04-01T18:45:22Z',
                  accessed: '2023-04-01T20:15:30Z',
                  md5Hash: generateFakeHash('md5'),
                  sha256Hash: generateFakeHash('sha256'),
                  deleted: false,
                  hidden: true
                },
                {
                  fileName: '.hidden_keylogger.dll',
                  fileType: 'Windows Dynamic Link Library',
                  fileSize: '125 KB',
                  created: '2023-04-01T18:46:30Z',
                  modified: '2023-04-01T18:46:30Z',
                  accessed: '2023-04-01T20:15:30Z',
                  md5Hash: generateFakeHash('md5'),
                  sha256Hash: generateFakeHash('sha256'),
                  deleted: false,
                  hidden: true
                }
              ],
              timeline: [
                { time: '2023-04-01T18:45:22Z', event: 'Suspicious file created', details: 'svchost_backup.exe created in system directory' },
                { time: '2023-04-01T18:46:30Z', event: 'Hidden file created', details: '.hidden_keylogger.dll created' },
                { time: '2023-04-01T19:15:45Z', event: 'System file modified', details: 'Registry modification to HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run' },
                { time: '2023-04-01T20:10:15Z', event: 'Cleanup script created', details: 'evidence_cleanup.bat created' },
                { time: '2023-04-01T20:15:30Z', event: 'Cleanup script executed', details: 'evidence_cleanup.bat executed and deleted files' }
              ]
            };
            
            resolve(result);
          }, 2000 + Math.random() * 1000);
        }, 2000 + Math.random() * 1000);
      }, 2000 + Math.random() * 1000);
    }, 2000 + Math.random() * 1000); // Simulate processing time
  });
}

// Helper function to get file type from extension
function getFileType(extension: string): string {
  const types: {[key: string]: string} = {
    '.txt': 'Text Document',
    '.pdf': 'Adobe PDF Document',
    '.docx': 'Microsoft Word Document',
    '.xlsx': 'Microsoft Excel Spreadsheet',
    '.pptx': 'Microsoft PowerPoint Presentation',
    '.jpg': 'JPEG Image',
    '.jpeg': 'JPEG Image',
    '.png': 'PNG Image',
    '.gif': 'GIF Image',
    '.mp3': 'MP3 Audio',
    '.mp4': 'MP4 Video',
    '.zip': 'ZIP Archive',
    '.exe': 'Windows Executable',
    '.dll': 'Windows Dynamic Link Library',
    '.bat': 'Windows Batch File',
    '.sh': 'Shell Script',
    '.html': 'HTML Document',
    '.css': 'CSS Stylesheet',
    '.js': 'JavaScript File'
  };
  
  return types[extension] || 'Unknown File Type';
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
}

// Helper function to generate fake hash
function generateFakeHash(type: 'md5' | 'sha256'): string {
  const chars = '0123456789abcdef';
  let hash = '';
  const length = type === 'md5' ? 32 : 64;
  
  for (let i = 0; i < length; i++) {
    hash += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return hash;
}

// Display the menu for digital forensics tools
async function menu(): Promise<void> {
  console.log(chalk.cyan('\n=== Digital Forensics Tools ===\n'));
  console.log(chalk.red('IMPORTANT: This is an educational simulation only.'));
  console.log(chalk.yellow('Learn about digital forensics techniques and analysis.'));
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'Select a forensics tool:',
      choices: [
        'File Analysis',
        'Memory Dump Analysis',
        'Disk Image Analysis',
        'Back to main menu'
      ]
    }
  ]);
  
  switch (action) {
    case 'File Analysis':
      const { filePath } = await inquirer.prompt([
        {
          type: 'input',
          name: 'filePath',
          message: 'Enter path to file:',
          default: './samples/evidence.pdf'
        }
      ]);
      
      try {
        const result = await analyzeFile(filePath);
        
        console.log(chalk.green('\nFile Forensic Analysis Results:'));
        console.log(`File Name: ${result.fileName}`);
        console.log(`File Type: ${result.fileType}`);
        console.log(`File Size: ${result.fileSize}`);
        console.log(`Created: ${result.created}`);
        console.log(`Modified: ${result.modified}`);
        console.log(`Accessed: ${result.accessed}`);
        console.log(`MD5 Hash: ${result.md5Hash}`);
        console.log(`SHA256 Hash: ${result.sha256Hash}`);
        console.log(`Deleted: ${result.deleted ? 'Yes' : 'No'}`);
        console.log(`Hidden: ${result.hidden ? 'Yes' : 'No'}`);
        
        if (result.metadata) {
          console.log(chalk.yellow('\nFile Metadata:'));
          Object.entries(result.metadata).forEach(([key, value]) => {
            console.log(`${key}: ${value}`);
          });
        }
        
      } catch (error) {
        console.error(chalk.red('Error in analysis:'), error);
      }
      break;
      
    case 'Memory Dump Analysis':
      const { memoryDumpPath } = await inquirer.prompt([
        {
          type: 'input',
          name: 'memoryDumpPath',
          message: 'Enter path to memory dump file:',
          default: './samples/memory.dmp'
        }
      ]);
      
      try {
        const result = await analyzeMemoryDump(memoryDumpPath);
        
        console.log(chalk.green('\nMemory Analysis Results:'));
        
        console.log(chalk.yellow('\nRunning Processes:'));
        result.runningProcesses.forEach(process => {
          console.log(chalk.keyword(process.suspicious ? 'red' : 'blue')(`- ${process.name} (PID: ${process.pid})`));
          console.log(`  Path: ${process.path}`);
          console.log(`  User: ${process.user}`);
          console.log(`  Memory: ${process.memory}`);
          if (process.suspicious) {
            console.log(chalk.red('  [SUSPICIOUS PROCESS]'));
          }
        });
        
        console.log(chalk.yellow('\nNetwork Connections:'));
        result.networkConnections.forEach(conn => {
          console.log(chalk.keyword(conn.suspicious ? 'red' : 'blue')(`- ${conn.process} (PID: ${conn.pid})`));
          console.log(`  Local: ${conn.localAddr}`);
          console.log(`  Remote: ${conn.remoteAddr}`);
          console.log(`  State: ${conn.state}`);
          console.log(`  Protocol: ${conn.protocol}`);
          if (conn.suspicious) {
            console.log(chalk.red('  [SUSPICIOUS CONNECTION]'));
          }
        });
        
        console.log(chalk.yellow('\nLoaded Modules:'));
        result.loadedModules.forEach(module => {
          console.log(chalk.keyword(module.suspicious ? 'red' : 'blue')(`- ${module.process}: ${module.module}`));
          console.log(`  Path: ${module.path}`);
          if (module.suspicious) {
            console.log(chalk.red('  [SUSPICIOUS MODULE]'));
          }
        });
        
        console.log(chalk.red('\nSuspicious Indicators:'));
        result.suspiciousIndicators.forEach(indicator => {
          console.log(chalk.keyword(
            indicator.severity === 'Low' ? 'green' : 
            indicator.severity === 'Medium' ? 'yellow' : 
            indicator.severity === 'High' ? 'orange' : 'red'
          )(`- ${indicator.type} (${indicator.severity}): ${indicator.details}`));
        });
        
        console.log(chalk.yellow('\nExtracted Strings of Interest:'));
        result.extractedStrings.forEach(str => {
          console.log(`- "${str}"`);
        });
        
        console.log(chalk.blue('\nEvent Timeline:'));
        result.timeline.forEach(event => {
          console.log(`- ${event.time}: ${event.event}`);
          console.log(`  ${event.details}`);
        });
        
      } catch (error) {
        console.error(chalk.red('Error in analysis:'), error);
      }
      break;
      
    case 'Disk Image Analysis':
      const { diskImagePath } = await inquirer.prompt([
        {
          type: 'input',
          name: 'diskImagePath',
          message: 'Enter path to disk image file:',
          default: './samples/disk.img'
        }
      ]);
      
      try {
        const result = await analyzeDiskImage(diskImagePath);
        
        console.log(chalk.green('\nDisk Image Analysis Results:'));
        console.log(`File System: ${result.fileSystem}`);
        
        console.log(chalk.yellow('\nPartitions:'));
        result.partitions.forEach(partition => {
          console.log(`- Partition ${partition.number}: ${partition.type}`);
          console.log(`  Size: ${partition.size}`);
          console.log(`  Offset: ${partition.offset}`);
          if (partition.fileSystem) {
            console.log(`  File System: ${partition.fileSystem}`);
          }
        });
        
        console.log(chalk.blue('\nRecovered Files:'));
        result.recoveredFiles.forEach(file => {
          console.log(`- ${file.fileName} (${file.fileType})`);
          console.log(`  Size: ${file.fileSize}`);
          console.log(`  Created: ${file.created}`);
          console.log(`  Modified: ${file.modified}`);
          console.log(`  MD5: ${file.md5Hash}`);
        });
        
        console.log(chalk.red('\nDeleted Files (Recovered):'));
        result.deletedFiles.forEach(file => {
          console.log(`- ${file.fileName} (${file.fileType})`);
          console.log(`  Size: ${file.fileSize}`);
          console.log(`  Created: ${file.created}`);
          console.log(`  Modified: ${file.modified}`);
          console.log(`  MD5: ${file.md5Hash}`);
        });
        
        console.log(chalk.red('\nSuspicious Files:'));
        result.suspiciousFiles.forEach(file => {
          console.log(`- ${file.fileName} (${file.fileType})`);
          console.log(`  Size: ${file.fileSize}`);
          console.log(`  Created: ${file.created}`);
          console.log(`  Hidden: ${file.hidden ? 'Yes' : 'No'}`);
          console.log(`  MD5: ${file.md5Hash}`);
        });
        
        console.log(chalk.blue('\nEvent Timeline:'));
        result.timeline.forEach(event => {
          console.log(`- ${event.time}: ${event.event}`);
          console.log(`  ${event.details}`);
        });
        
      } catch (error) {
        console.error(chalk.red('Error in analysis:'), error);
      }
      break;
      
    case 'Back to main menu':
      return;
  }
  
  console.log(chalk.red('\nIMPORTANT REMINDER:'));
  console.log(chalk.red('This is only a simulation for educational purposes.'));
  console.log(chalk.red('No actual forensic analysis was performed.'));
  
  // Ask if the user wants to perform another action
  const { anotherAction } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'anotherAction',
      message: 'Would you like to perform another forensic analysis?',
      default: true
    }
  ]);
  
  if (anotherAction) {
    await menu();
  }
}

export default {
  menu,
  analyzeFile,
  analyzeMemoryDump,
  analyzeDiskImage
};
